import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AddPieceDialogComponent } from './add-piece-dialog.component';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';

describe('AddPieceDialogComponent', () => {
	let component: AddPieceDialogComponent;
	let fixture: ComponentFixture<AddPieceDialogComponent>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<AddPieceDialogComponent>>;

	beforeEach(async () => {
		mockDialogRef = jasmine.createSpyObj<MatDialogRef<AddPieceDialogComponent>>('MatDialogRef', ['close']);
		await TestBed.configureTestingModule({
			imports: [AddPieceDialogComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: {} },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(AddPieceDialogComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should set submitted to true when called', () => {
		component.onNext();
		expect(component.submitted).toBe(true);
	});

	it('should return early if form is invalid', () => {
		// Mock form to be invalid
		component.pieceTypeForm = {
			invalid: true,
			value: { pieceType: '' },
		} as any;

		component.onNext();
		expect(mockDialogRef.close).not.toHaveBeenCalled();
		expect(component.submitted).toBe(true);
	});

	it('should set submitted back to false when form is valid', () => {
		// Mock form to be valid
		component.pieceTypeForm = {
			invalid: false,
			value: { pieceType: 'test' },
		} as any;

		component.onNext();
		expect(component.submitted).toBe(false);
	});

	it('should close dialog with pieceType value when form is valid', () => {
		const testValue = 'test-value';
		// Mock form to be valid
		component.pieceTypeForm = {
			invalid: false,
			value: { pieceType: testValue },
		} as any;

		component.onNext();
		expect(mockDialogRef.close).toHaveBeenCalledWith(testValue);
	});

	it('should handle empty string as valid pieceType if form is valid', () => {
		// Mock form to be valid with empty string
		component.pieceTypeForm = {
			invalid: false,
			value: { pieceType: '' },
		} as any;

		component.onNext();
		expect(mockDialogRef.close).toHaveBeenCalledWith('');
	});

	it('should call dialogRef.close when onCancel is called', () => {
		component.onCancel();
		expect(mockDialogRef.close).toHaveBeenCalled();
	});

	it('should call dialogRef.close with no arguments', () => {
		component.onCancel();
		expect(mockDialogRef.close).toHaveBeenCalledWith();
	});
});
