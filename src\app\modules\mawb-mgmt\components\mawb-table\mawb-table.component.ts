import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MatPaginatorIntl, MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { CustomPaginatorIntl } from '@shared/services/custom-paginator-intl.service';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatSortModule, Sort } from '@angular/material/sort';
import { Router } from '@angular/router';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { MawbListObject } from '../../models/mawb-list-object.model';
import { Modules, UserPermission } from '@shared/models/user-role.model';
import { AsyncPipe } from '@angular/common';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
	selector: 'orll-mawb-table',
	templateUrl: './mawb-table.component.html',
	styleUrls: ['./mawb-table.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatTableModule, MatSortModule, MatButtonModule, MatMenuModule, MatIconModule, MatPaginatorModule, TranslateModule, AsyncPipe],
	providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }],
})
export class MawbTableComponent extends RolesAwareComponent implements OnChanges, OnInit {
	@Input() records: MawbListObject[] = [];
	@Input() totalRecords = 0;
	@Input() pageParams!: PaginationRequest;

	@Output() shareMawb: EventEmitter<MawbListObject> = new EventEmitter<MawbListObject>();
	@Output() sortChange = new EventEmitter<Sort>();
	@Output() pagination = new EventEmitter<PageEvent & { sortField?: string; sortDirection?: string }>();

	currentSort: Sort = { active: '', direction: '' };

	readonly displayedColumns: string[] = [
		'mawbNumber',
		'airlineCode',
		'goodsDescription',
		'origin',
		'destination',
		'latestStatus',
		'createDate',
	];
	readonly tablePageSizes: number[] = [10, 50, 100];

	dataSource = new MatTableDataSource<MawbListObject>(this.records || []);

	readonly mawbModule = Modules.MAWB;
	readonly createPermission = UserPermission.CREATE;
	readonly sharePermission = UserPermission.SHARE;

	constructor(private readonly router: Router) {
		super();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['records']) {
			this.dataSource.data = this.records;
		}
	}

	ngOnInit(): void {
		this.hasPermission(this.sharePermission, this.mawbModule)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((hasPermission) => {
				if (hasPermission) {
					this.displayedColumns.push('share');
				}
			});
	}

	onSortChange(event: Sort): void {
		this.currentSort = event;
		this.sortChange.emit(event);
		this.emitPaginationWithSort();
	}

	private emitPaginationWithSort(event?: PageEvent) {
		const pageEvent = event || {
			pageIndex: this.pageParams.pageNum - 1,
			pageSize: this.pageParams.pageSize,
			length: this.totalRecords,
		};

		this.pagination.emit({
			...pageEvent,
			sortField: this.currentSort.active,
			sortDirection: this.currentSort.direction,
		});
	}

	createMawbFromHawb(): void {
		this.router.navigate(['/mawb/create']);
	}

	editMawb(mawbId: string): void {
		this.router.navigate(['/mawb/edit', mawbId]);
	}

	// eslint-disable-next-line
	trackByMawbId(_index: number, record: MawbListObject): string {
		return record.mawbId + record.createDate;
	}
}
