import {
	clearFormArray,
	deepCopy,
	downloadBinaryFile,
	roundNumber,
} from '@shared/utils/common.utils';
import { FormArray, FormControl } from '@angular/forms';
import { HttpResponse } from '@angular/common/http';

describe('Common Utils', () => {
	it('#deepCopy should return the copied object', () => {
		const obj = { code: '123', name: 'Test' };
		expect(deepCopy(obj)).toEqual(obj);
	});

	it('#roundNumber should round down a number to 2 fraction digits', () => {
		expect(roundNumber(1.2345, 2)).toEqual(1.23);
	});

	it('#roundNumber should round up a number to 3 fraction digits', () => {
		expect(roundNumber(1.2345, 3)).toEqual(1.235);
	});

	it('#clearFormArray should remove all items from the FormArray', () => {
		const arrayItem = new FormControl();
		const formArray = new FormArray([arrayItem, arrayItem, arrayItem]);

		expect(formArray).toHaveSize(3);

		clearFormArray(formArray);

		expect(formArray).toHaveSize(0);
	});

	it('#downloadBinaryFile should download the file when given valid response and filename', () => {
		const createObjectURLSpy = spyOn(URL, 'createObjectURL').and.returnValue('blob:mock-url');
		const revokeObjectURLSpy = spyOn(URL, 'revokeObjectURL');
		const clickSpy = jasmine.createSpy('click');
		const createElementSpy = spyOn(document, 'createElement').and.returnValue({
			href: '',
			download: '',
			click: clickSpy,
		} as any);

		const mockBlob = new Blob(['test content'], { type: 'text/plain' });
		const mockResponse = new HttpResponse({
			body: mockBlob,
		});
		const filename = 'test-file.txt';

		downloadBinaryFile(mockResponse, filename);

		expect(createObjectURLSpy).toHaveBeenCalledWith(mockBlob);
		expect(createElementSpy).toHaveBeenCalledWith('a');
		expect(clickSpy).toHaveBeenCalled();
		expect(revokeObjectURLSpy).toHaveBeenCalledWith('blob:mock-url');
	});

	it('#downloadBinaryFile should not proceed if response is missing', () => {
		const createObjectURLSpy = spyOn(URL, 'createObjectURL');

		downloadBinaryFile(null, 'test.txt');
		expect(createObjectURLSpy).not.toHaveBeenCalled();
	});

	it('#downloadBinaryFile should set correct href and download attributes', () => {
		const createObjectURLSpy = spyOn(URL, 'createObjectURL').and.returnValue('blob:mock-url');
		const revokeObjectURLSpy = spyOn(URL, 'revokeObjectURL');
		const clickSpy = jasmine.createSpy('click');
		let aElement: any;
		const createElementSpy = spyOn(document, 'createElement').and.callFake(() => {
			aElement = {
				href: '',
				download: '',
				click: clickSpy,
			};
			return aElement;
		});

		const mockBlob = new Blob(['test content'], { type: 'text/plain' });
		const mockResponse = new HttpResponse({
			body: mockBlob,
		});
		const filename = 'test-file.txt';

		downloadBinaryFile(mockResponse, filename);

		expect(createObjectURLSpy).toHaveBeenCalledWith(mockBlob);
		expect(createElementSpy).toHaveBeenCalledWith('a');
		expect(aElement.href).toBe('blob:mock-url');
		expect(aElement.download).toBe(filename);
		expect(clickSpy).toHaveBeenCalled();
		expect(revokeObjectURLSpy).toHaveBeenCalledWith('blob:mock-url');
	});
});
