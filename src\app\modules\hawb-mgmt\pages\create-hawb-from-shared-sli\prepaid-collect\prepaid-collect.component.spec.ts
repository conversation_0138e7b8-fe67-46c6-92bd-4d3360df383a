import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { PrepaidCollectComponent } from './prepaid-collect.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';
import { By } from '@angular/platform-browser';

describe('PrepaidComponent', () => {
	let component: PrepaidCollectComponent;
	let fixture: ComponentFixture<PrepaidCollectComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [PrepaidCollectComponent],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting(), provideTranslateService()],
		}).compileComponents();

		fixture = TestBed.createComponent(PrepaidCollectComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('test form add prepaid value', () => {
		component.prepaidForm.get('weightChargePrepaid')?.setValue(100);
		let totalPrepaid = component.prepaidForm.get('totalPrepaid')!.value;
		expect(totalPrepaid).toEqual(100);

		component.prepaidForm.get('valuationChargePrepaid')?.setValue(100);
		totalPrepaid = component.prepaidForm.get('totalPrepaid')!.value;
		expect(totalPrepaid).toEqual(200);
	});

	it('test form add collect value', () => {
		component.prepaidForm.get('weightChargeCollect')?.setValue(100);
		let totalPrepaid = component.prepaidForm.get('totalCollect')!.value;
		expect(totalPrepaid).toEqual(100);

		component.prepaidForm.get('valuationChargeCollect')?.setValue(100);
		totalPrepaid = component.prepaidForm.get('totalCollect')!.value;
		expect(totalPrepaid).toEqual(200);
	});

	it('should initialize form with correct structure', () => {
		expect(component.prepaidForm.contains('weightChargePrepaid')).toBeDefined();
		expect(component.prepaidForm.contains('weightChargeCollect')).toBeDefined();
		expect(component.prepaidForm.contains('valuationChargePrepaid')).toBeDefined();
		expect(component.prepaidForm.contains('valuationChargeCollect')).toBeDefined();
		expect(component.prepaidForm.contains('totalPrepaid')).toBeDefined();
		expect(component.prepaidForm.contains('totalCollect')).toBeDefined();
	});

	it('should calculate totalPrepaid with any combination of values', fakeAsync(() => {
		// Both values
		component.prepaidForm.get('weightChargePrepaid')?.setValue(200);
		component.prepaidForm.get('valuationChargePrepaid')?.setValue(75);
		tick();
		expect(component.prepaidForm.get('totalPrepaid')?.value).toBe(275);

		// One value
		component.prepaidForm.get('valuationChargePrepaid')?.setValue(null);
		tick();
		expect(component.prepaidForm.get('totalPrepaid')?.value).toBe(200);

		// Other value
		component.prepaidForm.get('weightChargePrepaid')?.setValue(null);
		component.prepaidForm.get('valuationChargePrepaid')?.setValue(50);
		tick();
		expect(component.prepaidForm.get('totalPrepaid')?.value).toBe(50);

		// Both null
		component.prepaidForm.get('valuationChargePrepaid')?.setValue(null);
		tick();
		expect(component.prepaidForm.get('totalPrepaid')?.value).toBe(0);
	}));

	it('should calculate totalCollect with any combination of values', fakeAsync(() => {
		// Both values
		component.prepaidForm.get('weightChargeCollect')?.setValue(200);
		component.prepaidForm.get('valuationChargeCollect')?.setValue(75);
		tick();
		expect(component.prepaidForm.get('totalCollect')?.value).toBe(275);

		// One value
		component.prepaidForm.get('valuationChargeCollect')?.setValue(null);
		tick();
		expect(component.prepaidForm.get('totalCollect')?.value).toBe(200);

		// Other value
		component.prepaidForm.get('weightChargeCollect')?.setValue(null);
		component.prepaidForm.get('valuationChargeCollect')?.setValue(50);
		tick();
		expect(component.prepaidForm.get('totalCollect')?.value).toBe(50);

		// Both null
		component.prepaidForm.get('valuationChargeCollect')?.setValue(null);
		tick();
		expect(component.prepaidForm.get('totalCollect')?.value).toBe(0);
	}));

	it('should handle string values in calculations', fakeAsync(() => {
		component.prepaidForm.get('weightChargePrepaid')?.setValue(100);
		component.prepaidForm.get('valuationChargePrepaid')?.setValue(50);
		tick();
		expect(component.prepaidForm.get('totalPrepaid')?.value).toBe(150);

		component.prepaidForm.get('weightChargeCollect')?.setValue(200);
		component.prepaidForm.get('valuationChargeCollect')?.setValue(75);
		tick();
		expect(component.prepaidForm.get('totalCollect')?.value).toBe(275);
	}));

	it('should handle decimal values correctly', fakeAsync(() => {
		component.prepaidForm.get('weightChargePrepaid')?.setValue(100.25);
		component.prepaidForm.get('valuationChargePrepaid')?.setValue(50.75);
		tick();
		expect(component.prepaidForm.get('totalPrepaid')?.value).toBe(151);

		component.prepaidForm.get('weightChargeCollect')?.setValue(200.5);
		component.prepaidForm.get('valuationChargeCollect')?.setValue(75.25);
		tick();
		expect(component.prepaidForm.get('totalCollect')?.value).toBe(275.75);
	}));

	it('should update UI when values change', fakeAsync(() => {
		const weightPrepaidInput = fixture.debugElement.query(By.css('[formControlName="weightChargePrepaid"]')).nativeElement;
		const valuationPrepaidInput = fixture.debugElement.query(By.css('[formControlName="valuationChargePrepaid"]')).nativeElement;
		const totalPrepaidInput = fixture.debugElement.query(By.css('[formControlName="totalPrepaid"]')).nativeElement;

		weightPrepaidInput.value = '100';
		weightPrepaidInput.dispatchEvent(new Event('input'));
		valuationPrepaidInput.value = '50';
		valuationPrepaidInput.dispatchEvent(new Event('input'));
		tick();
		fixture.detectChanges();

		expect(totalPrepaidInput.value).toBe('150');
	}));

	it('should unsubscribe on component destruction', () => {
		const weightControl = component.prepaidForm.get('weightChargePrepaid');
		const valuationControl = component.prepaidForm.get('valuationChargePrepaid');

		// Set initial values
		weightControl?.setValue(100);
		valuationControl?.setValue(50);

		// Destroy component
		fixture.destroy();

		// Change values after destruction
		weightControl?.setValue(200);
		valuationControl?.setValue(100);

		// Should not update
		expect(component.prepaidForm.get('totalPrepaid')?.value).toBe(150);
	});
});
