<div class="orll-user-table__container">
	<div class="orll-user-table__create">
		<mat-form-field class="orll-user-table__search-keyword">
			<mat-icon matPrefix>search</mat-icon>
			<input matInput [formControl]="keyword" placeholder="{{'users.mgmt.keyword' | translate}}">
		</mat-form-field>
		<button mat-stroked-button color="primary" (click)="searchUser.emit(keyword?.value ?? '')" class="orll-user-table__search-button">
			<mat-icon>search</mat-icon>
			{{'users.mgmt.search' | translate}}
		</button>

		<button mat-flat-button color="primary" (click)="createUser()" class="orll-user-table__create-button">
			<mat-icon>add</mat-icon>
			{{'users.mgmt.create.title' | translate}}
		</button>
	</div>
	<table mat-table [dataSource]="dataSource" [trackBy]="trackByUserId"
		   matSort
		   (matSortChange)="onSortChange($event)"
		   aria-label="User table"
		   class="orll-user-table__mat">

		<ng-container matColumnDef="email">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="user-email-width">{{'users.table.column.email' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.email}}</td>
		</ng-container>

		<ng-container matColumnDef="firstName">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="user-firstname-width">{{'users.table.column.firstName' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.firstName}}</td>
		</ng-container>

		<ng-container matColumnDef="lastName">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="user-lastname-width">{{'users.table.column.lastName' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.lastName}}</td>
		</ng-container>

		<ng-container matColumnDef="orgName">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="user-orgname-width">{{'users.table.column.orgName' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.orgName}}</td>
		</ng-container>

		<ng-container matColumnDef="primaryOrgName">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="user-pryorgname-width">{{'users.table.column.primaryOrgName' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.primaryOrgName}}</td>
		</ng-container>

		<ng-container matColumnDef="userTypeText">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="user-type-width">{{'users.table.column.userType' | translate}}</th>
			<td mat-cell *matCellDef="let record">{{record.userTypeText}}</td>
		</ng-container>

		<ng-container matColumnDef="actions" class="user-actions-width">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>{{'users.table.column.actions' | translate}}</th>
			<td mat-cell *matCellDef="let record">
				<button mat-icon-button color="primary" (click)="editUser($event, record.userId)"
					class="orll-user-table__edit-button">
					<mat-icon>edit</mat-icon>
				</button>
				<button mat-icon-button color="primary" (click)="deleteUser($event, record.userId)"
					class="orll-user-table__delete-button">
					<mat-icon>delete</mat-icon>
				</button>
			</td>
		</ng-container>

		<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
		<tr mat-row *matRowDef="let record; columns: displayedColumns;" class="orll-user-table__row"></tr>
	</table>
</div>

<mat-paginator
   [pageSizeOptions]="tablePageSizes"
   [length]="totalRecords"
   (page)="pagination.emit($event)"
></mat-paginator>
