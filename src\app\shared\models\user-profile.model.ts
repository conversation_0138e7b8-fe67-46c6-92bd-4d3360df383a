export interface UserProfile {
	userId: string;
	email: string;
	firstName: string;
	lastName: string;
	primaryOrgId: string;
	primaryOrgName: string;
	orgId: string;
	orgName: string;
	orgType: string;
	userType: string;
	menuList: Menu[];
	permissionList: Permission[];
	orgList: Org[];
}

export interface Menu {
	path: string;
	name: string;
	icon: string;
}

export interface Permission {
	name: string;
	module: string;
	code: string[];
}

export interface Org {
	id: string;
	name: string;
	orgType: string;
	prefix: string;
}
