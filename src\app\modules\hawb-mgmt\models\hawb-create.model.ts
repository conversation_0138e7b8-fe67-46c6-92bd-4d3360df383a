export interface HawbCreateDto {
	id?: Id;
	sliId?: string;
	orgId: string;
	// waybillType: string;
	waybillPrefix: string;
	waybillNumber: string;
	partyList: PartyList[];
	sliPartyList?: PartyList[];
	accountingInformation: string;
	departureLocation: string;
	arrivalLocation: string;
	insuredAmount: InsuredAmount;
	weightValuationIndicator: string;
	otherChargesIndicator: string;
	declaredValueForCarriage: DeclaredValueForCarriage;
	declaredValueForCustoms: DeclaredValueForCustoms;
	textualHandlingInstructions: string;
	totalGrossWeight: number | null;
	rateClassCode: string | null;
	totalVolumetricWeight: number;
	rateCharge: RateCharge;
	goodsDescriptionForRate: string;
	otherChargeList: OtherChargeList[];
	carrierDeclarationDate: string;
	carrierDeclarationPlace: string;
	consignorDeclarationSignature: string;
	carrierDeclarationSignature: string;
}

export interface Id {
	iri: boolean;
	namespace: string;
	localName: string;
	resource: boolean;
	bnode: boolean;
	triple: boolean;
	literal: boolean;
}

export interface PartyList {
	id?: string | null;
	companyName: string;
	contactName: string;
	countryCode: string;
	regionCode: string;
	locationName: string;
	cityCode: string;
	textualPostCode: string;
	phoneNumber: string;
	emailAddress: string;
	companyType: string;
	iataCargoAgentCode: string;
}

export interface InsuredAmount {
	currencyUnit: string;
	numericalValue: number | null;
}

export interface DeclaredValueForCarriage {
	currencyUnit: string;
	numericalValue: number | null;
}

export interface DeclaredValueForCustoms {
	currencyUnit: string;
	numericalValue: number | null;
}

export interface RateCharge {
	currencyUnit: string;
	numericalValue: number | null;
}

export interface OtherChargeList {
	id?: string | null;
	chargePaymentType: string;
	entitlement: string;
	otherChargeAmount?: OtherChargeAmount;
	otherChargeCode: string;
	chargeQuantity?: number;
	locationIndicator?: string;
	reasonDescription?: string;
}

export interface OtherChargeAmount {
	currencyUnit: string;
	numericalValue: number | null;
}
