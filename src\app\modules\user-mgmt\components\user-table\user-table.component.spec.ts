import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { UserTableComponent } from './user-table.component';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, SimpleChange } from '@angular/core';
import { UserListObject } from '../../models/user-list-object.model';
import { TranslateModule } from '@ngx-translate/core';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { By } from '@angular/platform-browser';
import { MatDialog } from '@angular/material/dialog';
import { UserMgmtRequestService } from '../../services/user-mgmt-request.service';
import { of } from 'rxjs';

describe('UserTableComponent', () => {
	let component: UserTableComponent;
	let fixture: ComponentFixture<UserTableComponent>;
	let matDialogSpy: jasmine.SpyObj<MatDialog>;
	let userMgmtRequestServiceSpy: jasmine.SpyObj<UserMgmtRequestService>;

	// Mock data
	const mockRecords: UserListObject[] = [
		{
			userId: '2b7f9542-8e28-4d1f-b30b-ac0eac97db5d',
			firstName: 'John',
			lastName: 'Doe',
			email: '<EMAIL>',
			orgId: 'org123',
			orgName: 'org123',
			primaryOrgId: 'org123',
			primaryOrgName: 'org123',
			userType: 'admin',
			secondaryOrgIds: [
				{
					orgId: 'org456',
					orgName: 'Org 456',
					userType: 'user',
				},
			],
		},
		{
			userId: '2b7f9542-8e28-4d1f-b30b-ac0eac97db4d',
			firstName: 'John',
			lastName: 'Will',
			email: '<EMAIL>',
			orgId: 'org123',
			orgName: 'org123',
			primaryOrgId: 'org123',
			primaryOrgName: 'org123',
			userType: 'admin',
			secondaryOrgIds: [
				{
					orgId: 'org456',
					orgName: 'Org 456',
					userType: 'user',
				},
			],
		},
		{
			userId: '2b7f9542-8e28-4d1f-b30b-ac0eac97db3d',
			firstName: 'John',
			lastName: 'denver',
			email: '<EMAIL>',
			orgId: 'org123',
			orgName: 'org123',
			primaryOrgId: 'org123',
			primaryOrgName: 'org123',
			userType: 'admin',
			secondaryOrgIds: [
				{
					orgId: 'org456',
					orgName: 'Org 456',
					userType: 'user',
				},
			],
		},
	];

	// Mock data with missing fields
	const mockIncompleteRecords: UserListObject[] = [
		{
			userId: '2b7f9542-8e28-4d1f-b30b-ac0eac97db2d',
			firstName: 'John',
			lastName: 'Doe',
			email: '',
			orgId: 'org123',
			orgName: 'org123',
			primaryOrgId: 'org123',
			primaryOrgName: 'org123',
			userType: 'admin',
			secondaryOrgIds: [
				{
					orgId: 'org456',
					orgName: 'Org 456',
					userType: 'user',
				},
			],
		},
		{
			userId: '2b7f9542-8e28-4d1f-b30b-ac0eac97db1d',
			firstName: '',
			lastName: '',
			email: '<EMAIL>',
			orgId: 'org123',
			orgName: 'org123',
			primaryOrgId: 'org123',
			primaryOrgName: 'org123',
			userType: 'admin',
			secondaryOrgIds: [
				{
					orgId: 'org456',
					orgName: 'Org 456',
					userType: 'user',
				},
			],
		},
	];

	beforeEach(async () => {
		matDialogSpy = jasmine.createSpyObj('MatDialog', ['open']);
		userMgmtRequestServiceSpy = jasmine.createSpyObj('UserMgmtRequestService', ['deleteUser']);

		await TestBed.configureTestingModule({
			imports: [UserTableComponent, TranslateModule.forRoot(), NoopAnimationsModule],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
			providers: [
				{ provide: MatDialog, useValue: matDialogSpy },
				{ provide: UserMgmtRequestService, useValue: userMgmtRequestServiceSpy },
			],
		}).compileComponents();
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(UserTableComponent);
		component = fixture.componentInstance;

		// Initialize required pagination params
		component.pageParams = {
			pageNum: 1,
			pageSize: 10,
			orderByColumn: '',
			isAsc: 'asc',
		};

		fixture.detectChanges();
	});

	describe('Component Initialization', () => {
		it('should create the component', () => {
			expect(component).toBeTruthy();
		});

		it('should initialize with empty records array', () => {
			expect(component.records).toEqual([]);
			expect(component.dataSource.data).toEqual([]);
		});

		it('should initialize with correct displayed columns', () => {
			const expectedColumns = ['email', 'firstName', 'lastName', 'orgName', 'primaryOrgName', 'userTypeText', 'actions'];
			expect(component.displayedColumns).toEqual(expectedColumns);
		});

		it('should initialize with correct page size options', () => {
			expect(component.tablePageSizes).toEqual([10, 50, 100]);
		});
	});

	describe('Data Handling', () => {
		it('should update dataSource when records input changes', () => {
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockRecords, true) });
			expect(component.dataSource.data).toEqual(mockRecords);
		});

		it('should not update dataSource when other inputs change', () => {
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockRecords, true) });

			// Set initial state
			expect(component.dataSource.data).toEqual(mockRecords);

			// Change totalRecords
			component.totalRecords = 100;
			component.ngOnChanges({ totalRecords: new SimpleChange(0, 100, true) });

			// DataSource should remain unchanged
			expect(component.dataSource.data).toEqual(mockRecords);
		});

		it('should generate correct tracking key with trackByUserId', () => {
			const record = mockRecords[0];
			const key = component.trackByUserId(record);
			expect(key).toEqual('2b7f9542-8e28-4d1f-b30b-ac0eac97db5d');
		});

		it('should handle empty records array', () => {
			component.records = [];
			component.ngOnChanges({ records: new SimpleChange(mockRecords, [], false) });
			expect(component.dataSource.data).toEqual([]);
		});

		it('should handle records with missing data', () => {
			component.records = mockIncompleteRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockIncompleteRecords, true) });
			expect(component.dataSource.data).toEqual(mockIncompleteRecords);

			// Verify the table renders correctly with incomplete data
			fixture.detectChanges();
			const rows = fixture.debugElement.queryAll(By.css('tr.orll-user-table__row'));
			expect(rows.length).toBe(mockIncompleteRecords.length);
		});

		it('should handle null records gracefully', () => {
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(mockRecords, null, false) });
			// When null is passed, the component should default to an empty array
			expect(component.dataSource.data).toEqual(mockRecords);
		});
	});

	describe('Event Handling', () => {
		it('should emit sortChange event when sort is changed', fakeAsync(() => {
			// Setup
			spyOn(component.sortChange, 'emit');

			// Manually trigger the sort event
			const sortEvent: Sort = { active: 'waybillNumber', direction: 'asc' };
			component.onSortChange(sortEvent);
			tick();

			// Verify the event was emitted with the correct sort parameters
			expect(component.sortChange.emit).toHaveBeenCalledWith(sortEvent);
		}));

		it('should emit pagination event when page is changed', () => {
			// Setup
			spyOn(component.pagination, 'emit');

			// Create a page event
			const pageEvent: PageEvent = {
				pageIndex: 1,
				pageSize: 50,
				length: 100,
			};

			// Get the paginator and trigger the page event
			const paginator = fixture.debugElement.query(By.directive(MatPaginator));
			if (paginator) {
				const paginatorComponent = paginator.componentInstance as MatPaginator;
				paginatorComponent.page.emit(pageEvent);

				// Verify the event was emitted with the correct page parameters
				expect(component.pagination.emit).toHaveBeenCalledWith(pageEvent);
			}
		});

		it('should emit pagination with sort information when emitPaginationWithSort is called', fakeAsync(() => {
			// Setup
			spyOn(component.pagination, 'emit');

			// Set current sort
			const sortEvent: Sort = { active: 'shipper', direction: 'desc' };
			component.currentSort = sortEvent;

			// Call the private method using type assertion
			(component as any).emitPaginationWithSort();
			tick();

			// Verify the event was emitted with the correct parameters
			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: component.pageParams.pageNum - 1,
				pageSize: component.pageParams.pageSize,
				length: component.totalRecords,
				sortField: 'shipper',
				sortDirection: 'desc',
			});
		}));

		it('should emit pagination with sort and custom page event when emitPaginationWithSort is called with page event', fakeAsync(() => {
			// Setup
			spyOn(component.pagination, 'emit');

			// Set current sort
			const sortEvent: Sort = { active: 'consignee', direction: 'asc' };
			component.currentSort = sortEvent;

			// Create a page event
			const pageEvent: PageEvent = {
				pageIndex: 2,
				pageSize: 25,
				length: 150,
			};

			// Call the private method using type assertion
			(component as any).emitPaginationWithSort(pageEvent);
			tick();

			// Verify the event was emitted with the correct parameters
			expect(component.pagination.emit).toHaveBeenCalledWith({
				...pageEvent,
				sortField: 'consignee',
				sortDirection: 'asc',
			});
		}));
	});

	describe('UI Elements', () => {
		beforeEach(() => {
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockRecords, true) });
			fixture.detectChanges();
		});

		it('should display the correct number of rows in the table', () => {
			const rows = fixture.debugElement.queryAll(By.css('tr.orll-user-table__row'));
			expect(rows.length).toBe(mockRecords.length);
		});

		it('should display the create button with correct text', () => {
			const createButton = fixture.debugElement.query(By.css('.orll-user-table__create-button'));
			expect(createButton).toBeTruthy();
		});

		it('should display the edit button with correct text', () => {
			const editButton = fixture.debugElement.query(By.css('.orll-user-table__edit-button'));
			expect(editButton).toBeTruthy();
		});

		it('should display the delete button with correct text', () => {
			const deleteButton = fixture.debugElement.query(By.css('.orll-user-table__delete-button'));
			expect(deleteButton).toBeTruthy();
		});

		it('should call createUser when create button is clicked', () => {
			spyOn(component, 'createUser');
			const createButton = fixture.debugElement.query(By.css('.orll-user-table__create-button'));
			createButton.nativeElement.click();
			expect(component.createUser).toHaveBeenCalled();
		});

		it('should call editUser when edit button is clicked', () => {
			spyOn(component, 'editUser');
			const editButton = fixture.debugElement.query(By.css('.orll-user-table__edit-button'));
			editButton.nativeElement.click();
			expect(component.editUser).toHaveBeenCalled();
		});

		it('should call deleteUser when delete button is clicked', () => {
			spyOn(component, 'deleteUser');
			const deleteButton = fixture.debugElement.query(By.css('.orll-user-table__delete-button'));
			deleteButton.nativeElement.click();
			expect(component.deleteUser).toHaveBeenCalled();
		});

		it('should display all table columns correctly', () => {
			const headerCells = fixture.debugElement.queryAll(By.css('th[mat-header-cell]'));
			expect(headerCells.length).toBe(component.displayedColumns.length);
		});

		it('should display correct data in each cell', () => {
			const firstRow = fixture.debugElement.queryAll(By.css('tr.orll-user-table__row'))[0];
			const cells = firstRow.queryAll(By.css('td'));

			expect(cells[1].nativeElement.textContent.trim()).toBe(mockRecords[0].firstName);

			expect(cells[2].nativeElement.textContent.trim()).toBe(mockRecords[0].lastName);
		});
	});

	describe('Edge Cases', () => {
		it('should handle empty string values in records', () => {
			component.records = mockIncompleteRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockIncompleteRecords, true) });
			fixture.detectChanges();

			// Get the first row (which has empty shipper)
			const firstRow = fixture.debugElement.queryAll(By.css('tr.orll-user-table__row'))[0];
			const cells = firstRow.queryAll(By.css('td'));

			expect(cells[0].nativeElement.textContent.trim()).toBe('');

			const secondRow = fixture.debugElement.queryAll(By.css('tr.orll-user-table__row'))[1];
			const secondRowCells = secondRow.queryAll(By.css('td'));

			expect(secondRowCells[1].nativeElement.textContent.trim()).toBe('');

			expect(secondRowCells[2].nativeElement.textContent.trim()).toBe('');
		});

		it('should handle sorting with empty values', fakeAsync(() => {
			// Setup with records containing empty values
			component.records = [...mockRecords, ...mockIncompleteRecords];
			component.ngOnChanges({ records: new SimpleChange(null, component.records, true) });
			fixture.detectChanges();

			// Spy on the sort change event
			spyOn(component.sortChange, 'emit');

			// Trigger sort on a column with empty values (shipper)
			const sortEvent: Sort = { active: 'shipper', direction: 'asc' };
			component.onSortChange(sortEvent);
			tick();

			// Verify the event was emitted with the correct sort parameters
			expect(component.sortChange.emit).toHaveBeenCalledWith(sortEvent);
		}));
	});

	describe('Dialog and Service Interactions', () => {
		it('should open AddUserDialogComponent and emit searchUser on createUser if confirmed', fakeAsync(() => {
			matDialogSpy.open.and.returnValue({ afterClosed: () => of(true) } as any);
			spyOn(component.searchUser, 'emit');
			component.keyword.setValue('test');
			component.createUser();
			tick();
			expect(matDialogSpy.open).toHaveBeenCalled();
			expect(component.searchUser.emit).toHaveBeenCalledWith('test');
		}));

		it('should open AddUserDialogComponent and emit searchUser on editUser if confirmed', fakeAsync(() => {
			const event = new Event('click');
			matDialogSpy.open.and.returnValue({ afterClosed: () => of(true) } as any);
			spyOn(component.searchUser, 'emit');
			component.keyword.setValue('test');
			component.editUser(event, 'userId123');
			tick();
			expect(matDialogSpy.open).toHaveBeenCalled();
			const callArgs = matDialogSpy.open.calls.mostRecent().args[1];
			if (callArgs && typeof callArgs === 'object' && 'data' in callArgs && callArgs.data) {
				expect((callArgs as any).data.userId).toBe('userId123');
			}
			expect(component.searchUser.emit).toHaveBeenCalledWith('test');
		}));

		it('should open ConfirmDialogComponent and call deleteUser and emit searchUser if confirmed', fakeAsync(() => {
			matDialogSpy.open.and.returnValue({ afterClosed: () => of(true) } as any);
			userMgmtRequestServiceSpy.deleteUser.and.returnValue(of(''));
			spyOn(component.searchUser, 'emit');
			const event = new Event('click');
			component.keyword.setValue('delete-keyword');
			component.deleteUser(event, 'userIdToDelete');
			tick();
			expect(matDialogSpy.open).toHaveBeenCalled();
			expect(userMgmtRequestServiceSpy.deleteUser).toHaveBeenCalledWith('userIdToDelete');
			expect(component.searchUser.emit).toHaveBeenCalledWith('delete-keyword');
		}));

		it('should not emit searchUser if deleteUser dialog is not confirmed', () => {
			const afterClosedSpy = jasmine.createSpyObj({ subscribe: (fn: any) => fn(false) });
			matDialogSpy.open.and.returnValue({ afterClosed: () => afterClosedSpy } as any);
			userMgmtRequestServiceSpy.deleteUser.and.returnValue(of(''));
			spyOn(component.searchUser, 'emit');
			const event = new Event('click');
			component.deleteUser(event, 'userIdToDelete');
			expect(matDialogSpy.open).toHaveBeenCalled();
			expect(userMgmtRequestServiceSpy.deleteUser).not.toHaveBeenCalled();
			expect(component.searchUser.emit).not.toHaveBeenCalled();
		});
	});
});
