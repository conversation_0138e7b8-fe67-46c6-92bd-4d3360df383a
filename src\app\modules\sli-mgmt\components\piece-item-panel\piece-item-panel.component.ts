import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { PieceItem } from '../../models/piece/piece-item.model';
import { Piece } from '../../models/piece/piece.model';
import { MatTooltipModule } from '@angular/material/tooltip';

const REGX_NUMBER_1_DECIMAL = '^\\d+(\\.\\d{1})?$';
const REGX_POSITIVE_NUMBER = '^[1-9]\\d*$';

@Component({
	selector: 'orll-piece-item-panel',
	templateUrl: './piece-item-panel.component.html',
	styleUrl: './piece-item-panel.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatIconModule,
		MatButtonModule,
		MatExpansionModule,
		MatTooltipModule,
		MatInputModule,
		MatListModule,
		MatDividerModule,
		MatFormFieldModule,
		ReactiveFormsModule,
		FormsModule,
		CommonModule,
		TranslateModule,
	],
})
export class PieceItemPanelComponent {
	@Input() pieceIndex = 0;
	@Input() containedPiece: Piece | null = null;
	@Input() pieceItemList: PieceItem[] = [];

	@Output() deletePiece = new EventEmitter<void>();

	sliPieceItemForm: FormGroup = new FormGroup({
		description: new FormControl<string>(''),
		weight: new FormControl<string>('', [Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		quantity: new FormControl<string>('', [Validators.pattern(REGX_POSITIVE_NUMBER)]),
	});

	addPieceItem(pieceIndex: number): void {
		if (this.sliPieceItemForm.invalid) {
			return;
		}

		if (pieceIndex >= 0 && this.containedPiece) {
			this.containedPiece.containedItems.push({
				product: {
					description: this.sliPieceItemForm.value.description ?? '',
					hsCommodityDescription: '',
				},
				weight: Number(this.sliPieceItemForm.value.weight ?? ''),
				itemQuantity: Number(this.sliPieceItemForm.value.quantity ?? ''),
			});
		} else {
			this.pieceItemList.push({
				product: {
					description: this.sliPieceItemForm.value.description ?? '',
					hsCommodityDescription: '',
				},
				weight: Number(this.sliPieceItemForm.value.weight ?? ''),
				itemQuantity: Number(this.sliPieceItemForm.value.quantity ?? ''),
			});
		}

		this.sliPieceItemForm.reset();
	}

	delPieceItem(pieceIndex: number, pieceItemIndex: number): void {
		if (pieceIndex >= 0 && this.containedPiece) {
			this.containedPiece.containedItems.splice(pieceItemIndex, 1);
		} else {
			this.pieceItemList.splice(pieceItemIndex, 1);
		}
	}

	getPieceItemList(): PieceItem[] {
		return this.pieceItemList;
	}
}
