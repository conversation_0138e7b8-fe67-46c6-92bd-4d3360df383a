<div class="orll-secondary-user-panel">
	<mat-expansion-panel class="orll-secondary-user-panel__panel" expanded>
		<mat-expansion-panel-header>
			<mat-panel-title>

			</mat-panel-title>
			<mat-panel-description>

			</mat-panel-description>
		</mat-expansion-panel-header>

		<form [formGroup]="secondaryUserForm">
			<div class="row">
				<mat-form-field appearance="outline" class="col-5">
					<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
					<mat-label>{{'users.mgmt.create.orgName' | translate}}</mat-label>
					<input type="text" matInput
						formControlName="orgId"
						[matAutocomplete]="autoOrg">
					<mat-autocomplete #autoOrg="matAutocomplete" [displayWith]="displayOrgName">
						@for (org of filteredOrgList; track org) {
							<mat-option [value]="org.id">{{org.name}}</mat-option>
						}
					</mat-autocomplete>
					@if (secondaryUserForm.get('orgId')?.hasError('required')) {
						<mat-error>{{'users.mgmt.create.orgName.required' | translate}}</mat-error>
					}
				</mat-form-field>

				<mat-form-field appearance="outline" class="col-4">
					<mat-label>{{'users.mgmt.create.userType' | translate}}</mat-label>
					<mat-select formControlName="userType">
						@for (userType of userTypes; track userType) {
							<mat-option [value]="userType.code">{{userType.name}}</mat-option>
						}
					</mat-select>
					@if (secondaryUserForm.get('userType')?.hasError('required')) {
						<mat-error>{{'users.mgmt.create.userType.required' | translate}}</mat-error>
					}
				</mat-form-field>

				<button mat-raised-button type="button" (click)="addSecondaryUser()" class="col-2 orll-secondary-user-panel__add-button">
					<mat-icon>add</mat-icon>
					{{'users.mgmt.create.secondary.add' | translate}}
				</button>
			</div>
		</form>

		<mat-list>
			@for (userItem of secondaryUserList; track userItem; let userItemIndex = $index) {
				<mat-list-item>
					<div class="secondary-user-content">
						<div class="item-info">
							<div class="item-orgName col-6">{{userItem.orgName}}</div>
							<div class="item-userType col-6">{{displayUserType(userItem.userType.toString())}}</div>
						</div>
						<button mat-icon-button color="primary" type="button" (click)="delUserItem(userItemIndex)" class="orll-secondary-user-panel__delete-button">
							<mat-icon>delete</mat-icon>
						</button>
					</div>
				</mat-list-item>
				@if (!$last) {
					<mat-divider></mat-divider>
				}
			}
		</mat-list>
	</mat-expansion-panel>
</div>
