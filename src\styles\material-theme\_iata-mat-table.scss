@mixin iata-table-theme($theme) {
	.mat-mdc-table {
		--mat-table-header-headline-size: 12px;
		--mat-table-header-headline-line-height: 14px;
		--mat-table-row-item-label-text-size: 12px;
		--mat-table-row-item-label-text-line-height: 20px;
		--mat-table-header-container-height: 48px;
		--mat-table-footer-container-height: 48px;
		--mat-table-row-item-container-height: 48px;
		--mat-sort-arrow-color: var(--iata-grey-200);

		.mat-mdc-header-cell {
			color: rgb(from var(--iata-grey-600) r g b / 0.6);
			border-right: 1px solid var(--iata-grey-50);
			border-bottom-color: rgb(from var(--iata-grey-700) r g b / 0.16);
			border-bottom-width: 2px;
			font-weight: 500;
			padding: 0 10px;

			.mat-sort-header-content {
				text-align: left;
			}

			.mat-sort-header-arrow {
				margin-left: 0;
			}
		}

		.mdc-data-table__row:last-child .mat-mdc-cell,
		.mat-mdc-cell {
			border-bottom: 1px solid rgb(from var(--iata-grey-700) r g b / 0.16);
			border-right: 1px solid var(--iata-grey-50);
			color: var(--iata-grey-700);
			padding: 0 10px;
		}
	}
}
