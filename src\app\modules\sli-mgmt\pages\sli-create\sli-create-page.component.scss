.orll-sli-create-page {
	margin-top: 40px;

	.row {
		display: flex;
		flex-wrap: nowrap;
		gap: 20px;
		margin: 20px 15px;

		&.margin-r-5 {
			margin-right: -5px;
		}
	}

	.iata-box {
		position: relative;
		margin-bottom: 20px;
	}

	.orll-sli-shipper {
		&__box {
			min-height: 340px;
		}
	}

	.orll-sli-consignee {
		&__box {
			min-height: 340px;
		}
	}

	.orll-sli-also-notify {
		&__box {
			min-height: 76px;
		}

		&__panel {
			margin-bottom: 20px;
		}

		&__title {
			color: var(--iata-blue-primary);
			font-size: 32px;
			margin-bottom: 0;
		}

		&__add-button {
			color: var(--iata-blue-primary) !important;
			border: 1px solid var(--iata-blue-primary);
		}
	}

	.orll-sli-routing {
		&__box {
			min-height: 190px;
		}
	}

	&__footer {
		display: flex;
		justify-content: flex-end;
		margin: 0 20px;
	}

	&__cancel-button {
		margin-right: 20px;
		color: var(--iata-blue-primary) !important;
		border: 1px solid var(--iata-blue-primary);
	}

	::ng-deep .mat-expansion-panel-header-description {
		flex-grow: 0;
	}
}
