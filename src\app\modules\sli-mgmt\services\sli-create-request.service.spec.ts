import { TestBed } from '@angular/core/testing';
import { SliCreateRequestService } from './sli-create-request.service';
import { map, of, throwError } from 'rxjs';
import { CodeName } from '@shared/models/code-name.model';
import { AIRPORTS } from '../ref-data/airports.data';
import { COUNTRIES } from '../ref-data/countries.data';
import { CURRENCIES } from '../ref-data/currencies.data';
import { INCOTERMS } from '../ref-data/incoterms.data';
import { Country } from '../models/country.model';
import { Province } from '../models/province.model';
import { HttpClient } from '@angular/common/http';
import { SliCreatePayload } from '../models/sli-create-payload.model';
import { ShipmentParty } from '../models/shipment-party.model';
import { Dimensions } from '../models/piece/dimensions.model';
import { Piece } from '../models/piece/piece.model';
import { Currency } from '../models/currency.model';
import { PieceList } from '../models/piece/piece-list.model';

const mockProvinces: Province[] = [
	{ code: 'P1', name: 'Province A', cities: [] },
	{ code: 'P2', name: 'Province B', cities: [] },
	{ code: 'P3', name: 'Province C', cities: [] },
];

const mockCountry: Country = {
	name: 'Test Country',
	code: 'TC',
	provinces: [...mockProvinces],
};

describe('SliCreateRequestService', () => {
	let service: SliCreateRequestService;
	let httpClientSpy: jasmine.SpyObj<HttpClient>;

	beforeEach(async () => {
		httpClientSpy = jasmine.createSpyObj('HttpClient', ['get', 'post', 'patch']);

		await TestBed.configureTestingModule({
			providers: [SliCreateRequestService, { provide: HttpClient, useValue: httpClientSpy }],
		});
	});

	beforeEach(() => {
		service = TestBed.inject(SliCreateRequestService);
	});

	it('#getCountries should retrieve all countries', () => {
		service.getCountries().subscribe((response: Country[]) => {
			expect(response).toHaveSize(COUNTRIES.length);
		});
	});

	it('#getCountries should return filtered country when code is provided', () => {
		const testCode = 'US';
		service.getCountries(testCode).subscribe((result) => {
			expect(result.length).toBe(1);
			expect(result[0].code).toBe(testCode);
		});
	});

	it('#getCountries should return empty array when non-existent code is provided', () => {
		service.getCountries('XX').subscribe((result) => {
			expect(result.length).toBe(0);
		});
	});

	it('#getCountries should maintain provinces data in the response', () => {
		service.getCountries().subscribe((result) => {
			expect(result[0].provinces).toBeDefined();
			expect(Array.isArray(result[0].provinces)).toBe(true);
		});
	});

	it('#getProvinces should filter provinces by code when code is provided', () => {
		const result$ = service.getProvinces(mockCountry, 'P2');

		result$.subscribe((provinces: Province[]) => {
			expect(provinces.length).toBe(1);
			expect(provinces[0].code).toBe('P2');
		});
	});

	it('#getProvinces should return empty array when no matching code found', () => {
		const result$ = service.getProvinces(mockCountry, 'INVALID_CODE');

		result$.subscribe((provinces: Province[]) => {
			expect(provinces.length).toBe(0);
		});
	});

	it('#getProvinces should maintain the cities array in the output', () => {
		const testProvince: Province = {
			code: 'TEST',
			name: 'Test Province',
			cities: [{ code: 'C1', name: 'City 1' }],
		};
		const testCountry: Country = {
			name: 'Test Country',
			code: 'TC',
			provinces: [testProvince],
		};

		const result$ = service.getProvinces(testCountry);

		result$.subscribe((provinces: Province[]) => {
			expect(provinces[0].cities).toEqual(testProvince.cities);
		});
	});

	it('#getProvinces should handle empty provinces array', () => {
		const emptyCountry: Country = {
			name: 'Empty Country',
			code: 'EC',
			provinces: [],
		};

		const result$ = service.getProvinces(emptyCountry);

		result$.subscribe((provinces: Province[]) => {
			expect(provinces.length).toBe(0);
		});
	});

	it('#getCities should handle empty cities array', () => {
		const mockProvince: Province = {
			name: 'Test Province',
			code: 'TP',
			cities: [],
		};

		of(mockProvince.cities)
			.pipe(
				map((cities: CodeName[]) => {
					return cities.map((city: CodeName) => {
						return { code: city.code, name: city.name };
					});
				})
			)
			.subscribe((result) => {
				expect(result).toEqual([]);
			});
	});

	it('#getCities should transform city data correctly', () => {
		const mockProvince: Province = {
			name: 'Test Province',
			code: 'TP',
			cities: [
				{ code: 'C1', name: 'City One' },
				{ code: 'C2', name: 'City Two' },
			],
		};

		service.getCities(mockProvince).subscribe((cities: CodeName[]) => {
			expect(cities.length).toBe(2);
			expect(cities[0].code).toBe('C1');
			expect(cities[0].name).toBe('City One');
			expect(cities[1].code).toBe('C2');
			expect(cities[1].name).toBe('City Two');
		});
	});

	it('#getAirports should retrieve all airports', () => {
		service.getAirports().subscribe((response: CodeName[]) => {
			expect(response).toHaveSize(AIRPORTS.length);
		});
	});

	it('#getAirports should format airport names correctly', () => {
		service.getAirports().subscribe((response: CodeName[]) => {
			response.forEach((airport, index) => {
				expect(airport.name).toContain(AIRPORTS[index].code);
				expect(airport.name).toContain(AIRPORTS[index].name);
				expect(airport.name).toBe(`${AIRPORTS[index].code} - ${AIRPORTS[index].name}`);
			});
		});
	});

	it('#getCurrencies should retrieve all currencies', () => {
		service.getCurrencies().subscribe((response: string[]) => {
			expect(response).toHaveSize(CURRENCIES.length);
		});
	});

	it('#getIncoterms should retrieve all incoterms', () => {
		service.getIncoterms().subscribe((response: CodeName[]) => {
			expect(response).toHaveSize(INCOTERMS.length);
		});
	});

	it('#getIncoterms should format incoterm names correctly', () => {
		service.getIncoterms().subscribe((response: CodeName[]) => {
			response.forEach((incoterm, index) => {
				expect(incoterm.name).toContain(INCOTERMS[index].code);
				expect(incoterm.name).toContain(INCOTERMS[index].name);
				expect(incoterm.name).toBe(`${INCOTERMS[index].code} - ${INCOTERMS[index].name}`);
			});
		});
	});

	it('#createSli should post SLI data to the API', (done: DoneFn) => {
		const mockSliPayload = createMockSliPayload();
		const mockResponse = '123';
		httpClientSpy.post.and.returnValue(of(mockResponse));

		service.createSli(mockSliPayload).subscribe({
			next: (response) => {
				expect(response).toEqual(mockResponse);
				expect(httpClientSpy.post).toHaveBeenCalledWith(jasmine.stringContaining('sli'), mockSliPayload);
				done();
			},
			error: done.fail,
		});
	});

	it('#createSli should handle error response', (done: DoneFn) => {
		const mockSliPayload = createMockSliPayload();
		const errorResponse = { status: 500, message: 'Server error' };
		httpClientSpy.post.and.returnValue(throwError(() => errorResponse));

		service.createSli(mockSliPayload).subscribe({
			next: () => done.fail('Expected error but got success response'),
			error: (error) => {
				expect(error).toBe(errorResponse);
				done();
			},
		});
	});

	it('#updateSli should update SLI data via API', (done: DoneFn) => {
		const mockSliPayload = createMockSliPayload();
		const sliNumber = 'SLI-001';
		const mockResponse = '456';
		httpClientSpy.patch.and.returnValue(of(mockResponse));

		service.updateSli(mockSliPayload, sliNumber).subscribe({
			next: (response) => {
				expect(response).toEqual(mockResponse);
				expect(httpClientSpy.patch).toHaveBeenCalledWith(jasmine.stringContaining('sli'), { id: sliNumber, ...mockSliPayload });
				done();
			},
			error: done.fail,
		});
	});

	it('#updateSli should handle error response', (done: DoneFn) => {
		const mockSliPayload = createMockSliPayload();
		const sliNumber = 'SLI-001';
		const errorResponse = { status: 400, message: 'Bad request' };
		httpClientSpy.patch.and.returnValue(throwError(() => errorResponse));

		service.updateSli(mockSliPayload, sliNumber).subscribe({
			next: () => done.fail('Expected error but got success response'),
			error: (error) => {
				expect(error).toBe(errorResponse);
				done();
			},
		});
	});

	it('#getSliDetail should retrieve SLI details by sliNumber', (done: DoneFn) => {
		const sliNumber = 'SLI-001';
		const mockResponse = createMockSliPayload();
		httpClientSpy.get.and.returnValue(of(mockResponse));

		service.getSliDetail(sliNumber).subscribe({
			next: (response) => {
				expect(response).toEqual(mockResponse);
				expect(httpClientSpy.get).toHaveBeenCalledWith(jasmine.stringContaining('sli/detail'), { params: jasmine.any(Object) });
				done();
			},
			error: done.fail,
		});
	});

	it('#getSliDetail should handle error response', (done: DoneFn) => {
		const sliNumber = 'SLI-001';
		const errorResponse = { status: 404, message: 'Not found' };
		httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

		service.getSliDetail(sliNumber).subscribe({
			next: () => done.fail('Expected error but got success response'),
			error: (error) => {
				expect(error).toBe(errorResponse);
				done();
			},
		});
	});

	it('#getPackingTypes should retrieve packing types from API', (done: DoneFn) => {
		const mockPackingTypes: CodeName[] = [
			{ code: 'BOX', name: 'Box' },
			{ code: 'PALLET', name: 'Pallet' },
		];
		httpClientSpy.get.and.returnValue(of(mockPackingTypes));

		service.getPackingTypes().subscribe({
			next: (response: CodeName[]) => {
				expect(response.length).toBe(mockPackingTypes.length);
				expect(response[0].code).toBe(mockPackingTypes[0].code);
				expect(response[0].name).toBe(mockPackingTypes[0].name);
				expect(httpClientSpy.get).toHaveBeenCalledWith(
					jasmine.stringContaining('sys-management/enums/packageType'),
					jasmine.any(Object)
				);
				done();
			},
			error: done.fail,
		});
	});

	it('#getPackingTypes should handle empty response', (done: DoneFn) => {
		const mockResponse: CodeName[] = [];
		httpClientSpy.get.and.returnValue(of(mockResponse));

		service.getPackingTypes().subscribe({
			next: (response: CodeName[]) => {
				expect(response.length).toBe(0);
				done();
			},
			error: done.fail,
		});
	});

	it('#getPackingTypes should handle error response', (done: DoneFn) => {
		const errorResponse = { status: 500, message: 'Server error' };
		httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

		service.getPackingTypes().subscribe({
			next: () => done.fail('Expected error but got success response'),
			error: (error) => {
				expect(error).toBe(errorResponse);
				done();
			},
		});
	});

	it('#createPiece should post piece data to the API', (done: DoneFn) => {
		const mockPiece = createMockSliPayload().pieces[0];
		const mockResponse = 'piece-123';
		httpClientSpy.post.and.returnValue(of(mockResponse));

		service.createPiece(mockPiece).subscribe({
			next: (response) => {
				expect(response).toEqual(mockResponse);
				expect(httpClientSpy.post).toHaveBeenCalledWith(jasmine.stringContaining('sli/piece'), mockPiece);
				done();
			},
			error: done.fail,
		});
	});

	it('#createPiece should handle error response', (done: DoneFn) => {
		const mockPiece = createMockSliPayload().pieces[0];
		const errorResponse = { status: 500, message: 'Server error' };
		httpClientSpy.post.and.returnValue(throwError(() => errorResponse));

		service.createPiece(mockPiece).subscribe({
			next: () => done.fail('Expected error but got success response'),
			error: (error) => {
				expect(error).toBe(errorResponse);
				done();
			},
		});
	});

	it('#updatePiece should patch piece data via API', (done: DoneFn) => {
		const mockPiece = createMockSliPayload().pieces[0];
		const pieceId = 'piece-123';
		const mockResponse = 'updated-piece-123';
		httpClientSpy.patch.and.returnValue(of(mockResponse));

		service.updatePiece(mockPiece, pieceId).subscribe({
			next: (response) => {
				expect(response).toEqual(mockResponse);
				expect(httpClientSpy.patch).toHaveBeenCalledWith(jasmine.stringContaining('sli/piece'), { id: pieceId, ...mockPiece });
				done();
			},
			error: done.fail,
		});
	});

	it('#updatePiece should handle error response', (done: DoneFn) => {
		const mockPiece = createMockSliPayload().pieces[0];
		const pieceId = 'piece-123';
		const errorResponse = { status: 400, message: 'Bad request' };
		httpClientSpy.patch.and.returnValue(throwError(() => errorResponse));

		service.updatePiece(mockPiece, pieceId).subscribe({
			next: () => done.fail('Expected error but got success response'),
			error: (error) => {
				expect(error).toBe(errorResponse);
				done();
			},
		});
	});

	it('#getPieceDetail should retrieve piece details by pieceId', (done: DoneFn) => {
		const pieceId = 'piece-123';
		const mockResponse = createMockSliPayload().pieces[0];
		httpClientSpy.get.and.returnValue(of(mockResponse));

		service.getPieceDetail(pieceId).subscribe({
			next: (response) => {
				expect(response).toEqual(mockResponse);
				expect(httpClientSpy.get).toHaveBeenCalledWith(jasmine.stringContaining('sli/piece/detail'), {
					params: jasmine.any(Object),
				});
				done();
			},
			error: done.fail,
		});
	});

	it('#getPieceDetail should handle error response', (done: DoneFn) => {
		const pieceId = 'piece-123';
		const errorResponse = { status: 404, message: 'Not found' };
		httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

		service.getPieceDetail(pieceId).subscribe({
			next: () => done.fail('Expected error but got success response'),
			error: (error) => {
				expect(error).toBe(errorResponse);
				done();
			},
		});
	});

	it('#getPieceList should retrieve piece list with pagination', (done: DoneFn) => {
		const sliId = 'sli-123';
		const pageParams = { pageNum: 0, pageSize: 10, orderByColumn: 'id', isAsc: 'true' };
		const mockPiece = createMockSliPayload().pieces[0];
		const mockPieceList: PieceList = {
			type: mockPiece.type || '',
			pieceId: mockPiece.upid || '',
			productDescription: mockPiece.product.description || '',
			packagingType: mockPiece.packagingType.typeCode,
			grossWeight: mockPiece.grossWeight,
			dimensions: mockPiece.dimensions,
			pieceQuantity: mockPiece.pieceQuantity,
			slac: mockPiece.slac || 0,
		};
		const mockResponse = {
			rows: [mockPieceList],
			total: 1,
		};
		httpClientSpy.get.and.returnValue(of(mockResponse));

		service.getPieceList(pageParams, sliId).subscribe({
			next: (response) => {
				expect(response).toEqual(mockResponse);
				expect(httpClientSpy.get).toHaveBeenCalledWith(jasmine.stringContaining('sli/piece/list'), { params: jasmine.any(Object) });
				done();
			},
			error: done.fail,
		});
	});

	it('#getPieceList should handle error response', (done: DoneFn) => {
		const sliId = 'sli-123';
		const pageParams = { pageNum: 0, pageSize: 10, orderByColumn: 'id', isAsc: 'true' };
		const errorResponse = { status: 500, message: 'Server error' };
		httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

		service.getPieceList(pageParams, sliId).subscribe({
			next: () => done.fail('Expected error but got success response'),
			error: (error) => {
				expect(error).toBe(errorResponse);
				done();
			},
		});
	});
});

// Helper function to create a mock SLI payload
function createMockSliPayload(): SliCreatePayload {
	const shipperParty: ShipmentParty = {
		companyName: 'Test Shipper',
		contactName: 'John Doe',
		countryCode: 'US',
		regionCode: 'CA',
		cityCode: 'SFO',
		textualPostCode: '94105',
		locationName: 'San Francisco',
		phoneNumber: '************',
		emailAddress: '<EMAIL>',
		companyType: 'SHP',
	};

	const consigneeParty: ShipmentParty = {
		companyName: 'Test Consignee',
		contactName: 'Jane Smith',
		countryCode: 'UK',
		regionCode: 'LDN',
		cityCode: 'LON',
		textualPostCode: 'EC1A 1BB',
		locationName: 'London',
		phoneNumber: '************',
		emailAddress: '<EMAIL>',
		companyType: 'CNE',
	};

	const dimensions: Dimensions = {
		length: 100,
		width: 50,
		height: 30,
	};

	const piece: Piece = {
		upid: 'TEST123',
		type: 'Piece',
		product: { description: 'Test Product' },
		packagingType: { typeCode: 'BOX', description: 'Box' },
		packagedIdentifier: 'ID123',
		nvdForCustoms: true,
		nvdForCarriage: true,
		grossWeight: 50,
		dimensions: { length: 20, width: 15, height: 10 },
		pieceQuantity: 1,
		slac: 0,
		shippingMarks: '',
		textualHandlingInstructions: '',
		containedPieces: [],
		containedItems: [],
	};

	const currency: Currency = {
		numericalValue: 1000,
		currencyUnit: 'USD',
	};

	return {
		shipmentParty: [shipperParty, consigneeParty],
		departureLocation: 'SFO',
		arrivalLocation: 'LON',
		shippingInfo: 'Test shipping info',
		goodsDescription: 'Test goods',
		totalGrossWeight: 75,
		totalDimensions: dimensions,
		insuredAmount: currency,
		declaredValueForCarriage: currency,
		declaredValueForCustoms: currency,
		weightValuationIndicator: 'KG',
		textualHandlingInstructions: 'Handle with care',
		incoterms: 'FOB',
		pieces: [piece],
	};
}
