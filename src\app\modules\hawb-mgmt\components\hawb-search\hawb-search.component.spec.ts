import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HawbSearchComponent } from './hawb-search.component';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { DatePipe } from '@angular/common';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { TranslateModule } from '@ngx-translate/core';
import { noop } from 'rxjs';

describe('HawbSearchComponent', () => {
	let component: HawbSearchComponent;
	let fixture: ComponentFixture<HawbSearchComponent>;
	let mockSearchService: Partial<HawbSearchRequestService>;

	beforeEach(async () => {
		mockSearchService = {
			getOptions: jasmine.createSpy('getOptions'),
		};

		await TestBed.configureTestingModule({
			imports: [ReactiveFormsModule, MatDatepickerModule, MatNativeDateModule, TranslateModule.forRoot()],
			providers: [DatePipe, { provide: HawbSearchRequestService, useValue: mockSearchService }],
		}).compileComponents();

		fixture = TestBed.createComponent(HawbSearchComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize the hawbSearchForm', () => {
		expect(component.hawbSearchForm).toBeDefined();
		expect(component.hawbSearchForm.controls['goodsDescription']).toBeDefined();
		expect(component.hawbSearchForm.controls['startDate']).toBeDefined();
		expect(component.hawbSearchForm.controls['endDate']).toBeDefined();
	});

	it('onSearch() the searchHawb emit should be allowed when search button is clicked', () => {
		spyOn(component.searchHawb, 'emit');

		component.onSearch();

		expect(component.searchHawb.emit).toHaveBeenCalled();
	});

	it('onReset() should reset all the form fields and selected items', () => {
		component.hawbSearchForm.patchValue({ goodsDescription: 'test', startDate: new Date(), endDate: new Date() });
		component.selectedShippers = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedConsignees = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedHawbNumbers = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedDepartureLocations = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedArrivalLocations = [{ code: '1', name: 'Test' }] as CodeName[];

		component.onReset({ preventDefault: noop, stopPropagation: noop } as any);

		expect(component.hawbSearchForm.value).toEqual({
			goodsDescription: null,
			startDate: null,
			endDate: null,
		});
		expect(component.selectedShippers).toEqual([]);
		expect(component.selectedConsignees).toEqual([]);
		expect(component.selectedHawbNumbers).toEqual([]);
		expect(component.selectedDepartureLocations).toEqual([]);
		expect(component.selectedArrivalLocations).toEqual([]);
	});

	it('selectedItems() should update the selected items', () => {
		const testItems = [{ code: '1', name: 'Test' }] as CodeName[];

		component.selectedItems(testItems, 'shipper');
		expect(component.selectedShippers).toEqual(testItems);

		component.selectedItems(testItems, 'consignee');
		expect(component.selectedConsignees).toEqual(testItems);

		component.selectedItems(testItems, 'hawbNumber');
		expect(component.selectedHawbNumbers).toEqual(testItems);

		component.selectedItems(testItems, 'origin');
		expect(component.selectedDepartureLocations).toEqual(testItems);

		component.selectedItems(testItems, 'destination');
		expect(component.selectedArrivalLocations).toEqual(testItems);
	});
});
