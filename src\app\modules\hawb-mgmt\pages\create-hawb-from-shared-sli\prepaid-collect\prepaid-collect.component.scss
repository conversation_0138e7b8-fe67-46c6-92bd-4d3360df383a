.orll-prepaid-collect {
	margin-bottom: -4px;
}

.flex-row {
	margin-bottom: 5px;
}

.mb-20-px {
	margin-bottom: 20px;
}

.width-half {
	width: 50%;
}

.rounded-l-md {
	border-bottom-left-radius: 0.375rem;
	border-top-left-radius: 0.375rem;
}

.relative {
	position: relative;
}

.cool-input-container {
	.cool-label {
		color: var(--iata-grey-400);
		position: absolute;
		top: -8px;
		background-color: transparent;
		padding: 0 12px;
		left: 50%;
		z-index: 100;
		transform: translateX(-50%);
	}

	.divider {
		color: var(--iata-grey-400);
		position: absolute;
		top: 0;
		bottom: 0;
		width: 4px;
		height: 100%;
		background-color: #f7f8fc;
		padding: 11px 0;
		left: 50%;
		z-index: 50;
		transform: translate(-50%, -1px);

		display: flex;
		flex-direction: column;

		.divider-content {
			width: 2px;
			border-right: 1px solid var(--iata-grey-200);
			height: 28px;
		}
	}
}

.rounded-r-md {
	border-top-right-radius: 0.375rem;
	border-bottom-right-radius: 0.375rem;
}

.left {
	border-right-width: 0;
}

.right {
	border-left-width: 0;
}

.cool-input {
	display: block;
	width: 100%;
	background-color: rgb(255 255 255);
	padding: 0.375rem 0.75rem;
	font-size: 14px;
	line-height: 1.5rem;
	color: rgb(17 24 39);
	outline: 1px solid var(--iata-grey-200);
	outline-offset: -1px;
	border: none;
	height: 42px;
}

.cool-input::placeholder {
	color: rgb(156 163 175);
}

.cool-input:focus {
	position: relative;
	outline-offset: -2px;
	outline: 2px solid var(--iata-blue-primary);
}

:host {
	input.disabled {
		background-color: var(--iata-grey-50);
	}
}
