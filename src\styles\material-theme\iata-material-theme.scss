@use '@angular/material' as mat;
@use 'iata-mat-palette' as palette;
@use 'iata-mat-buttons' as buttons;
@use 'iata-mat-slide-toggle' as slide-toggle;
@use 'iata-mat-paginator' as paginator;
@use 'iata-mat-dialog' as dialog;
@use 'iata-mat-form-field' as form-field;
@use 'iata-mat-tabs' as tabs;
@use 'iata-mat-card' as card;
@use 'iata-mat-progress-bar' as progress-bar;
@use 'iata-mat-disabled-fields' as disabled-form-field;
@use 'iata-cdk-drag-drop' as drag-and-drop;
@use 'iata-mat-tooltip' as tooltip;
@use 'iata-mat-stepper' as stepper;
@use 'iata-mat-radio-button' as radio-button;
@use 'iata-mat-icon' as icon;
@use 'iata-mat-expansion-panel' as expansion-panel;
@use 'iata-mat-action-list' as action-list;
@use 'iata-mat-menu' as menu;
@use 'iata-mat-badge' as badge;

@include palette.create-css-variables-for-all-palettes();

$iata-mat-palette-primary: mat.m2-define-palette(
		palette.$iata-blue-palette,
		palette.$iata-blue-weight-primary,
		palette.$iata-blue-weight-lighter,
		palette.$iata-blue-weight-darker
);

$iata-mat-palette-accent: mat.m2-define-palette(
		palette.$iata-green-palette,
		palette.$iata-green-weight-primary,
		palette.$iata-green-weight-lighter,
		palette.$iata-green-weight-darker
);

$iata-mat-palette-warn: mat.m2-define-palette(
		palette.$iata-red-palette,
		palette.$iata-red-weight-primary,
		palette.$iata-red-weight-lighter,
		palette.$iata-red-weight-darker
);

$iata-typography: mat.m2-define-typography-config(
	$font-family: 'AktivGrotesk, Arial, Helvetica, sans-serif',
);

$iata-mat-theme: mat.m2-define-light-theme((
	color: (
		primary: $iata-mat-palette-primary,
		accent: $iata-mat-palette-accent,
		warn: $iata-mat-palette-warn,
	),
));

@include mat.all-component-typographies($iata-typography);
@include mat.elevation-classes();
@include mat.app-background();
@include mat.all-component-themes($iata-mat-theme);

@include buttons.iata-buttons-theme($iata-mat-theme);
@include paginator.iata-paginator-theme($iata-mat-theme);
@include slide-toggle.iata-slide-toggle-theme($iata-mat-theme);
@include dialog.iata-dialog-theme($iata-mat-theme);
@include form-field.iata-form-field-theme($iata-mat-theme);
@include tabs.iata-tabs-theme($iata-mat-theme);
@include card.iata-card-theme($iata-mat-theme);
@include progress-bar.iata-progress-bar-theme($iata-mat-theme);
@include disabled-form-field.iata-disabled-fields-theme($iata-mat-theme);
@include drag-and-drop.iata-drag-and-drop-theme($iata-mat-theme);
@include tooltip.iata-tooltip-theme($iata-mat-theme);
@include stepper.iata-stepper-theme($iata-mat-theme);
@include radio-button.iata-radio-button-theme($iata-mat-theme);
@include icon.iata-icon-theme($iata-mat-theme);
@include expansion-panel.iata-expansion-panel-theme($iata-mat-theme);
@include action-list.iata-action-list-theme($iata-mat-theme);
@include menu.iata-menu-theme($iata-mat-theme);
@include badge.iata-badge-theme($iata-mat-theme);
