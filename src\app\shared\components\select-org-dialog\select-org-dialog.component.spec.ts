import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SelectOrgDialogComponent } from './select-org-dialog.component';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { ChangeDetectorRef } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';
import { Organization } from '@shared/models/organization.model';
import { OrgInfo } from '@shared/models/org-info.model';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('SelectOrgDialogComponent', () => {
	let component: SelectOrgDialogComponent;
	let fixture: ComponentFixture<SelectOrgDialogComponent>;
	let mockOrgMgmtRequestService: jasmine.SpyObj<OrgMgmtRequestService>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<SelectOrgDialogComponent>>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;

	const mockOrganizations: Organization[] = [
		{ id: '1', name: 'Org 1', orgType: 'SHP' },
		{ id: '2', name: 'Org 2', orgType: 'CNE' },
		{ id: '3', name: 'Org 3', orgType: 'AIRLINE' },
		{ id: '4', name: 'Org 4', orgType: 'FREIGHT_FORWARDER' },
	];

	const mockOrgInfo: OrgInfo = {
		id: '3',
		companyName: 'Org 3',
		partyRole: 'AIRLINE',
		countryCode: 'US',
		locationName: 'New York',
		regionCode: 'NY',
		textualPostCode: '10001',
		cityCode: 'NYC',
		persons: [],
	};

	describe('with no orgType specified', () => {
		beforeEach(async () => {
			mockOrgMgmtRequestService = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList', 'getOrgInfo']);
			mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
			mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

			await TestBed.configureTestingModule({
				imports: [MatDialogModule, MatListModule, TranslateModule.forRoot(), NoopAnimationsModule],
				providers: [
					{ provide: OrgMgmtRequestService, useValue: mockOrgMgmtRequestService },
					{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
					{ provide: MatDialogRef, useValue: mockDialogRef },
					{ provide: MAT_DIALOG_DATA, useValue: { orgType: null } },
				],
			}).compileComponents();

			fixture = TestBed.createComponent(SelectOrgDialogComponent);
			component = fixture.componentInstance;
		});

		it('should response all organizations when no orgType is specified', () => {
			mockOrgMgmtRequestService.getOrgList.and.returnValue(of(mockOrganizations));
			fixture.detectChanges();

			expect(mockOrgMgmtRequestService.getOrgList).toHaveBeenCalledWith();
			expect(component.orgList).toEqual(mockOrganizations);
		});
	});

	describe('getOrgInfo', () => {
		beforeEach(async () => {
			mockOrgMgmtRequestService = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList', 'getOrgInfo']);
			mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
			mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

			await TestBed.configureTestingModule({
				imports: [MatDialogModule, MatListModule, TranslateModule.forRoot(), NoopAnimationsModule],
				providers: [
					{ provide: OrgMgmtRequestService, useValue: mockOrgMgmtRequestService },
					{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
					{ provide: MatDialogRef, useValue: mockDialogRef },
					{ provide: MAT_DIALOG_DATA, useValue: { orgType: 'AIRLINE' } },
				],
			}).compileComponents();

			fixture = TestBed.createComponent(SelectOrgDialogComponent);
			component = fixture.componentInstance;
			mockOrgMgmtRequestService.getOrgList.and.returnValue(of(mockOrganizations));
			fixture.detectChanges();
		});

		it('should fetch organization info and close dialog with result', () => {
			mockOrgMgmtRequestService.getOrgInfo.and.returnValue(of(mockOrgInfo));

			component.getOrgInfo('3');

			expect(mockOrgMgmtRequestService.getOrgInfo).toHaveBeenCalledWith('3');
			expect(mockDialogRef.close).toHaveBeenCalledWith(mockOrgInfo);
		});
	});
});
