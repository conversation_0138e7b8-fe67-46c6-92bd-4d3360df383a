import { Component, Input } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { FormControl, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
	selector: 'orll-carrier-agent',
	imports: [MatFormFieldModule, MatInput, ReactiveFormsModule, TranslatePipe],
	templateUrl: './carrier-agent.component.html',
	styleUrl: './carrier-agent.component.scss',
})
export class CarrierAgentComponent {
	@Input()
	carrierAgentForm = this.fb.group({
		id: new FormControl<string | null>(null),
		company: [''],
		agentIataCode: ['', [Validators.pattern(/^\d{11}$/g)]],
		country: [''],
		province: [''],
		cityName: [''],
		textualPostCode: [''],
		address: [''],
		phoneNumber: [''],
		email: [''],
	});

	constructor(private readonly fb: NonNullableFormBuilder) {}
}
