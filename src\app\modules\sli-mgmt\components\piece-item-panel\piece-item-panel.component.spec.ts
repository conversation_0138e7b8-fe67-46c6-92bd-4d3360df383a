import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PieceItemPanelComponent } from './piece-item-panel.component';
import { TranslateModule } from '@ngx-translate/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { Piece } from '../../models/piece/piece.model';
import { PieceItem } from '../../models/piece/piece-item.model';
import { By } from '@angular/platform-browser';
import { ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('PieceItemPanelComponent', () => {
	let component: PieceItemPanelComponent;
	let fixture: ComponentFixture<PieceItemPanelComponent>;

	// Mock data
	const mockPieceItem: PieceItem = {
		product: { description: 'Test Item' },
		weight: 10.5,
		itemQuantity: 5,
	};

	const mockPiece: Piece = {
		upid: 'TEST123',
		type: 'TEST',
		product: { description: 'Test Piece', hsCommodityDescription: '' },
		packagingType: { typeCode: 'BOX', description: 'Box' },
		packagedIdentifier: 'ID123',
		nvdForCustoms: false,
		nvdForCarriage: false,
		grossWeight: 50,
		dimensions: { length: 20, width: 15, height: 10 },
		pieceQuantity: 1,
		slac: 0,
		shippingMarks: 'TEST',
		textualHandlingInstructions: 'Handle with care',
		containedPieces: [],
		containedItems: [],
	};

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [PieceItemPanelComponent, TranslateModule.forRoot(), NoopAnimationsModule],
			schemas: [CUSTOM_ELEMENTS_SCHEMA],
		}).compileComponents();

		fixture = TestBed.createComponent(PieceItemPanelComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize with default values', () => {
		expect(component.pieceIndex).toBe(0);
		expect(component.containedPiece).toBeNull();
		expect(component.pieceItemList).toEqual([]);
		expect(component.sliPieceItemForm).toBeDefined();
		expect(component.sliPieceItemForm.get('description')).toBeDefined();
		expect(component.sliPieceItemForm.get('weight')).toBeDefined();
		expect(component.sliPieceItemForm.get('quantity')).toBeDefined();
	});

	it('should validate weight field with decimal pattern', () => {
		const weightControl = component.sliPieceItemForm.get('weight');

		// Invalid values
		weightControl?.setValue('abc');
		expect(weightControl?.valid).toBeFalsy();

		weightControl?.setValue('10.25'); // More than 1 decimal place
		expect(weightControl?.valid).toBeFalsy();

		// Valid values
		weightControl?.setValue('10');
		expect(weightControl?.valid).toBeTruthy();

		weightControl?.setValue('10.5');
		expect(weightControl?.valid).toBeTruthy();
	});

	it('should validate quantity field with positive number pattern', () => {
		const quantityControl = component.sliPieceItemForm.get('quantity');

		// Invalid values
		quantityControl?.setValue('abc');
		expect(quantityControl?.valid).toBeFalsy();

		quantityControl?.setValue('0'); // Not positive
		expect(quantityControl?.valid).toBeFalsy();

		quantityControl?.setValue('-1'); // Negative
		expect(quantityControl?.valid).toBeFalsy();

		quantityControl?.setValue('1.5'); // Decimal
		expect(quantityControl?.valid).toBeFalsy();

		// Valid values
		quantityControl?.setValue('1');
		expect(quantityControl?.valid).toBeTruthy();

		quantityControl?.setValue('10');
		expect(quantityControl?.valid).toBeTruthy();
	});

	it('should add piece item to pieceItemList when containedPiece is null', () => {
		component.sliPieceItemForm.setValue({
			description: 'New Item',
			weight: '15.5',
			quantity: '3',
		});

		component.addPieceItem(0);

		expect(component.pieceItemList.length).toBe(1);
		expect(component.pieceItemList[0].product.description).toBe('New Item');
		expect(component.pieceItemList[0].weight).toBe(15.5);
		expect(component.pieceItemList[0].itemQuantity).toBe(3);

		expect(component.sliPieceItemForm.value.description).toBeNull();
		expect(component.sliPieceItemForm.value.weight).toBeNull();
		expect(component.sliPieceItemForm.value.quantity).toBeNull();
	});

	it('should add piece item to containedPiece when it exists', () => {
		component.containedPiece = { ...mockPiece };
		component.pieceIndex = 1;

		component.sliPieceItemForm.setValue({
			description: 'New Contained Item',
			weight: '8.5',
			quantity: '2',
		});

		component.addPieceItem(1);

		expect(component.containedPiece?.containedItems.length).toBe(1);
		expect(component.containedPiece?.containedItems[0].product.description).toBe('New Contained Item');
		expect(component.containedPiece?.containedItems[0].weight).toBe(8.5);
		expect(component.containedPiece?.containedItems[0].itemQuantity).toBe(2);
	});

	it('should not add piece item when form is invalid', () => {
		component.sliPieceItemForm.setValue({
			description: 'Invalid Item',
			weight: 'abc',
			quantity: '3',
		});

		component.addPieceItem(0);

		expect(component.pieceItemList.length).toBe(0);
	});

	it('should delete piece item from pieceItemList', () => {
		component.pieceItemList = [{ ...mockPieceItem }, { ...mockPieceItem, product: { description: 'Second Item' } }];

		component.delPieceItem(-1, 0);

		expect(component.pieceItemList.length).toBe(1);
		expect(component.pieceItemList[0].product.description).toBe('Second Item');
	});

	it('should delete piece item from containedPiece', () => {
		component.containedPiece = { ...mockPiece };
		component.containedPiece.containedItems = [
			{ ...mockPieceItem },
			{ ...mockPieceItem, product: { description: 'Second Contained Item' } },
		];
		component.pieceIndex = 1;

		component.delPieceItem(1, 0);

		expect(component.containedPiece.containedItems.length).toBe(1);
		expect(component.containedPiece.containedItems[0].product.description).toBe('Second Contained Item');
	});

	it('should emit deletePiece event when delete button is clicked', () => {
		spyOn(component.deletePiece, 'emit');

		component.containedPiece = { ...mockPiece };

		fixture.componentRef.injector.get(ChangeDetectorRef).markForCheck();
		fixture.detectChanges();

		const deleteButton = fixture.debugElement.query(By.css('mat-panel-description .orll-piece-item-panel__delete-button'));

		expect(deleteButton).toBeTruthy();

		// eslint-disable-next-line @typescript-eslint/no-empty-function
		deleteButton.triggerEventHandler('click', { stopPropagation: () => {} });

		expect(component.deletePiece.emit).toHaveBeenCalled();
	});

	it('should return pieceItemList when getPieceItemList is called', () => {
		const testPieceItems = [{ ...mockPieceItem }, { ...mockPieceItem, product: { description: 'Second Item' } }];
		component.pieceItemList = testPieceItems;

		const result = component.getPieceItemList();

		expect(result).toBe(component.pieceItemList);
		expect(result).toEqual(testPieceItems);
		expect(result.length).toBe(2);
	});
});
