@use 'iata-mat-palette' as palette;

@mixin iata-stepper-theme($theme) {
	.mat-step-header {
		.mat-step-icon {
			background-color: #ccc;
		}

		.mat-step-icon-state-done {
			background-color: var(--iata-white);
			border: 1px solid var(--iata-green-primary);
			color: var(--iata-green-primary);
		}

		.mat-step-label {
			color: var(--iata-grey-300);
		}

		.mat-step-icon.mat-step-icon-selected {
			background-color: var(--iata-green-700) !important;
		}

		.mat-step-label.mat-step-label-selected {
			color: var(--iata-grey-500);
		}
	}

	.mat-stepper-horizontal-line {
		display: none;
	}

	.mat-horizontal-stepper-header-container {
		justify-content: space-between;
		padding: 0 var(--iata-gap-l) 0 var(--iata-gap-l);
	}

	.mat-horizontal-stepper-header {
		padding: 0 var(--iata-gap-xs) !important;
	}

	.mat-horizontal-content-container {
		padding: 0 !important;
	}

	.iata-stepper-disable-manual-navigation {
		.mat-horizontal-stepper-header {
			pointer-events: none !important;
		}
	}

	.iata-stepper-centered-header {
		.mat-horizontal-stepper-header-container {
			width: fit-content;
			margin: 0 auto;
			gap: var(--iata-gap-m);
		}
	}
}
