import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { SecondaryUser } from '../../models/secondary-user.model';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatSelectModule } from '@angular/material/select';
import { CodeName } from '@shared/models/code-name.model';
import { USER_TYPES } from '../../ref-data/user-list.data';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { Organization } from '@shared/models/organization.model';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { startWith } from 'rxjs';

@Component({
	selector: 'orll-secondary-user-panel',
	templateUrl: './secondary-user-panel.component.html',
	styleUrl: './secondary-user-panel.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatIconModule,
		MatButtonModule,
		MatExpansionModule,
		MatTooltipModule,
		MatInputModule,
		MatListModule,
		MatDividerModule,
		MatFormFieldModule,
		ReactiveFormsModule,
		FormsModule,
		CommonModule,
		TranslateModule,
		MatSelectModule,
		MatAutocompleteModule,
	],
})
export class SecondaryUserPanelComponent extends DestroyRefComponent implements OnInit {
	@Input() secondaryUserList: SecondaryUser[] = [];

	orgList: Organization[] = [];
	filteredOrgList: Organization[] = [];
	userTypes: CodeName[] = USER_TYPES;

	secondaryUserForm: FormGroup = new FormGroup({
		orgId: new FormControl<string>('', [Validators.required]),
		userType: new FormControl<string>('', [Validators.required]),
	});

	constructor(
		public readonly orgMgmtRequestService: OrgMgmtRequestService,
		private readonly cdr: ChangeDetectorRef
	) {
		super();
	}

	ngOnInit(): void {
		this.initRefData();
	}

	private initRefData(): void {
		this.orgMgmtRequestService
			.getOrgList()
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((res) => {
				this.orgList = res;
				this.setupAutocomplete();
				this.cdr.markForCheck();
			});
	}

	private setupAutocomplete(): void {
		this.secondaryUserForm
			.get('orgId')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredOrgList = this.orgList.filter((org) => org.name.toLowerCase().includes(search?.toLowerCase().trim() ?? ''));
			});
	}

	displayOrgName = (id: string): string => {
		const org = this.orgList.find((item) => item.id === id);
		return org?.name ?? '';
	};

	displayUserType = (code: string): string => {
		const userType = this.userTypes.find((item) => item.code === code);
		return userType?.name ?? '';
	};

	addSecondaryUser(): void {
		this.secondaryUserForm.markAllAsTouched();
		if (this.secondaryUserForm.invalid) {
			return;
		}

		this.secondaryUserList.push({
			orgId: this.secondaryUserForm.value.orgId ?? '',
			orgName: this.displayOrgName(this.secondaryUserForm.value.orgId),
			userType: this.secondaryUserForm.value.userType ?? '',
		});

		this.secondaryUserForm.reset();
	}

	delUserItem(userItemIndex: number): void {
		this.secondaryUserList.splice(userItemIndex, 1);
	}

	getSecondaryUserList(): SecondaryUser[] {
		return this.secondaryUserList;
	}
}
