import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { of, throwError } from 'rxjs';
import { PageEvent } from '@angular/material/paginator';
import { PaginationResponse } from '@shared/models/pagination-response.model';
// eslint-disable-next-line @typescript-eslint/naming-convention
import HawbListPageComponent from './hawb-list-page.component';
import { TranslateModule } from '@ngx-translate/core';
import { ActivatedRoute } from '@angular/router';
import { HawbSearchPayload } from '../../models/hawb-search-payload.model';
import { Sort } from '@angular/material/sort';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { UserProfileService } from '@shared/services/user-profile.service';

describe('HawbListPageComponent', () => {
	let component: HawbListPageComponent;
	let fixture: ComponentFixture<HawbListPageComponent>;
	let hawbSearchRequestServiceSpy: jasmine.SpyObj<HawbSearchRequestService>;
	let cdrSpy: jasmine.SpyObj<ChangeDetectorRef>;
	let mockHawbRecords: any[];
	let mockPaginationResponse: PaginationResponse<any>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;

	beforeEach(() => {
		// Create spies for all required services
		hawbSearchRequestServiceSpy = jasmine.createSpyObj('HawbSearchRequestService', ['getHawbList']);
		cdrSpy = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

		// Setup mock data
		mockHawbRecords = [{ id: 1 }, { id: 2 }, { id: 3 }];
		mockPaginationResponse = {
			rows: mockHawbRecords,
			total: mockHawbRecords.length,
			pageNum: 1,
		} as PaginationResponse<any>;

		// Configure default spy behavior
		hawbSearchRequestServiceSpy.getHawbList.and.returnValue(of(mockPaginationResponse));
	});

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [HawbListPageComponent, TranslateModule.forRoot()],
			providers: [
				{
					provide: ActivatedRoute,
					useClass: class ActivatedRouteMock {},
				},
				{
					provide: HawbSearchRequestService,
					useValue: hawbSearchRequestServiceSpy,
				},
				{
					provide: ChangeDetectorRef,
					useValue: cdrSpy,
				},
				{
					provide: UserProfileService,
					useValue: userProfileServiceSpy,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA],
		}).compileComponents();

		fixture = TestBed.createComponent(HawbListPageComponent);
		component = fixture.componentInstance;
		component['hawbSearchPayload'] = {} as HawbSearchPayload;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should retrieve the hawb list page with default pagination parameters', () => {
			spyOn<HawbListPageComponent, any>(component, 'getHawbListPage');

			component.ngOnInit();

			expect(component['getHawbListPage']).toHaveBeenCalledWith({ pageNum: 1, pageSize: 10 });
		});
	});

	describe('onHawbListPageChange', () => {
		it('should update pagination parameters and retrieve the requested page', () => {
			spyOn<HawbListPageComponent, any>(component, 'getHawbListPage');
			const pageEvent: PageEvent = { pageIndex: 1, previousPageIndex: 0, pageSize: 10, length: 30 };

			component.onHawbListPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(2);
			expect(component.pageParams.pageSize).toBe(10);
			expect(component['getHawbListPage']).toHaveBeenCalledWith({ pageNum: 2, pageSize: 10 });
		});
	});

	describe('onSearch', () => {
		it('should update search payload and retrieve hawb list with current pagination', () => {
			spyOn<HawbListPageComponent, any>(component, 'getHawbListPage');
			const mockSearchPayload: HawbSearchPayload = { searchTerm: 'test' } as HawbSearchPayload;

			component.onSearch(mockSearchPayload);

			expect(component.hawbSearchPayload).toBe(mockSearchPayload);
			expect(component['getHawbListPage']).toHaveBeenCalledWith(component.pageParams);
		});
	});

	describe('onHawbListSortChange', () => {
		it('should update sort parameters when sort direction is provided', () => {
			const sortEvent: Sort = { active: 'createdAt', direction: 'asc' };

			component.onHawbListSortChange(sortEvent);

			expect(component.pageParams.orderByColumn).toBe('createdAt');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should clear sort parameters when sort direction is empty', () => {
			// First set some values
			component.pageParams.orderByColumn = 'createdAt';
			component.pageParams.isAsc = 'asc';

			const sortEvent: Sort = { active: 'createdAt', direction: '' };

			component.onHawbListSortChange(sortEvent);

			expect(component.pageParams.orderByColumn).toBe('');
			expect(component.pageParams.isAsc).toBe('');
		});
	});

	describe('getHawbListPage', () => {
		it('should set dataLoading to true and clear hawbList before making the request', () => {
			// Setup
			component.dataLoading = false;
			component.hawbList = [{} as any];

			// We need to modify the spy to capture the state before the observable completes
			hawbSearchRequestServiceSpy.getHawbList.and.callFake(() => {
				// Check state right after the method call but before observable completes
				expect(component.dataLoading).toBeTrue();
				expect(component.hawbList.length).toBe(0);
				return of(mockPaginationResponse);
			});

			// Execute
			component['getHawbListPage']({ pageNum: 1, pageSize: 10 });

			// Verify
			expect(hawbSearchRequestServiceSpy.getHawbList).toHaveBeenCalled();
		});

		it('should retrieve a page of hawb list and update component properties on success', () => {
			// Execute
			component['getHawbListPage']({ pageNum: 1, pageSize: 10 });

			// Verify
			expect(hawbSearchRequestServiceSpy.getHawbList).toHaveBeenCalledWith({ pageNum: 1, pageSize: 10 }, {});
			expect(component.hawbList).toEqual(mockHawbRecords);
			expect(component.hawbListTotalRecords).toEqual(mockHawbRecords.length);
			expect(component.dataLoading).toBeFalse();
		});

		it('should handle errors and display a snackbar notification', () => {
			const errorResponse = { message: 'something went wrong' };
			hawbSearchRequestServiceSpy.getHawbList.and.returnValue(throwError(() => errorResponse));

			component['getHawbListPage']({ pageNum: 1, pageSize: 10 });

			expect(component.dataLoading).toBeFalse();
		});
	});
});
