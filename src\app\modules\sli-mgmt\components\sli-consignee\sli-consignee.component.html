<div class="orll-sli-consignee-box">
	@if (title) {
		<div class="orll-sli-consignee-box__header">
			<h2 class="mat-display-2 orll-sli-consignee-box__title">
				{{'sli.mgmt.company.' + title | translate}} *
			</h2>
			<div class="orll-sli-consignee-box__header__right">
				<button mat-icon-button color="primary" (click)="getOrgList()" class="orll-sli-consignee-box__header__contact-button">
					<mat-icon>contacts</mat-icon>
				</button>
			</div>
		</div>
	}
	<div class="row">
		<div class="col-6">
			<div class="orll-sli-consignee-box__label">{{'sli.mgmt.company.companyName' | translate}}</div>
			<div>{{shipmentParty?.companyName}}</div>
		</div>

		<div class="col-6">
			<div class="orll-sli-consignee-box__label">{{'sli.mgmt.company.contactName' | translate}}</div>
			<div>{{shipmentParty?.contactName}}</div>
		</div>
	</div>

	<div class="row">
		<div class="col-3">
			<div class="orll-sli-consignee-box__label">{{'sli.mgmt.company.country' | translate}}</div>
			<div>{{shipmentParty?.countryCode}}</div>
		</div>

		<div class="col-3">
			<div class="orll-sli-consignee-box__label">{{'sli.mgmt.company.province' | translate}}</div>
			<div>{{shipmentParty?.regionCode}}</div>
		</div>

		<div class="col-3">
			<div class="orll-sli-consignee-box__label">{{'sli.mgmt.company.city' | translate}}</div>
			<div>{{shipmentParty?.cityCode}}</div>
		</div>

		<div class="col-3">
			<div class="orll-sli-consignee-box__label">{{'sli.mgmt.company.textualPostCode' | translate}}</div>
			<div>{{shipmentParty?.textualPostCode}}</div>
		</div>
	</div>

	<div class="row">
		<div class="col-6">
			<div class="orll-sli-consignee-box__label">{{'sli.mgmt.company.address' | translate}}</div>
			<div>{{shipmentParty?.locationName}}</div>
		</div>

		<div class="col-3">
			<div class="orll-sli-consignee-box__label">{{'sli.mgmt.company.phoneNumber' | translate}}</div>
			<div>{{shipmentParty?.phoneNumber}}</div>
		</div>

		<div class="col-3">
			<div class="orll-sli-consignee-box__label">{{'sli.mgmt.company.emailAddress' | translate}}</div>
			<div>{{shipmentParty?.emailAddress}}</div>
		</div>
	</div>
</div>
