import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SecondaryUserPanelComponent } from './secondary-user-panel.component';
import { TranslateModule } from '@ngx-translate/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SecondaryUser } from '../../models/secondary-user.model';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { of } from 'rxjs';

describe('SecondaryUserPanelComponent', () => {
	let component: SecondaryUserPanelComponent;
	let fixture: ComponentFixture<SecondaryUserPanelComponent>;
	let orgMgmtRequestServiceSpy: jasmine.SpyObj<OrgMgmtRequestService>;

	// Mock data
	const mockSecondaryUser: SecondaryUser = {
		orgId: 'Test Item',
		orgName: 'Test Org',
		userType: '1',
	};

	beforeEach(async () => {
		orgMgmtRequestServiceSpy = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList']);
		orgMgmtRequestServiceSpy.getOrgList.and.returnValue(of([]));

		await TestBed.configureTestingModule({
			imports: [SecondaryUserPanelComponent, TranslateModule.forRoot(), NoopAnimationsModule],
			schemas: [CUSTOM_ELEMENTS_SCHEMA],
			providers: [{ provide: OrgMgmtRequestService, useValue: orgMgmtRequestServiceSpy }],
		}).compileComponents();

		fixture = TestBed.createComponent(SecondaryUserPanelComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should call getOrgList on init', () => {
		expect(orgMgmtRequestServiceSpy.getOrgList).toHaveBeenCalled();
	});

	it('should initialize with default values', () => {
		expect(component.secondaryUserList).toEqual([]);
		expect(component.secondaryUserForm).toBeDefined();
		expect(component.secondaryUserForm.get('orgId')).toBeDefined();
		expect(component.secondaryUserForm.get('userType')).toBeDefined();
	});

	it('should add user item to secondaryUserList when addSecondaryUser is called with valid form', () => {
		spyOn(component, 'displayOrgName').and.returnValue('New Org');
		component.secondaryUserForm.setValue({
			orgId: 'New Item',
			userType: '1',
		});
		component.addSecondaryUser();
		expect(component.secondaryUserList.length).toBe(1);
		expect(component.secondaryUserList[0].orgId).toBe('New Item');
		expect(component.secondaryUserList[0].orgName).toBe('New Org');
		expect(component.secondaryUserList[0].userType).toBe('1');
		// Form should be reset
		expect(component.secondaryUserForm.value.orgId).toBeNull();
		expect(component.secondaryUserForm.value.userType).toBeNull();
	});

	it('should not add user item when form is invalid', () => {
		component.secondaryUserForm.setValue({
			orgId: '',
			userType: '1',
		});
		component.addSecondaryUser();
		expect(component.secondaryUserList.length).toBe(0);
	});

	it('should delete user item from secondaryUserList', () => {
		component.secondaryUserList = [{ ...mockSecondaryUser }, { ...mockSecondaryUser, orgId: 'Second Item' }];
		component.delUserItem(0);
		expect(component.secondaryUserList.length).toBe(1);
		expect(component.secondaryUserList[0].orgId).toBe('Second Item');
	});

	it('should return secondaryUserList when getSecondaryUserList is called', () => {
		const testUserItems = [{ ...mockSecondaryUser }, { ...mockSecondaryUser, orgId: 'Second Item' }];
		component.secondaryUserList = testUserItems;
		const result = component.getSecondaryUserList();
		expect(result).toBe(component.secondaryUserList);
		expect(result).toEqual(testUserItems);
		expect(result.length).toBe(2);
	});

	it('should display org name using displayOrgName', () => {
		component.orgList = [{ id: 'org1', name: 'Org One' } as any];
		expect(component.displayOrgName('org1')).toBe('Org One');
		expect(component.displayOrgName('notfound')).toBe('');
	});

	it('should display user type using displayUserType', () => {
		component.userTypes = [{ code: 'admin', name: 'Admin' } as any];
		expect(component.displayUserType('admin')).toBe('Admin');
		expect(component.displayUserType('notfound')).toBe('');
	});
});
