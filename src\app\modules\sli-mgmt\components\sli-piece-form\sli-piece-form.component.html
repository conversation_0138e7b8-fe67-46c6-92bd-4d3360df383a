<div class="orll-piece-add-form">
	<form [formGroup]="sliPieceForm">
		<h2 class="mat-display-2 orll-piece-add-form__title">{{'sli.piece.title' | translate}}</h2>
		<div class="row">
			<div class="col-6">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.piece.productDescription' | translate}}</mat-label>
					<textarea matInput formControlName="productDescription" rows="4"></textarea>
					@if (sliPieceForm.get('productDescription')?.hasError('required')) {
						<mat-error>{{'sli.piece.productDescription.required' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>
			<div class="col-3">
				<mat-form-field appearance="outline" class="width-100 gross-weight" floatLabel="always">
					<mat-label>{{'sli.piece.grossWeight' | translate}}</mat-label>
					<input matInput formControlName="grossWeight" required>
					<span matSuffix class="unit">KG</span>
					@if (sliPieceForm.get('grossWeight')?.hasError('required')) {
						<mat-error>{{'sli.piece.grossWeight.required' | translate}}</mat-error>
					}
					@if (sliPieceForm.get('grossWeight')?.hasError('pattern')) {
						<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>
			<div class="col-3">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.piece.upid' | translate}}</mat-label>
					<input matInput formControlName="upid">
				</mat-form-field>
			</div>
			<div class="offset-6 col-6 dimensions">
				<div class="lwh">
					<mat-form-field appearance="outline" class="width-100" floatLabel="always">
						<mat-label>{{'sli.piece.dimensions' | translate}}</mat-label>
						<input matInput formControlName="dimLength" placeholder="{{'sli.mgmt.pieceList.dimLength' | translate}}" required>
						<span matSuffix class="unit">CM</span>
						@if (sliPieceForm.get('dimLength')?.hasError('required')) {
							<mat-error>{{'sli.mgmt.pieceList.dimLength.required' | translate}}</mat-error>
						}
						@if (sliPieceForm.get('dimLength')?.hasError('pattern')) {
							<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="lwh">
					<mat-form-field appearance="outline" class="width-100" floatLabel="always">
						<input matInput formControlName="dimWidth" placeholder="{{'sli.mgmt.pieceList.dimWidth' | translate}}" required>
						<span matSuffix class="unit">CM</span>
						@if (sliPieceForm.get('dimWidth')?.hasError('required')) {
							<mat-error>{{'sli.mgmt.pieceList.dimWidth.required' | translate}}</mat-error>
						}
						@if (sliPieceForm.get('dimWidth')?.hasError('pattern')) {
							<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="lwh">
					<mat-form-field appearance="outline" class="width-100" floatLabel="always">
						<input matInput formControlName="dimHeight" placeholder="{{'sli.mgmt.pieceList.dimHeight' | translate}}" required>
						<span matSuffix class="unit">CM</span>
						@if (sliPieceForm.get('dimHeight')?.hasError('required')) {
							<mat-error>{{'sli.mgmt.pieceList.dimHeight.required' | translate}}</mat-error>
						}
						@if (sliPieceForm.get('dimHeight')?.hasError('pattern')) {
							<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
						}
					</mat-form-field>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-2">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
					<mat-label>{{'sli.piece.packagingType' | translate}}</mat-label>
					<input type="text" matInput
						formControlName="packagingType"
						[matAutocomplete]="autoPackagingType">
					<mat-autocomplete #autoPackagingType="matAutocomplete" [displayWith]="displayPackagingTypeName">
						@for (packagingTypes of filteredPackagingTypes; track packagingTypes) {
							<mat-option [value]="packagingTypes.code">{{packagingTypes.name}}</mat-option>
						}
					</mat-autocomplete>
					@if (sliPieceForm.get('packagingType')?.hasError('required')) {
						<mat-error>{{'sli.piece.packagingType.required' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>
			<div class="col-4">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.piece.packagedIdentifier' | translate}}</mat-label>
					<input matInput formControlName="packagedIdentifier">
				</mat-form-field>
			</div>
			<div class="col-6">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.piece.hsCommodityDescription' | translate}}</mat-label>
					<input matInput formControlName="hsCommodityDescription">
				</mat-form-field>
			</div>
		</div>

		<div class="row">
			<div class="col-3">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.piece.nvdForCustoms' | translate}}</mat-label>
					<mat-select formControlName="nvdForCustoms">
						@for (item of nvdForCustoms; track item) {
							<mat-option [value]="item">{{item}}</mat-option>
						}
					</mat-select>
				</mat-form-field>
			</div>
			<div class="col-3">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.piece.nvdForCarriage' | translate}}</mat-label>
					<mat-select formControlName="nvdForCarriage">
						@for (item of nvdForCarriage; track item) {
							<mat-option [value]="item">{{item}}</mat-option>
						}
					</mat-select>
				</mat-form-field>
			</div>
			<div class="col-3">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.piece.shippingMarks' | translate}}</mat-label>
					<input matInput formControlName="shippingMarks">
				</mat-form-field>
			</div>
			<div class="col-3">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.piece.textualHandlingInstructions' | translate}}</mat-label>
					<input matInput formControlName="textualHandlingInstructions">
				</mat-form-field>
			</div>
		</div>
	</form>
</div>
