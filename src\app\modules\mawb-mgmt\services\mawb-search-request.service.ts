import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { CodeName } from '@shared/models/code-name.model';
import { SearchType } from '@shared/models/search-type.model';
import { map } from 'rxjs/operators';
import { ApiService } from '@shared/services/api.service';
import { AbstractAutocompleteService } from '@shared/models/autocomplete.model';
import { HttpClient } from '@angular/common/http';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { MawbSearchPayload } from '../models/mawb-search-payload.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { MawbListObject } from '../models/mawb-list-object.model';

@Injectable({
	providedIn: 'root',
})
export class MawbSearchRequestService extends ApiService implements AbstractAutocompleteService<CodeName> {
	constructor(http: HttpClient) {
		super(http);
	}

	keywordQuery(keyword: string, queryKey: string): Observable<CodeName[]> {
		return super
			.getData<string[]>('mawb-management/get-keyword', {
				keyword,
				keywordType: queryKey,
			})
			.pipe(
				map((res: string[]) => {
					return res.map((item) => ({ code: item, name: item }));
				})
			);
	}

	getOptions(keyword: string, id?: string): Observable<CodeName[]> {
		switch (id) {
			case SearchType.AIRLINE_CODE: {
				return this.keywordQuery(keyword, 'airlineCode');
			}

			case SearchType.MAWB: {
				return this.keywordQuery(keyword, 'mawbNumber');
			}

			case SearchType.LATEST_STATUS: {
				return this.keywordQuery(keyword, 'latestStatus');
			}

			case SearchType.ORIGIN: {
				return this.keywordQuery(keyword, 'origin');
			}

			case SearchType.DESTINATION: {
				return this.keywordQuery(keyword, 'destination');
			}

			default:
				return of([]);
		}
	}

	getMawbList(pageParams: PaginationRequest, mawbSearchPayload: MawbSearchPayload): Observable<PaginationResponse<MawbListObject>> {
		return super.getData<PaginationResponse<MawbListObject>>('mawb-management', {
			...pageParams,
			...mawbSearchPayload,
		});
	}
}
