import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, Input, OnChanges, SimpleChanges, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { TranslateModule } from '@ngx-translate/core';
import { PieceItemPanelComponent } from '../piece-item-panel/piece-item-panel.component';
import { PieceInPanelComponent } from '../piece-in-panel/piece-in-panel.component';
import { Piece } from '../../models/piece/piece.model';
import { PieceItem } from '../../models/piece/piece-item.model';

@Component({
	selector: 'orll-sli-piece-item',
	templateUrl: './sli-piece-item.component.html',
	styleUrl: './sli-piece-item.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatRadioModule, FormsModule, TranslateModule, PieceItemPanelComponent, PieceInPanelComponent],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SliPieceItemComponent implements OnChanges {
	@Input() pieceType = '';
	@Input() piece: Piece | null = null;

	@ViewChild(PieceItemPanelComponent) pieceItemPanel!: PieceItemPanelComponent;
	@ViewChild(PieceInPanelComponent) pieceInPanel: PieceInPanelComponent | undefined;

	isContained = 'no';

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['piece']) {
			this.isContained = this.piece?.containedPieces?.length ? 'yes' : 'no';
		}
	}

	getPieceItemList(): PieceItem[] {
		return this.pieceItemPanel?.getPieceItemList() ?? [];
	}

	getPieceInList(): Piece[] {
		return this.pieceInPanel?.getPieceInList() ?? [];
	}
}
