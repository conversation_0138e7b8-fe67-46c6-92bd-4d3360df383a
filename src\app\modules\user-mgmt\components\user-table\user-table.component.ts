import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { MatPaginatorModule, MatPaginatorIntl, PageEvent } from '@angular/material/paginator';
import { CustomPaginatorIntl } from '@shared/services/custom-paginator-intl.service';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { UserListObject } from '../../models/user-list-object.model';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatSortModule, Sort } from '@angular/material/sort';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { MatInputModule } from '@angular/material/input';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDialog } from '@angular/material/dialog';
import { AddUserDialogComponent } from '../add-user-dialog/add-user-dialog.component';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { UserMgmtRequestService } from '../../services/user-mgmt-request.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { USER_TYPES } from '../../ref-data/user-list.data';
import { CodeName } from '@shared/models/code-name.model';

@Component({
	selector: 'orll-user-table',
	templateUrl: './user-table.component.html',
	styleUrls: ['./user-table.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatTableModule,
		MatSortModule,
		MatButtonModule,
		MatMenuModule,
		MatIconModule,
		MatInputModule,
		ReactiveFormsModule,
		FormsModule,
		MatFormFieldModule,
		MatPaginatorModule,
		TranslateModule,
	],
	providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }],
})
export class UserTableComponent extends DestroyRefComponent implements OnChanges {
	@Input() records: UserListObject[] = [];
	@Input() totalRecords = 0;
	@Input() pageParams!: PaginationRequest;

	@Output() searchUser = new EventEmitter<string>();
	@Output() sortChange = new EventEmitter<Sort>();
	@Output() pagination = new EventEmitter<PageEvent & { sortField?: string; sortDirection?: string }>();

	keyword = new FormControl<string>('');
	userTypes: CodeName[] = USER_TYPES;

	currentSort: Sort = { active: '', direction: '' };

	displayedColumns: string[] = ['email', 'firstName', 'lastName', 'orgName', 'primaryOrgName', 'userTypeText', 'actions'];
	readonly tablePageSizes: number[] = [10, 50, 100];

	dataSource = new MatTableDataSource<UserListObject>(this.records || []);

	constructor(
		private readonly dialog: MatDialog,
		private readonly translate: TranslateService,
		private readonly userMgmtRequestService: UserMgmtRequestService
	) {
		super();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['records']) {
			this.dataSource.data = this.records;
		}
	}

	onSortChange(sort: Sort): void {
		this.currentSort = sort;
		this.sortChange.emit(sort);
		this.emitPaginationWithSort();
	}

	private emitPaginationWithSort(event?: PageEvent) {
		const pageEvent = event || {
			pageIndex: this.pageParams.pageNum - 1,
			pageSize: this.pageParams.pageSize,
			length: this.totalRecords,
		};

		this.pagination.emit({
			...pageEvent,
			sortField: this.currentSort.active,
			sortDirection: this.currentSort.direction,
		});
	}

	private openUserDialog(userId?: string): void {
		const dialogRef = this.dialog.open(AddUserDialogComponent, {
			width: '800px',
			data: {
				userId,
			},
		});

		dialogRef.afterClosed().subscribe(() => {
			this.searchUser.emit(this.keyword?.value ?? '');
		});
	}

	createUser(): void {
		this.openUserDialog();
	}

	editUser(event: Event, userId: string): void {
		event.stopPropagation();
		this.openUserDialog(userId);
	}

	deleteUser(event: Event, userId: string): void {
		event.stopPropagation();

		const dialogRef = this.dialog.open(ConfirmDialogComponent, {
			width: '300px',
			data: {
				content: this.translate.instant('common.dialog.delete.content'),
			},
		});

		dialogRef.afterClosed().subscribe((confirmed) => {
			if (confirmed) {
				this.userMgmtRequestService
					.deleteUser(userId)
					.pipe(takeUntilDestroyed(this.destroyRef))
					.subscribe(() => this.searchUser.emit(this.keyword?.value ?? ''));
			}
		});
	}

	trackByUserId(record: UserListObject): string {
		return record.userId;
	}
}
