<div class="orll-sli-piece-list">
	<form [formGroup]="sliPieceListForm">
		<div class="row">
			<div class="col-12">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.mgmt.pieceList.goodsDescription' | translate}}</mat-label>
					<textarea matInput formControlName="goodsDescription" rows="4" required></textarea>
					@if (sliPieceListForm.get('goodsDescription')?.hasError('required')) {
						<mat-error>{{'sli.mgmt.pieceList.goodsDescription.required' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>
		</div>

		<div class="row margin-b-20">
			<div class="col-12">
				<orll-sli-piece-table
					[records]="pieceList"
					[totalRecords]="totalRecords"
					[pageParams]="pageParams"
					[sliNumber]="sliNumber"
					(saveRequest)="saveRequest.emit($event)"
					(refresh)="refreshData()"
					(sortChange)="onSortChange($event)"
					(pagination)="onPageChange($event)">
				</orll-sli-piece-table>
			</div>
		</div>

		<div class="row margin-auto">
			<div class="width-10">
				<mat-form-field appearance="outline" floatLabel="always">
					<mat-label>{{'sli.mgmt.pieceList.declaredValueForCustoms' | translate}}</mat-label>
					<input matInput formControlName="declaredValueForCustoms">
					@if (sliPieceListForm.get('declaredValueForCustoms')?.hasError('pattern')) {
						<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber2NCV' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>
			<div class="col-1 declared-value">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<input type="text" matInput
						formControlName="declaredValueForCustomsCurrency"
						[matAutocomplete]="autoCustomsCurrency">
					<mat-autocomplete #autoCustomsCurrency="matAutocomplete">
						@for (currency of filteredDeclaredCustomsCurrency; track currency) {
							<mat-option [value]="currency">{{currency}}</mat-option>
						}
					</mat-autocomplete>
					<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
				</mat-form-field>
			</div>

			<div class="width-10">
				<mat-form-field appearance="outline" floatLabel="always">
					<mat-label>{{'sli.mgmt.pieceList.declaredValueForCarriage' | translate}}</mat-label>
					<input matInput formControlName="declaredValueForCarriage">
					@if (sliPieceListForm.get('declaredValueForCarriage')?.hasError('pattern')) {
						<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber2NVD' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>
			<div class="col-1 declared-value">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<input type="text" matInput
						formControlName="declaredValueForCarriageCurrency"
						[matAutocomplete]="autoCarriageCurrency">
					<mat-autocomplete #autoCarriageCurrency="matAutocomplete">
						@for (currency of filteredDeclaredCarriageCurrency; track currency) {
							<mat-option [value]="currency">{{currency}}</mat-option>
						}
					</mat-autocomplete>
					<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
				</mat-form-field>
			</div>

			<div class="width-10">
				<mat-form-field appearance="outline" floatLabel="always">
					<mat-label>{{'sli.mgmt.pieceList.insuredAmount' | translate}}</mat-label>
					<input matInput formControlName="insuredAmount">
					@if (sliPieceListForm.get('insuredAmount')?.hasError('pattern')) {
						<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber2NIL' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>
			<div class="col-1 declared-value">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<input type="text" matInput
						formControlName="insuredAmountCurrency"
						[matAutocomplete]="autoInsuredAmountCurrency">
					<mat-autocomplete #autoInsuredAmountCurrency="matAutocomplete">
						@for (currency of filteredInsuredAmountCurrency; track currency) {
							<mat-option [value]="currency">{{currency}}</mat-option>
						}
					</mat-autocomplete>
					<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
				</mat-form-field>
			</div>

			<div class="width-13 total-gross-weight">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.mgmt.pieceList.totalGrossWeight' | translate}}</mat-label>
					<input matInput formControlName="totalGrossWeight" required>
					<span matSuffix class="unit">KG</span>
					@if (sliPieceListForm.get('totalGrossWeight')?.hasError('required')) {
						<mat-error>{{'sli.mgmt.pieceList.totalGrossWeight.required' | translate}}</mat-error>
					}
					@if (sliPieceListForm.get('totalGrossWeight')?.hasError('pattern')) {
						<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>

			<div class="width-10 total-dimensions">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.mgmt.pieceList.totalDimensions' | translate}}</mat-label>
					<input matInput formControlName="dimLength" placeholder="{{'sli.mgmt.pieceList.dimLength' | translate}}" required>
					<span matSuffix class="unit">CM</span>
					@if (sliPieceListForm.get('dimLength')?.hasError('required')) {
						<mat-error>{{'sli.mgmt.pieceList.dimLength.required' | translate}}</mat-error>
					}
					@if (sliPieceListForm.get('dimLength')?.hasError('pattern')) {
						<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>
			<div class="width-10 total-dimensions">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<input matInput formControlName="dimWidth" placeholder="{{'sli.mgmt.pieceList.dimWidth' | translate}}" required>
					<span matSuffix class="unit">CM</span>
					@if (sliPieceListForm.get('dimWidth')?.hasError('required')) {
						<mat-error>{{'sli.mgmt.pieceList.dimWidth.required' | translate}}</mat-error>
					}
					@if (sliPieceListForm.get('dimWidth')?.hasError('pattern')) {
						<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>
			<div class="width-10 total-dimensions">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<input matInput formControlName="dimHeight" placeholder="{{'sli.mgmt.pieceList.dimHeight' | translate}}" required>
					<span matSuffix class="unit">CM</span>
					@if (sliPieceListForm.get('dimHeight')?.hasError('required')) {
						<mat-error>{{'sli.mgmt.pieceList.dimHeight.required' | translate}}</mat-error>
					}
					@if (sliPieceListForm.get('dimHeight')?.hasError('pattern')) {
						<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>
		</div>

		<div class="row margin-t-20">
			<div class="col-9">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.mgmt.pieceList.textualHandlingInstructions' | translate}}</mat-label>
					<textarea matInput formControlName="textualHandlingInstructions" rows="4"></textarea>
				</mat-form-field>
			</div>

			<div class="col-3">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.mgmt.pieceList.weightValuationIndicator' | translate}}</mat-label>
					<mat-select formControlName="weightValuationIndicator">
						@for (wvi of weightValuationIndicators; track wvi) {
							<mat-option [value]="wvi">{{wvi}}</mat-option>
						}
					</mat-select>
				</mat-form-field>
			</div>

			<div class="offset-9 col-3 incoterms">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-label>{{'sli.mgmt.pieceList.incoterms' | translate}}</mat-label>
					<mat-select formControlName="incoterms">
						@for (incoterm of incoterms; track incoterm) {
							<mat-option [value]="incoterm.code">{{incoterm.name}}</mat-option>
						}
					</mat-select>
				</mat-form-field>
			</div>
		</div>
	</form>
</div>
