import { CommonSort } from './sort.model';
import { CommonSortDirection } from './sort-direction.model';
import { deepCopy } from '../common.utils';
import { SortDirection } from '@angular/material/sort';

export class SortingHelperUtils {
	public static sortByString(data: any[], sorting: CommonSort): any[] {
		const order = this.getSortingOrderMultiplier(sorting);
		const result = deepCopy(data);

		return result.sort((prev: any, next: any) => {
			const prevField: string | undefined = prev[sorting.field];
			const nextField: string | undefined = next[sorting.field];

			const emptyFieldsResult = this.checkEmptyFields(prevField, nextField);
			if (emptyFieldsResult !== null) {
				return emptyFieldsResult * order;
			}

			return prevField!.localeCompare(nextField!, undefined, { numeric: true, sensitivity: 'base' }) * order;
		});
	}

	public static sortByNumber(data: any[], sorting: CommonSort): any[] {
		const order = this.getSortingOrderMultiplier(sorting);
		const result = deepCopy(data);

		return result.sort((prev: any, next: any) => {
			const prevField: number | undefined = prev[sorting.field] ? Number(prev[sorting.field]) : undefined;
			const nextField: number | undefined = next[sorting.field] ? Number(next[sorting.field]) : undefined;

			const emptyFieldsResult = this.checkEmptyFields(prevField, nextField);
			if (emptyFieldsResult !== null) {
				return emptyFieldsResult * order;
			}

			return (prevField! - nextField!) * order;
		});
	}

	public static sortByBoolean(data: any[], sorting: CommonSort): any[] {
		const order = this.getSortingOrderMultiplier(sorting);
		const result = deepCopy(data);

		return result.sort((prev: any, next: any) => {
			const prevField: boolean | undefined = prev[sorting.field] ? Boolean(prev[sorting.field]) : undefined;
			const nextField: boolean | undefined = next[sorting.field] ? Boolean(next[sorting.field]) : undefined;

			const emptyFieldsResult = this.checkEmptyFields(prevField, nextField);
			if (emptyFieldsResult !== null) {
				return emptyFieldsResult * order;
			}

			if (prevField === true && nextField === false) {
				return order;
			} else if (prevField === false && nextField === true) {
				return -order;
			} else {
				return order;
			}
		});
	}

	public static transformMaterialSort(fieldKey: string, materialDirection: SortDirection): CommonSort {
		return { field: fieldKey, direction: SortingHelperUtils.transformSortDirection(materialDirection) };
	}

	public static getSortingOrderMultiplier(sorting: CommonSort): number {
		return sorting.direction === CommonSortDirection.ASC ? 1 : -1;
	}

	/**
	 * @return - 'null' if both fields are non-empty, otherwise - the sorting result
	 */
	public static checkEmptyFields(
		prevField: string | number | boolean | undefined,
		nextField: string | number | boolean | undefined
	): number | null {
		if (!!prevField && !!nextField) {
			return null;
		} else if (!prevField && !!nextField) {
			return 1;
		} else if (!nextField && !!prevField) {
			return -1;
		} else {
			return 0;
		}
	}

	private static transformSortDirection(direction: SortDirection): CommonSortDirection {
		if (direction === 'asc') {
			return CommonSortDirection.ASC;
		} else if (direction === 'desc') {
			return CommonSortDirection.DESC;
		}

		return CommonSortDirection.NONE;
	}
}
