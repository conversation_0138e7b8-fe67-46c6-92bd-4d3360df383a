import { FormArray } from '@angular/forms';

export function deepCopy(obj: any): any {
	return JSON.parse(JSON.stringify(obj));
}

export function downloadBinaryFile(res: any, filename: string): void {
	if (!res?.body?.size) return;

	const blob = new Blob([res.body], { type: res.body.type });
	const url = window.URL.createObjectURL(blob);
	const a = document.createElement('a');
	a.href = url;
	a.download = filename;
	a.click();
	window.URL.revokeObjectURL(url);
}

export function roundNumber(num: number, decimalDigits: number): number {
	// @ts-expect-error any
	return +(Math.round(num + `e+${decimalDigits}`) + `e-${decimalDigits}`);
}

export function clearFormArray(formArray: FormArray): void {
	while (formArray.length !== 0) {
		formArray.removeAt(0);
	}
}
