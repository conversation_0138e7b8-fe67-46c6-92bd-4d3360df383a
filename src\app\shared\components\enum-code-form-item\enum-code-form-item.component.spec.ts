import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EnumCodeFormItemComponent } from './enum-code-form-item.component';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';

describe('EnumCodeFormItemComponent', () => {
	let component: EnumCodeFormItemComponent;
	let fixture: ComponentFixture<EnumCodeFormItemComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [EnumCodeFormItemComponent],
			providers: [
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				provideTranslateService(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(EnumCodeFormItemComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
