import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MatPaginatorModule, MatPaginatorIntl, PageEvent } from '@angular/material/paginator';
import { CustomPaginatorIntl } from '@shared/services/custom-paginator-intl.service';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { SliListObject } from '../../models/sli-list-object.model';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatSortModule, Sort } from '@angular/material/sort';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { Modules, UserPermission, UserRole } from '@shared/models/user-role.model';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { AsyncPipe } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
	selector: 'orll-sli-table',
	templateUrl: './sli-table.component.html',
	styleUrls: ['./sli-table.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatTableModule,
		MatSortModule,
		MatButtonModule,
		MatMenuModule,
		MatIconModule,
		MatPaginatorModule,
		TranslateModule,
		RouterModule,
		AsyncPipe,
	],
	providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }],
})
export class SliTableComponent extends RolesAwareComponent implements OnChanges, OnInit {
	@Input() records: SliListObject[] = [];
	@Input() totalRecords = 0;
	@Input() pageParams!: PaginationRequest;
	@Input() fromCreateHawb = false;

	@Output() shareSli: EventEmitter<SliListObject> = new EventEmitter<SliListObject>();
	@Output() sortChange = new EventEmitter<Sort>();
	@Output() pagination = new EventEmitter<PageEvent & { sortField?: string; sortDirection?: string }>();

	currentSort: Sort = { active: '', direction: '' };

	displayedColumns: string[] = [
		'waybillNumber',
		'shipper',
		'consignee',
		'goodsDescription',
		'departureLocation',
		'arrivalLocation',
		'hawbNumber',
	];
	readonly tablePageSizes: number[] = [10, 50, 100];

	dataSource = new MatTableDataSource<SliListObject>(this.records || []);

	readonly sliModule = Modules.SLI;
	readonly createPermission = UserPermission.CREATE;
	readonly sharePermission = UserPermission.SHARE;
	readonly shipperRoles: string[] = [UserRole.SHIPPER];
	readonly forwarderRoles: string[] = [UserRole.FORWARDER];

	constructor(
		private readonly router: Router,
		private readonly route: ActivatedRoute
	) {
		super();
	}

	ngOnInit(): void {
		this.hasSomeRole(this.shipperRoles)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((hasRole) => {
				if (hasRole) {
					this.displayedColumns.splice(6, 0, 'createDate');
				}
			});
		this.hasSomeRole(this.forwarderRoles)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((hasRole) => {
				if (hasRole) {
					this.displayedColumns.splice(6, 0, 'receivedFrom');
				}
			});
		this.hasPermission(this.sharePermission, this.sliModule)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((hasPermission) => {
				if (hasPermission) {
					this.displayedColumns.push('share');
				}
			});
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['records']) {
			this.dataSource.data = this.records;
		}
	}

	onSortChange(sort: Sort): void {
		this.currentSort = sort;
		this.sortChange.emit(sort);
		this.emitPaginationWithSort();
	}

	private emitPaginationWithSort(event?: PageEvent) {
		const pageEvent = event || {
			pageIndex: this.pageParams.pageNum - 1,
			pageSize: this.pageParams.pageSize,
			length: this.totalRecords,
		};

		this.pagination.emit({
			...pageEvent,
			sortField: this.currentSort.active,
			sortDirection: this.currentSort.direction,
		});
	}

	createSli(): void {
		this.router.navigate(['create'], {
			relativeTo: this.route,
		});
	}

	editSli(waybillNumber: string): void {
		this.router.navigate(['edit', waybillNumber], {
			relativeTo: this.route,
		});
	}

	// eslint-disable-next-line
	trackBySliCode(_index: number, record: SliListObject): string {
		return record.waybillNumber + record.createDate;
	}
}
