(()=>{"use strict";var e,i={},_={};function n(e){var l=_[e];if(void 0!==l)return l.exports;var r=_[e]={exports:{}};return i[e](r,r.exports,n),r.exports}n.m=i,e=[],n.O=(l,r,o,f)=>{if(!r){var c=1/0;for(a=0;a<e.length;a++){for(var[r,o,f]=e[a],t=!0,u=0;u<r.length;u++)(!1&f||c>=f)&&Object.keys(n.O).every(p=>n.O[p](r[u]))?r.splice(u--,1):(t=!1,f<c&&(c=f));if(t){e.splice(a--,1);var s=o();void 0!==s&&(l=s)}}return l}f=f||0;for(var a=e.length;a>0&&e[a-1][2]>f;a--)e[a]=e[a-1];e[a]=[r,o,f]},n.o=(e,l)=>Object.prototype.hasOwnProperty.call(e,l),(()=>{var e={666:0};n.O.j=o=>0===e[o];var l=(o,f)=>{var u,s,[a,c,t]=f,v=0;if(a.some(h=>0!==e[h])){for(u in c)n.o(c,u)&&(n.m[u]=c[u]);if(t)var d=t(n)}for(o&&o(f);v<a.length;v++)n.o(e,s=a[v])&&e[s]&&e[s][0](),e[s]=0;return n.O(d)},r=self.webpackChunkmf_regular=self.webpackChunkmf_regular||[];r.forEach(l.bind(null,0)),r.push=l.bind(null,r.push.bind(r))})()})();
"use strict";(self.webpackChunkmf_regular=self.webpackChunkmf_regular||[]).push([[429],{332:()=>{!function(t){const n=t.performance;function i(L){n&&n.mark&&n.mark(L)}function o(L,T){n&&n.measure&&n.measure(L,T)}i("Zone");const c=t.__Zone_symbol_prefix||"__zone_symbol__";function a(L){return c+L}const y=!0===t[a("forceDuplicateZoneCheck")];if(t.Zone){if(y||"function"!=typeof t.Zone.__symbol__)throw new Error("Zone already loaded.");return t.Zone}let d=(()=>{class L{static#e=this.__symbol__=a;static assertZonePatched(){if(t.Promise!==se.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let e=L.current;for(;e.parent;)e=e.parent;return e}static get current(){return U.zone}static get currentTask(){return oe}static __load_patch(e,r,k=!1){if(se.hasOwnProperty(e)){if(!k&&y)throw Error("Already loaded patch: "+e)}else if(!t["__Zone_disable_"+e]){const C="Zone:"+e;i(C),se[e]=r(t,L,z),o(C,C)}}get parent(){return this._parent}get name(){return this._name}constructor(e,r){this._parent=e,this._name=r?r.name||"unnamed":"<root>",this._properties=r&&r.properties||{},this._zoneDelegate=new v(this,this._parent&&this._parent._zoneDelegate,r)}get(e){const r=this.getZoneWith(e);if(r)return r._properties[e]}getZoneWith(e){let r=this;for(;r;){if(r._properties.hasOwnProperty(e))return r;r=r._parent}return null}fork(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)}wrap(e,r){if("function"!=typeof e)throw new Error("Expecting function got: "+e);const k=this._zoneDelegate.intercept(this,e,r),C=this;return function(){return C.runGuarded(k,this,arguments,r)}}run(e,r,k,C){U={parent:U,zone:this};try{return this._zoneDelegate.invoke(this,e,r,k,C)}finally{U=U.parent}}runGuarded(e,r=null,k,C){U={parent:U,zone:this};try{try{return this._zoneDelegate.invoke(this,e,r,k,C)}catch($){if(this._zoneDelegate.handleError(this,$))throw $}}finally{U=U.parent}}runTask(e,r,k){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||J).name+"; Execution: "+this.name+")");if(e.state===x&&(e.type===Q||e.type===P))return;const C=e.state!=E;C&&e._transitionTo(E,j),e.runCount++;const $=oe;oe=e,U={parent:U,zone:this};try{e.type==P&&e.data&&!e.data.isPeriodic&&(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,e,r,k)}catch(u){if(this._zoneDelegate.handleError(this,u))throw u}}finally{e.state!==x&&e.state!==h&&(e.type==Q||e.data&&e.data.isPeriodic?C&&e._transitionTo(j,E):(e.runCount=0,this._updateTaskCount(e,-1),C&&e._transitionTo(x,E,x))),U=U.parent,oe=$}}scheduleTask(e){if(e.zone&&e.zone!==this){let k=this;for(;k;){if(k===e.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${e.zone.name}`);k=k.parent}}e._transitionTo(X,x);const r=[];e._zoneDelegates=r,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(k){throw e._transitionTo(h,X,x),this._zoneDelegate.handleError(this,k),k}return e._zoneDelegates===r&&this._updateTaskCount(e,1),e.state==X&&e._transitionTo(j,X),e}scheduleMicroTask(e,r,k,C){return this.scheduleTask(new p(I,e,r,k,C,void 0))}scheduleMacroTask(e,r,k,C,$){return this.scheduleTask(new p(P,e,r,k,C,$))}scheduleEventTask(e,r,k,C,$){return this.scheduleTask(new p(Q,e,r,k,C,$))}cancelTask(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||J).name+"; Execution: "+this.name+")");if(e.state===j||e.state===E){e._transitionTo(G,j,E);try{this._zoneDelegate.cancelTask(this,e)}catch(r){throw e._transitionTo(h,G),this._zoneDelegate.handleError(this,r),r}return this._updateTaskCount(e,-1),e._transitionTo(x,G),e.runCount=0,e}}_updateTaskCount(e,r){const k=e._zoneDelegates;-1==r&&(e._zoneDelegates=null);for(let C=0;C<k.length;C++)k[C]._updateTaskCount(e.type,r)}}return L})();const b={name:"",onHasTask:(L,T,e,r)=>L.hasTask(e,r),onScheduleTask:(L,T,e,r)=>L.scheduleTask(e,r),onInvokeTask:(L,T,e,r,k,C)=>L.invokeTask(e,r,k,C),onCancelTask:(L,T,e,r)=>L.cancelTask(e,r)};class v{constructor(T,e,r){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=T,this._parentDelegate=e,this._forkZS=r&&(r&&r.onFork?r:e._forkZS),this._forkDlgt=r&&(r.onFork?e:e._forkDlgt),this._forkCurrZone=r&&(r.onFork?this.zone:e._forkCurrZone),this._interceptZS=r&&(r.onIntercept?r:e._interceptZS),this._interceptDlgt=r&&(r.onIntercept?e:e._interceptDlgt),this._interceptCurrZone=r&&(r.onIntercept?this.zone:e._interceptCurrZone),this._invokeZS=r&&(r.onInvoke?r:e._invokeZS),this._invokeDlgt=r&&(r.onInvoke?e:e._invokeDlgt),this._invokeCurrZone=r&&(r.onInvoke?this.zone:e._invokeCurrZone),this._handleErrorZS=r&&(r.onHandleError?r:e._handleErrorZS),this._handleErrorDlgt=r&&(r.onHandleError?e:e._handleErrorDlgt),this._handleErrorCurrZone=r&&(r.onHandleError?this.zone:e._handleErrorCurrZone),this._scheduleTaskZS=r&&(r.onScheduleTask?r:e._scheduleTaskZS),this._scheduleTaskDlgt=r&&(r.onScheduleTask?e:e._scheduleTaskDlgt),this._scheduleTaskCurrZone=r&&(r.onScheduleTask?this.zone:e._scheduleTaskCurrZone),this._invokeTaskZS=r&&(r.onInvokeTask?r:e._invokeTaskZS),this._invokeTaskDlgt=r&&(r.onInvokeTask?e:e._invokeTaskDlgt),this._invokeTaskCurrZone=r&&(r.onInvokeTask?this.zone:e._invokeTaskCurrZone),this._cancelTaskZS=r&&(r.onCancelTask?r:e._cancelTaskZS),this._cancelTaskDlgt=r&&(r.onCancelTask?e:e._cancelTaskDlgt),this._cancelTaskCurrZone=r&&(r.onCancelTask?this.zone:e._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const k=r&&r.onHasTask;(k||e&&e._hasTaskZS)&&(this._hasTaskZS=k?r:b,this._hasTaskDlgt=e,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=T,r.onScheduleTask||(this._scheduleTaskZS=b,this._scheduleTaskDlgt=e,this._scheduleTaskCurrZone=this.zone),r.onInvokeTask||(this._invokeTaskZS=b,this._invokeTaskDlgt=e,this._invokeTaskCurrZone=this.zone),r.onCancelTask||(this._cancelTaskZS=b,this._cancelTaskDlgt=e,this._cancelTaskCurrZone=this.zone))}fork(T,e){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,T,e):new d(T,e)}intercept(T,e,r){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,T,e,r):e}invoke(T,e,r,k,C){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,T,e,r,k,C):e.apply(r,k)}handleError(T,e){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,T,e)}scheduleTask(T,e){let r=e;if(this._scheduleTaskZS)this._hasTaskZS&&r._zoneDelegates.push(this._hasTaskDlgtOwner),r=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,T,e),r||(r=e);else if(e.scheduleFn)e.scheduleFn(e);else{if(e.type!=I)throw new Error("Task is missing scheduleFn.");R(e)}return r}invokeTask(T,e,r,k){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,T,e,r,k):e.callback.apply(r,k)}cancelTask(T,e){let r;if(this._cancelTaskZS)r=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,T,e);else{if(!e.cancelFn)throw Error("Task is not cancelable");r=e.cancelFn(e)}return r}hasTask(T,e){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,T,e)}catch(r){this.handleError(T,r)}}_updateTaskCount(T,e){const r=this._taskCounts,k=r[T],C=r[T]=k+e;if(C<0)throw new Error("More tasks executed then were scheduled.");0!=k&&0!=C||this.hasTask(this.zone,{microTask:r.microTask>0,macroTask:r.macroTask>0,eventTask:r.eventTask>0,change:T})}}class p{constructor(T,e,r,k,C,$){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=T,this.source=e,this.data=k,this.scheduleFn=C,this.cancelFn=$,!r)throw new Error("callback is not defined");this.callback=r;const u=this;this.invoke=T===Q&&k&&k.useG?p.invokeTask:function(){return p.invokeTask.call(t,u,this,arguments)}}static invokeTask(T,e,r){T||(T=this),te++;try{return T.runCount++,T.zone.runTask(T,e,r)}finally{1==te&&_(),te--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(x,X)}_transitionTo(T,e,r){if(this._state!==e&&this._state!==r)throw new Error(`${this.type} '${this.source}': can not transition to '${T}', expecting state '${e}'${r?" or '"+r+"'":""}, was '${this._state}'.`);this._state=T,T==x&&(this._zoneDelegates=null)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const M=a("setTimeout"),O=a("Promise"),N=a("then");let K,B=[],H=!1;function q(L){if(K||t[O]&&(K=t[O].resolve(0)),K){let T=K[N];T||(T=K.then),T.call(K,L)}else t[M](L,0)}function R(L){0===te&&0===B.length&&q(_),L&&B.push(L)}function _(){if(!H){for(H=!0;B.length;){const L=B;B=[];for(let T=0;T<L.length;T++){const e=L[T];try{e.zone.runTask(e,null,null)}catch(r){z.onUnhandledError(r)}}}z.microtaskDrainDone(),H=!1}}const J={name:"NO ZONE"},x="notScheduled",X="scheduling",j="scheduled",E="running",G="canceling",h="unknown",I="microTask",P="macroTask",Q="eventTask",se={},z={symbol:a,currentZoneFrame:()=>U,onUnhandledError:W,microtaskDrainDone:W,scheduleMicroTask:R,showUncaughtError:()=>!d[a("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:W,patchMethod:()=>W,bindArguments:()=>[],patchThen:()=>W,patchMacroTask:()=>W,patchEventPrototype:()=>W,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>W,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>W,wrapWithCurrentZone:()=>W,filterProperties:()=>[],attachOriginToPatched:()=>W,_redefineProperty:()=>W,patchCallbacks:()=>W,nativeScheduleMicroTask:q};let U={parent:null,zone:new d(null,null)},oe=null,te=0;function W(){}o("Zone","Zone"),t.Zone=d}(globalThis);const fe=Object.getOwnPropertyDescriptor,pe=Object.defineProperty,be=Object.getPrototypeOf,De=Object.create,ct=Array.prototype.slice,Ze="addEventListener",Oe="removeEventListener",Ne=Zone.__symbol__(Ze),Ie=Zone.__symbol__(Oe),ce="true",ae="false",ge=Zone.__symbol__("");function Me(t,n){return Zone.current.wrap(t,n)}function Le(t,n,i,o,c){return Zone.current.scheduleMacroTask(t,n,i,o,c)}const A=Zone.__symbol__,Pe=typeof window<"u",_e=Pe?window:void 0,Y=Pe&&_e||globalThis,at="removeAttribute";function je(t,n){for(let i=t.length-1;i>=0;i--)"function"==typeof t[i]&&(t[i]=Me(t[i],n+"_"+i));return t}function Fe(t){return!t||!1!==t.writable&&!("function"==typeof t.get&&typeof t.set>"u")}const Be=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,we=!("nw"in Y)&&typeof Y.process<"u"&&"[object process]"==={}.toString.call(Y.process),Ae=!we&&!Be&&!(!Pe||!_e.HTMLElement),Ue=typeof Y.process<"u"&&"[object process]"==={}.toString.call(Y.process)&&!Be&&!(!Pe||!_e.HTMLElement),Re={},We=function(t){if(!(t=t||Y.event))return;let n=Re[t.type];n||(n=Re[t.type]=A("ON_PROPERTY"+t.type));const i=this||t.target||Y,o=i[n];let c;return Ae&&i===_e&&"error"===t.type?(c=o&&o.call(this,t.message,t.filename,t.lineno,t.colno,t.error),!0===c&&t.preventDefault()):(c=o&&o.apply(this,arguments),null!=c&&!c&&t.preventDefault()),c};function qe(t,n,i){let o=fe(t,n);if(!o&&i&&fe(i,n)&&(o={enumerable:!0,configurable:!0}),!o||!o.configurable)return;const c=A("on"+n+"patched");if(t.hasOwnProperty(c)&&t[c])return;delete o.writable,delete o.value;const a=o.get,y=o.set,d=n.slice(2);let b=Re[d];b||(b=Re[d]=A("ON_PROPERTY"+d)),o.set=function(v){let p=this;!p&&t===Y&&(p=Y),p&&("function"==typeof p[b]&&p.removeEventListener(d,We),y&&y.call(p,null),p[b]=v,"function"==typeof v&&p.addEventListener(d,We,!1))},o.get=function(){let v=this;if(!v&&t===Y&&(v=Y),!v)return null;const p=v[b];if(p)return p;if(a){let M=a.call(this);if(M)return o.set.call(this,M),"function"==typeof v[at]&&v.removeAttribute(n),M}return null},pe(t,n,o),t[c]=!0}function Xe(t,n,i){if(n)for(let o=0;o<n.length;o++)qe(t,"on"+n[o],i);else{const o=[];for(const c in t)"on"==c.slice(0,2)&&o.push(c);for(let c=0;c<o.length;c++)qe(t,o[c],i)}}const re=A("originalInstance");function me(t){const n=Y[t];if(!n)return;Y[A(t)]=n,Y[t]=function(){const c=je(arguments,t);switch(c.length){case 0:this[re]=new n;break;case 1:this[re]=new n(c[0]);break;case 2:this[re]=new n(c[0],c[1]);break;case 3:this[re]=new n(c[0],c[1],c[2]);break;case 4:this[re]=new n(c[0],c[1],c[2],c[3]);break;default:throw new Error("Arg list too long.")}},ue(Y[t],n);const i=new n(function(){});let o;for(o in i)"XMLHttpRequest"===t&&"responseBlob"===o||function(c){"function"==typeof i[c]?Y[t].prototype[c]=function(){return this[re][c].apply(this[re],arguments)}:pe(Y[t].prototype,c,{set:function(a){"function"==typeof a?(this[re][c]=Me(a,t+"."+c),ue(this[re][c],a)):this[re][c]=a},get:function(){return this[re][c]}})}(o);for(o in n)"prototype"!==o&&n.hasOwnProperty(o)&&(Y[t][o]=n[o])}function le(t,n,i){let o=t;for(;o&&!o.hasOwnProperty(n);)o=be(o);!o&&t[n]&&(o=t);const c=A(n);let a=null;if(o&&(!(a=o[c])||!o.hasOwnProperty(c))&&(a=o[c]=o[n],Fe(o&&fe(o,n)))){const d=i(a,c,n);o[n]=function(){return d(this,arguments)},ue(o[n],a)}return a}function ut(t,n,i){let o=null;function c(a){const y=a.data;return y.args[y.cbIdx]=function(){a.invoke.apply(this,arguments)},o.apply(y.target,y.args),a}o=le(t,n,a=>function(y,d){const b=i(y,d);return b.cbIdx>=0&&"function"==typeof d[b.cbIdx]?Le(b.name,d[b.cbIdx],b,c):a.apply(y,d)})}function ue(t,n){t[A("OriginalDelegate")]=n}let ze=!1,He=!1;function ht(){if(ze)return He;ze=!0;try{const t=_e.navigator.userAgent;(-1!==t.indexOf("MSIE ")||-1!==t.indexOf("Trident/")||-1!==t.indexOf("Edge/"))&&(He=!0)}catch{}return He}Zone.__load_patch("ZoneAwarePromise",(t,n,i)=>{const o=Object.getOwnPropertyDescriptor,c=Object.defineProperty,y=i.symbol,d=[],b=!1!==t[y("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],v=y("Promise"),p=y("then"),M="__creationTrace__";i.onUnhandledError=u=>{if(i.showUncaughtError()){const l=u&&u.rejection;l?console.error("Unhandled Promise rejection:",l instanceof Error?l.message:l,"; Zone:",u.zone.name,"; Task:",u.task&&u.task.source,"; Value:",l,l instanceof Error?l.stack:void 0):console.error(u)}},i.microtaskDrainDone=()=>{for(;d.length;){const u=d.shift();try{u.zone.runGuarded(()=>{throw u.throwOriginal?u.rejection:u})}catch(l){N(l)}}};const O=y("unhandledPromiseRejectionHandler");function N(u){i.onUnhandledError(u);try{const l=n[O];"function"==typeof l&&l.call(this,u)}catch{}}function B(u){return u&&u.then}function H(u){return u}function K(u){return e.reject(u)}const q=y("state"),R=y("value"),_=y("finally"),J=y("parentPromiseValue"),x=y("parentPromiseState"),X="Promise.then",j=null,E=!0,G=!1,h=0;function I(u,l){return s=>{try{z(u,l,s)}catch(f){z(u,!1,f)}}}const P=function(){let u=!1;return function(s){return function(){u||(u=!0,s.apply(null,arguments))}}},Q="Promise resolved with itself",se=y("currentTaskTrace");function z(u,l,s){const f=P();if(u===s)throw new TypeError(Q);if(u[q]===j){let m=null;try{("object"==typeof s||"function"==typeof s)&&(m=s&&s.then)}catch(w){return f(()=>{z(u,!1,w)})(),u}if(l!==G&&s instanceof e&&s.hasOwnProperty(q)&&s.hasOwnProperty(R)&&s[q]!==j)oe(s),z(u,s[q],s[R]);else if(l!==G&&"function"==typeof m)try{m.call(s,f(I(u,l)),f(I(u,!1)))}catch(w){f(()=>{z(u,!1,w)})()}else{u[q]=l;const w=u[R];if(u[R]=s,u[_]===_&&l===E&&(u[q]=u[x],u[R]=u[J]),l===G&&s instanceof Error){const g=n.currentTask&&n.currentTask.data&&n.currentTask.data[M];g&&c(s,se,{configurable:!0,enumerable:!1,writable:!0,value:g})}for(let g=0;g<w.length;)te(u,w[g++],w[g++],w[g++],w[g++]);if(0==w.length&&l==G){u[q]=h;let g=s;try{throw new Error("Uncaught (in promise): "+function a(u){return u&&u.toString===Object.prototype.toString?(u.constructor&&u.constructor.name||"")+": "+JSON.stringify(u):u?u.toString():Object.prototype.toString.call(u)}(s)+(s&&s.stack?"\n"+s.stack:""))}catch(S){g=S}b&&(g.throwOriginal=!0),g.rejection=s,g.promise=u,g.zone=n.current,g.task=n.currentTask,d.push(g),i.scheduleMicroTask()}}}return u}const U=y("rejectionHandledHandler");function oe(u){if(u[q]===h){try{const l=n[U];l&&"function"==typeof l&&l.call(this,{rejection:u[R],promise:u})}catch{}u[q]=G;for(let l=0;l<d.length;l++)u===d[l].promise&&d.splice(l,1)}}function te(u,l,s,f,m){oe(u);const w=u[q],g=w?"function"==typeof f?f:H:"function"==typeof m?m:K;l.scheduleMicroTask(X,()=>{try{const S=u[R],D=!!s&&_===s[_];D&&(s[J]=S,s[x]=w);const Z=l.run(g,void 0,D&&g!==K&&g!==H?[]:[S]);z(s,!0,Z)}catch(S){z(s,!1,S)}},s)}const L=function(){},T=t.AggregateError;class e{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(l){return l instanceof e?l:z(new this(null),E,l)}static reject(l){return z(new this(null),G,l)}static withResolvers(){const l={};return l.promise=new e((s,f)=>{l.resolve=s,l.reject=f}),l}static any(l){if(!l||"function"!=typeof l[Symbol.iterator])return Promise.reject(new T([],"All promises were rejected"));const s=[];let f=0;try{for(let g of l)f++,s.push(e.resolve(g))}catch{return Promise.reject(new T([],"All promises were rejected"))}if(0===f)return Promise.reject(new T([],"All promises were rejected"));let m=!1;const w=[];return new e((g,S)=>{for(let D=0;D<s.length;D++)s[D].then(Z=>{m||(m=!0,g(Z))},Z=>{w.push(Z),f--,0===f&&(m=!0,S(new T(w,"All promises were rejected")))})})}static race(l){let s,f,m=new this((S,D)=>{s=S,f=D});function w(S){s(S)}function g(S){f(S)}for(let S of l)B(S)||(S=this.resolve(S)),S.then(w,g);return m}static all(l){return e.allWithCallback(l)}static allSettled(l){return(this&&this.prototype instanceof e?this:e).allWithCallback(l,{thenCallback:f=>({status:"fulfilled",value:f}),errorCallback:f=>({status:"rejected",reason:f})})}static allWithCallback(l,s){let f,m,w=new this((Z,V)=>{f=Z,m=V}),g=2,S=0;const D=[];for(let Z of l){B(Z)||(Z=this.resolve(Z));const V=S;try{Z.then(F=>{D[V]=s?s.thenCallback(F):F,g--,0===g&&f(D)},F=>{s?(D[V]=s.errorCallback(F),g--,0===g&&f(D)):m(F)})}catch(F){m(F)}g++,S++}return g-=2,0===g&&f(D),w}constructor(l){const s=this;if(!(s instanceof e))throw new Error("Must be an instanceof Promise.");s[q]=j,s[R]=[];try{const f=P();l&&l(f(I(s,E)),f(I(s,G)))}catch(f){z(s,!1,f)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return e}then(l,s){let f=this.constructor?.[Symbol.species];(!f||"function"!=typeof f)&&(f=this.constructor||e);const m=new f(L),w=n.current;return this[q]==j?this[R].push(w,m,l,s):te(this,w,m,l,s),m}catch(l){return this.then(null,l)}finally(l){let s=this.constructor?.[Symbol.species];(!s||"function"!=typeof s)&&(s=e);const f=new s(L);f[_]=_;const m=n.current;return this[q]==j?this[R].push(m,f,l,l):te(this,m,f,l,l),f}}e.resolve=e.resolve,e.reject=e.reject,e.race=e.race,e.all=e.all;const r=t[v]=t.Promise;t.Promise=e;const k=y("thenPatched");function C(u){const l=u.prototype,s=o(l,"then");if(s&&(!1===s.writable||!s.configurable))return;const f=l.then;l[p]=f,u.prototype.then=function(m,w){return new e((S,D)=>{f.call(this,S,D)}).then(m,w)},u[k]=!0}return i.patchThen=C,r&&(C(r),le(t,"fetch",u=>function $(u){return function(l,s){let f=u.apply(l,s);if(f instanceof e)return f;let m=f.constructor;return m[k]||C(m),f}}(u))),Promise[n.__symbol__("uncaughtPromiseErrors")]=d,e}),Zone.__load_patch("toString",t=>{const n=Function.prototype.toString,i=A("OriginalDelegate"),o=A("Promise"),c=A("Error"),a=function(){if("function"==typeof this){const v=this[i];if(v)return"function"==typeof v?n.call(v):Object.prototype.toString.call(v);if(this===Promise){const p=t[o];if(p)return n.call(p)}if(this===Error){const p=t[c];if(p)return n.call(p)}}return n.call(this)};a[i]=n,Function.prototype.toString=a;const y=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":y.call(this)}});let Ee=!1;if(typeof window<"u")try{const t=Object.defineProperty({},"passive",{get:function(){Ee=!0}});window.addEventListener("test",t,t),window.removeEventListener("test",t,t)}catch{Ee=!1}const dt={useG:!0},ne={},Ye={},$e=new RegExp("^"+ge+"(\\w+)(true|false)$"),Je=A("propagationStopped");function Ke(t,n){const i=(n?n(t):t)+ae,o=(n?n(t):t)+ce,c=ge+i,a=ge+o;ne[t]={},ne[t][ae]=c,ne[t][ce]=a}function _t(t,n,i,o){const c=o&&o.add||Ze,a=o&&o.rm||Oe,y=o&&o.listeners||"eventListeners",d=o&&o.rmAll||"removeAllListeners",b=A(c),v="."+c+":",p="prependListener",M="."+p+":",O=function(R,_,J){if(R.isRemoved)return;const x=R.callback;let X;"object"==typeof x&&x.handleEvent&&(R.callback=E=>x.handleEvent(E),R.originalDelegate=x);try{R.invoke(R,_,[J])}catch(E){X=E}const j=R.options;return j&&"object"==typeof j&&j.once&&_[a].call(_,J.type,R.originalDelegate?R.originalDelegate:R.callback,j),X};function N(R,_,J){if(!(_=_||t.event))return;const x=R||_.target||t,X=x[ne[_.type][J?ce:ae]];if(X){const j=[];if(1===X.length){const E=O(X[0],x,_);E&&j.push(E)}else{const E=X.slice();for(let G=0;G<E.length&&(!_||!0!==_[Je]);G++){const h=O(E[G],x,_);h&&j.push(h)}}if(1===j.length)throw j[0];for(let E=0;E<j.length;E++){const G=j[E];n.nativeScheduleMicroTask(()=>{throw G})}}}const B=function(R){return N(this,R,!1)},H=function(R){return N(this,R,!0)};function K(R,_){if(!R)return!1;let J=!0;_&&void 0!==_.useG&&(J=_.useG);const x=_&&_.vh;let X=!0;_&&void 0!==_.chkDup&&(X=_.chkDup);let j=!1;_&&void 0!==_.rt&&(j=_.rt);let E=R;for(;E&&!E.hasOwnProperty(c);)E=be(E);if(!E&&R[c]&&(E=R),!E||E[b])return!1;const G=_&&_.eventNameToString,h={},I=E[b]=E[c],P=E[A(a)]=E[a],Q=E[A(y)]=E[y],se=E[A(d)]=E[d];let z;_&&_.prepend&&(z=E[A(_.prepend)]=E[_.prepend]);const e=J?function(s){if(!h.isExisting)return I.call(h.target,h.eventName,h.capture?H:B,h.options)}:function(s){return I.call(h.target,h.eventName,s.invoke,h.options)},r=J?function(s){if(!s.isRemoved){const f=ne[s.eventName];let m;f&&(m=f[s.capture?ce:ae]);const w=m&&s.target[m];if(w)for(let g=0;g<w.length;g++)if(w[g]===s){w.splice(g,1),s.isRemoved=!0,0===w.length&&(s.allRemoved=!0,s.target[m]=null);break}}if(s.allRemoved)return P.call(s.target,s.eventName,s.capture?H:B,s.options)}:function(s){return P.call(s.target,s.eventName,s.invoke,s.options)},C=_&&_.diff?_.diff:function(s,f){const m=typeof f;return"function"===m&&s.callback===f||"object"===m&&s.originalDelegate===f},$=Zone[A("UNPATCHED_EVENTS")],u=t[A("PASSIVE_EVENTS")],l=function(s,f,m,w,g=!1,S=!1){return function(){const D=this||t;let Z=arguments[0];_&&_.transferEventName&&(Z=_.transferEventName(Z));let V=arguments[1];if(!V)return s.apply(this,arguments);if(we&&"uncaughtException"===Z)return s.apply(this,arguments);let F=!1;if("function"!=typeof V){if(!V.handleEvent)return s.apply(this,arguments);F=!0}if(x&&!x(s,V,D,arguments))return;const he=Ee&&!!u&&-1!==u.indexOf(Z),ee=function U(s,f){return!Ee&&"object"==typeof s&&s?!!s.capture:Ee&&f?"boolean"==typeof s?{capture:s,passive:!0}:s?"object"==typeof s&&!1!==s.passive?{...s,passive:!0}:s:{passive:!0}:s}(arguments[2],he),ke=ee&&"object"==typeof ee&&ee.signal&&"object"==typeof ee.signal?ee.signal:void 0;if(ke?.aborted)return;if($)for(let de=0;de<$.length;de++)if(Z===$[de])return he?s.call(D,Z,V,ee):s.apply(this,arguments);const Ge=!!ee&&("boolean"==typeof ee||ee.capture),nt=!(!ee||"object"!=typeof ee)&&ee.once,vt=Zone.current;let Ve=ne[Z];Ve||(Ke(Z,G),Ve=ne[Z]);const rt=Ve[Ge?ce:ae];let Se,ye=D[rt],ot=!1;if(ye){if(ot=!0,X)for(let de=0;de<ye.length;de++)if(C(ye[de],V))return}else ye=D[rt]=[];const st=D.constructor.name,it=Ye[st];it&&(Se=it[Z]),Se||(Se=st+f+(G?G(Z):Z)),h.options=ee,nt&&(h.options.once=!1),h.target=D,h.capture=Ge,h.eventName=Z,h.isExisting=ot;const ve=J?dt:void 0;ve&&(ve.taskData=h),ke&&(h.options.signal=void 0);const ie=vt.scheduleEventTask(Se,V,ve,m,w);return ke&&(h.options.signal=ke,s.call(ke,"abort",()=>{ie.zone.cancelTask(ie)},{once:!0})),h.target=null,ve&&(ve.taskData=null),nt&&(ee.once=!0),!Ee&&"boolean"==typeof ie.options||(ie.options=ee),ie.target=D,ie.capture=Ge,ie.eventName=Z,F&&(ie.originalDelegate=V),S?ye.unshift(ie):ye.push(ie),g?D:void 0}};return E[c]=l(I,v,e,r,j),z&&(E[p]=l(z,M,function(s){return z.call(h.target,h.eventName,s.invoke,h.options)},r,j,!0)),E[a]=function(){const s=this||t;let f=arguments[0];_&&_.transferEventName&&(f=_.transferEventName(f));const m=arguments[2],w=!!m&&("boolean"==typeof m||m.capture),g=arguments[1];if(!g)return P.apply(this,arguments);if(x&&!x(P,g,s,arguments))return;const S=ne[f];let D;S&&(D=S[w?ce:ae]);const Z=D&&s[D];if(Z)for(let V=0;V<Z.length;V++){const F=Z[V];if(C(F,g))return Z.splice(V,1),F.isRemoved=!0,0===Z.length&&(F.allRemoved=!0,s[D]=null,"string"==typeof f)&&(s[ge+"ON_PROPERTY"+f]=null),F.zone.cancelTask(F),j?s:void 0}return P.apply(this,arguments)},E[y]=function(){const s=this||t;let f=arguments[0];_&&_.transferEventName&&(f=_.transferEventName(f));const m=[],w=Qe(s,G?G(f):f);for(let g=0;g<w.length;g++){const S=w[g];m.push(S.originalDelegate?S.originalDelegate:S.callback)}return m},E[d]=function(){const s=this||t;let f=arguments[0];if(f){_&&_.transferEventName&&(f=_.transferEventName(f));const m=ne[f];if(m){const S=s[m[ae]],D=s[m[ce]];if(S){const Z=S.slice();for(let V=0;V<Z.length;V++){const F=Z[V];this[a].call(this,f,F.originalDelegate?F.originalDelegate:F.callback,F.options)}}if(D){const Z=D.slice();for(let V=0;V<Z.length;V++){const F=Z[V];this[a].call(this,f,F.originalDelegate?F.originalDelegate:F.callback,F.options)}}}}else{const m=Object.keys(s);for(let w=0;w<m.length;w++){const S=$e.exec(m[w]);let D=S&&S[1];D&&"removeListener"!==D&&this[d].call(this,D)}this[d].call(this,"removeListener")}if(j)return this},ue(E[c],I),ue(E[a],P),se&&ue(E[d],se),Q&&ue(E[y],Q),!0}let q=[];for(let R=0;R<i.length;R++)q[R]=K(i[R],o);return q}function Qe(t,n){if(!n){const a=[];for(let y in t){const d=$e.exec(y);let b=d&&d[1];if(b&&(!n||b===n)){const v=t[y];if(v)for(let p=0;p<v.length;p++)a.push(v[p])}}return a}let i=ne[n];i||(Ke(n),i=ne[n]);const o=t[i[ae]],c=t[i[ce]];return o?c?o.concat(c):o.slice():c?c.slice():[]}function Et(t,n){const i=t.Event;i&&i.prototype&&n.patchMethod(i.prototype,"stopImmediatePropagation",o=>function(c,a){c[Je]=!0,o&&o.apply(c,a)})}function Tt(t,n,i,o,c){const a=Zone.__symbol__(o);if(n[a])return;const y=n[a]=n[o];n[o]=function(d,b,v){return b&&b.prototype&&c.forEach(function(p){const M=`${i}.${o}::`+p,O=b.prototype;try{if(O.hasOwnProperty(p)){const N=t.ObjectGetOwnPropertyDescriptor(O,p);N&&N.value?(N.value=t.wrapWithCurrentZone(N.value,M),t._redefineProperty(b.prototype,p,N)):O[p]&&(O[p]=t.wrapWithCurrentZone(O[p],M))}else O[p]&&(O[p]=t.wrapWithCurrentZone(O[p],M))}catch{}}),y.call(n,d,b,v)},t.attachOriginToPatched(n[o],y)}function et(t,n,i){if(!i||0===i.length)return n;const o=i.filter(a=>a.target===t);if(!o||0===o.length)return n;const c=o[0].ignoreProperties;return n.filter(a=>-1===c.indexOf(a))}function tt(t,n,i,o){t&&Xe(t,et(t,n,i),o)}function xe(t){return Object.getOwnPropertyNames(t).filter(n=>n.startsWith("on")&&n.length>2).map(n=>n.substring(2))}Zone.__load_patch("util",(t,n,i)=>{const o=xe(t);i.patchOnProperties=Xe,i.patchMethod=le,i.bindArguments=je,i.patchMacroTask=ut;const c=n.__symbol__("BLACK_LISTED_EVENTS"),a=n.__symbol__("UNPATCHED_EVENTS");t[a]&&(t[c]=t[a]),t[c]&&(n[c]=n[a]=t[c]),i.patchEventPrototype=Et,i.patchEventTarget=_t,i.isIEOrEdge=ht,i.ObjectDefineProperty=pe,i.ObjectGetOwnPropertyDescriptor=fe,i.ObjectCreate=De,i.ArraySlice=ct,i.patchClass=me,i.wrapWithCurrentZone=Me,i.filterProperties=et,i.attachOriginToPatched=ue,i._redefineProperty=Object.defineProperty,i.patchCallbacks=Tt,i.getGlobalObjects=()=>({globalSources:Ye,zoneSymbolEventNames:ne,eventNames:o,isBrowser:Ae,isMix:Ue,isNode:we,TRUE_STR:ce,FALSE_STR:ae,ZONE_SYMBOL_PREFIX:ge,ADD_EVENT_LISTENER_STR:Ze,REMOVE_EVENT_LISTENER_STR:Oe})});const Ce=A("zoneTask");function Te(t,n,i,o){let c=null,a=null;i+=o;const y={};function d(v){const p=v.data;return p.args[0]=function(){return v.invoke.apply(this,arguments)},p.handleId=c.apply(t,p.args),v}function b(v){return a.call(t,v.data.handleId)}c=le(t,n+=o,v=>function(p,M){if("function"==typeof M[0]){const O={isPeriodic:"Interval"===o,delay:"Timeout"===o||"Interval"===o?M[1]||0:void 0,args:M},N=M[0];M[0]=function(){try{return N.apply(this,arguments)}finally{O.isPeriodic||("number"==typeof O.handleId?delete y[O.handleId]:O.handleId&&(O.handleId[Ce]=null))}};const B=Le(n,M[0],O,d,b);if(!B)return B;const H=B.data.handleId;return"number"==typeof H?y[H]=B:H&&(H[Ce]=B),H&&H.ref&&H.unref&&"function"==typeof H.ref&&"function"==typeof H.unref&&(B.ref=H.ref.bind(H),B.unref=H.unref.bind(H)),"number"==typeof H||H?H:B}return v.apply(t,M)}),a=le(t,i,v=>function(p,M){const O=M[0];let N;"number"==typeof O?N=y[O]:(N=O&&O[Ce],N||(N=O)),N&&"string"==typeof N.type?"notScheduled"!==N.state&&(N.cancelFn&&N.data.isPeriodic||0===N.runCount)&&("number"==typeof O?delete y[O]:O&&(O[Ce]=null),N.zone.cancelTask(N)):v.apply(t,M)})}Zone.__load_patch("legacy",t=>{const n=t[Zone.__symbol__("legacyPatch")];n&&n()}),Zone.__load_patch("timers",t=>{const n="set",i="clear";Te(t,n,i,"Timeout"),Te(t,n,i,"Interval"),Te(t,n,i,"Immediate")}),Zone.__load_patch("requestAnimationFrame",t=>{Te(t,"request","cancel","AnimationFrame"),Te(t,"mozRequest","mozCancel","AnimationFrame"),Te(t,"webkitRequest","webkitCancel","AnimationFrame")}),Zone.__load_patch("blocking",(t,n)=>{const i=["alert","prompt","confirm"];for(let o=0;o<i.length;o++)le(t,i[o],(a,y,d)=>function(b,v){return n.current.run(a,t,v,d)})}),Zone.__load_patch("EventTarget",(t,n,i)=>{(function kt(t,n){n.patchEventPrototype(t,n)})(t,i),function mt(t,n){if(Zone[n.symbol("patchEventTarget")])return;const{eventNames:i,zoneSymbolEventNames:o,TRUE_STR:c,FALSE_STR:a,ZONE_SYMBOL_PREFIX:y}=n.getGlobalObjects();for(let b=0;b<i.length;b++){const v=i[b],O=y+(v+a),N=y+(v+c);o[v]={},o[v][a]=O,o[v][c]=N}const d=t.EventTarget;d&&d.prototype&&n.patchEventTarget(t,n,[d&&d.prototype])}(t,i);const o=t.XMLHttpRequestEventTarget;o&&o.prototype&&i.patchEventTarget(t,i,[o.prototype])}),Zone.__load_patch("MutationObserver",(t,n,i)=>{me("MutationObserver"),me("WebKitMutationObserver")}),Zone.__load_patch("IntersectionObserver",(t,n,i)=>{me("IntersectionObserver")}),Zone.__load_patch("FileReader",(t,n,i)=>{me("FileReader")}),Zone.__load_patch("on_property",(t,n,i)=>{!function yt(t,n){if(we&&!Ue||Zone[t.symbol("patchEvents")])return;const i=n.__Zone_ignore_on_properties;let o=[];if(Ae){const c=window;o=o.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);const a=function ft(){try{const t=_e.navigator.userAgent;if(-1!==t.indexOf("MSIE ")||-1!==t.indexOf("Trident/"))return!0}catch{}return!1}()?[{target:c,ignoreProperties:["error"]}]:[];tt(c,xe(c),i&&i.concat(a),be(c))}o=o.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let c=0;c<o.length;c++){const a=n[o[c]];a&&a.prototype&&tt(a.prototype,xe(a.prototype),i)}}(i,t)}),Zone.__load_patch("customElements",(t,n,i)=>{!function gt(t,n){const{isBrowser:i,isMix:o}=n.getGlobalObjects();(i||o)&&t.customElements&&"customElements"in t&&n.patchCallbacks(n,t.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback"])}(t,i)}),Zone.__load_patch("XHR",(t,n)=>{!function b(v){const p=v.XMLHttpRequest;if(!p)return;const M=p.prototype;let N=M[Ne],B=M[Ie];if(!N){const h=v.XMLHttpRequestEventTarget;if(h){const I=h.prototype;N=I[Ne],B=I[Ie]}}const H="readystatechange",K="scheduled";function q(h){const I=h.data,P=I.target;P[a]=!1,P[d]=!1;const Q=P[c];N||(N=P[Ne],B=P[Ie]),Q&&B.call(P,H,Q);const se=P[c]=()=>{if(P.readyState===P.DONE)if(!I.aborted&&P[a]&&h.state===K){const U=P[n.__symbol__("loadfalse")];if(0!==P.status&&U&&U.length>0){const oe=h.invoke;h.invoke=function(){const te=P[n.__symbol__("loadfalse")];for(let W=0;W<te.length;W++)te[W]===h&&te.splice(W,1);!I.aborted&&h.state===K&&oe.call(h)},U.push(h)}else h.invoke()}else!I.aborted&&!1===P[a]&&(P[d]=!0)};return N.call(P,H,se),P[i]||(P[i]=h),E.apply(P,I.args),P[a]=!0,h}function R(){}function _(h){const I=h.data;return I.aborted=!0,G.apply(I.target,I.args)}const J=le(M,"open",()=>function(h,I){return h[o]=0==I[2],h[y]=I[1],J.apply(h,I)}),X=A("fetchTaskAborting"),j=A("fetchTaskScheduling"),E=le(M,"send",()=>function(h,I){if(!0===n.current[j]||h[o])return E.apply(h,I);{const P={target:h,url:h[y],isPeriodic:!1,args:I,aborted:!1},Q=Le("XMLHttpRequest.send",R,P,q,_);h&&!0===h[d]&&!P.aborted&&Q.state===K&&Q.invoke()}}),G=le(M,"abort",()=>function(h,I){const P=function O(h){return h[i]}(h);if(P&&"string"==typeof P.type){if(null==P.cancelFn||P.data&&P.data.aborted)return;P.zone.cancelTask(P)}else if(!0===n.current[X])return G.apply(h,I)})}(t);const i=A("xhrTask"),o=A("xhrSync"),c=A("xhrListener"),a=A("xhrScheduled"),y=A("xhrURL"),d=A("xhrErrorBeforeScheduled")}),Zone.__load_patch("geolocation",t=>{t.navigator&&t.navigator.geolocation&&function lt(t,n){const i=t.constructor.name;for(let o=0;o<n.length;o++){const c=n[o],a=t[c];if(a){if(!Fe(fe(t,c)))continue;t[c]=(d=>{const b=function(){return d.apply(this,je(arguments,i+"."+c))};return ue(b,d),b})(a)}}}(t.navigator.geolocation,["getCurrentPosition","watchPosition"])}),Zone.__load_patch("PromiseRejectionEvent",(t,n)=>{function i(o){return function(c){Qe(t,o).forEach(y=>{const d=t.PromiseRejectionEvent;if(d){const b=new d(o,{promise:c.promise,reason:c.rejection});y.invoke(b)}})}}t.PromiseRejectionEvent&&(n[A("unhandledPromiseRejectionHandler")]=i("unhandledrejection"),n[A("rejectionHandledHandler")]=i("rejectionhandled"))}),Zone.__load_patch("queueMicrotask",(t,n,i)=>{!function pt(t,n){n.patchMethod(t,"queueMicrotask",i=>function(o,c){Zone.current.scheduleMicroTask("queueMicrotask",c[0])})}(t,i)})}},fe=>{fe(fe.s=332)}]);
"use strict";(self.webpackChunkmf_regular=self.webpackChunkmf_regular||[]).push([[179],{31:()=>{function Eo(e,t,n,i,o,r,s){try{var a=e[r](s),l=a.value}catch(c){return void n(c)}a.done?t(l):Promise.resolve(l).then(i,o)}let he=null,Qr=1;function de(e){const t=he;return he=e,t}function sh(e){if((!Io(e)||e.dirty)&&(e.dirty||e.lastCleanEpoch!==Qr)){if(!e.producerMustRecompute(e)&&!Al(e))return e.dirty=!1,void(e.lastCleanEpoch=Qr);e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Qr}}function Al(e){pi(e);for(let t=0;t<e.producerNode.length;t++){const n=e.producerNode[t],i=e.producerLastReadVersion[t];if(i!==n.version||(sh(n),i!==n.version))return!0}return!1}function Jr(e,t){if(function hh(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}(e),pi(e),1===e.liveConsumerNode.length)for(let i=0;i<e.producerNode.length;i++)Jr(e.producerNode[i],e.producerIndexOfThis[i]);const n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){const i=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];pi(o),o.producerIndexOfThis[i]=t}}function Io(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function pi(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}let mh=null;function ve(e){return"function"==typeof e}function bh(e){const n=e(i=>{Error.call(i),i.stack=(new Error).stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}const Nl=bh(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:\n${n.map((i,o)=>`${o+1}) ${i.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=n});function es(e,t){if(e){const n=e.indexOf(t);0<=n&&e.splice(n,1)}}class ft{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;const{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(const r of n)r.remove(this);else n.remove(this);const{initialTeardown:i}=this;if(ve(i))try{i()}catch(r){t=r instanceof Nl?r.errors:[r]}const{_finalizers:o}=this;if(o){this._finalizers=null;for(const r of o)try{yh(r)}catch(s){t=t??[],s instanceof Nl?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Nl(t)}}add(t){var n;if(t&&t!==this)if(this.closed)yh(t);else{if(t instanceof ft){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=null!==(n=this._finalizers)&&void 0!==n?n:[]).push(t)}}_hasParent(t){const{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){const{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){const{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&es(n,t)}remove(t){const{_finalizers:n}=this;n&&es(n,t),t instanceof ft&&t._removeParent(this)}}ft.EMPTY=(()=>{const e=new ft;return e.closed=!0,e})();const _h=ft.EMPTY;function vh(e){return e instanceof ft||e&&"closed"in e&&ve(e.remove)&&ve(e.add)&&ve(e.unsubscribe)}function yh(e){ve(e)?e():e.unsubscribe()}const Vn={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},ts={setTimeout(e,t,...n){const{delegate:i}=ts;return i?.setTimeout?i.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){const{delegate:t}=ts;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function xh(e){ts.setTimeout(()=>{const{onUnhandledError:t}=Vn;if(!t)throw e;t(e)})}function Fl(){}const z0=Ol("C",void 0,void 0);function Ol(e,t,n){return{kind:e,value:t,error:n}}let jn=null;function ns(e){if(Vn.useDeprecatedSynchronousErrorHandling){const t=!jn;if(t&&(jn={errorThrown:!1,error:null}),e(),t){const{errorThrown:n,error:i}=jn;if(jn=null,n)throw i}}else e()}class kl extends ft{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,vh(t)&&t.add(this)):this.destination=Q0}static create(t,n,i){return new Ao(t,n,i)}next(t){this.isStopped?Pl(function W0(e){return Ol("N",e,void 0)}(t),this):this._next(t)}error(t){this.isStopped?Pl(function G0(e){return Ol("E",void 0,e)}(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Pl(z0,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}}const Z0=Function.prototype.bind;function Rl(e,t){return Z0.call(e,t)}class Y0{constructor(t){this.partialObserver=t}next(t){const{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(i){is(i)}}error(t){const{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(i){is(i)}else is(t)}complete(){const{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){is(n)}}}class Ao extends kl{constructor(t,n,i){let o;if(super(),ve(t)||!t)o={next:t??void 0,error:n??void 0,complete:i??void 0};else{let r;this&&Vn.useDeprecatedNextContext?(r=Object.create(t),r.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Rl(t.next,r),error:t.error&&Rl(t.error,r),complete:t.complete&&Rl(t.complete,r)}):o=t}this.destination=new Y0(o)}}function is(e){Vn.useDeprecatedSynchronousErrorHandling?function q0(e){Vn.useDeprecatedSynchronousErrorHandling&&jn&&(jn.errorThrown=!0,jn.error=e)}(e):xh(e)}function Pl(e,t){const{onStoppedNotification:n}=Vn;n&&ts.setTimeout(()=>n(e,t))}const Q0={closed:!0,next:Fl,error:function K0(e){throw e},complete:Fl},Ll="function"==typeof Symbol&&Symbol.observable||"@@observable";function To(e){return e}let Re=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){const i=new e;return i.source=this,i.operator=n,i}subscribe(n,i,o){const r=function J0(e){return e&&e instanceof kl||function X0(e){return e&&ve(e.next)&&ve(e.error)&&ve(e.complete)}(e)&&vh(e)}(n)?n:new Ao(n,i,o);return ns(()=>{const{operator:s,source:a}=this;r.add(s?s.call(r,a):a?this._subscribe(r):this._trySubscribe(r))}),r}_trySubscribe(n){try{return this._subscribe(n)}catch(i){n.error(i)}}forEach(n,i){return new(i=wh(i))((o,r)=>{const s=new Ao({next:a=>{try{n(a)}catch(l){r(l),s.unsubscribe()}},error:r,complete:o});this.subscribe(s)})}_subscribe(n){var i;return null===(i=this.source)||void 0===i?void 0:i.subscribe(n)}[Ll](){return this}pipe(...n){return function Dh(e){return 0===e.length?To:1===e.length?e[0]:function(n){return e.reduce((i,o)=>o(i),n)}}(n)(this)}toPromise(n){return new(n=wh(n))((i,o)=>{let r;this.subscribe(s=>r=s,s=>o(s),()=>i(r))})}}return e.create=t=>new e(t),e})();function wh(e){var t;return null!==(t=e??Vn.Promise)&&void 0!==t?t:Promise}const ew=bh(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});let et=(()=>{class e extends Re{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){const i=new Ch(this,this);return i.operator=n,i}_throwIfClosed(){if(this.closed)throw new ew}next(n){ns(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(const i of this.currentObservers)i.next(n)}})}error(n){ns(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;const{observers:i}=this;for(;i.length;)i.shift().error(n)}})}complete(){ns(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;const{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return(null===(n=this.observers)||void 0===n?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){const{hasError:i,isStopped:o,observers:r}=this;return i||o?_h:(this.currentObservers=null,r.push(n),new ft(()=>{this.currentObservers=null,es(r,n)}))}_checkFinalizedStatuses(n){const{hasError:i,thrownError:o,isStopped:r}=this;i?n.error(o):r&&n.complete()}asObservable(){const n=new Re;return n.source=this,n}}return e.create=(t,n)=>new Ch(t,n),e})();class Ch extends et{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,i;null===(i=null===(n=this.destination)||void 0===n?void 0:n.next)||void 0===i||i.call(n,t)}error(t){var n,i;null===(i=null===(n=this.destination)||void 0===n?void 0:n.error)||void 0===i||i.call(n,t)}complete(){var t,n;null===(n=null===(t=this.destination)||void 0===t?void 0:t.complete)||void 0===n||n.call(t)}_subscribe(t){var n,i;return null!==(i=null===(n=this.source)||void 0===n?void 0:n.subscribe(t))&&void 0!==i?i:_h}}class Eh extends et{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){const n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){const{hasError:t,thrownError:n,_value:i}=this;if(t)throw n;return this._throwIfClosed(),i}next(t){super.next(this._value=t)}}function Dt(e){return t=>{if(function tw(e){return ve(e?.lift)}(t))return t.lift(function(n){try{return e(n,this)}catch(i){this.error(i)}});throw new TypeError("Unable to lift unknown Observable type")}}function ht(e,t,n,i,o){return new nw(e,t,n,i,o)}class nw extends kl{constructor(t,n,i,o,r,s){super(t),this.onFinalize=r,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(l){t.error(l)}}:super._next,this._error=o?function(a){try{o(a)}catch(l){t.error(l)}finally{this.unsubscribe()}}:super._error,this._complete=i?function(){try{i()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){const{closed:n}=this;super.unsubscribe(),!n&&(null===(t=this.onFinalize)||void 0===t||t.call(this))}}}function Bn(e,t){return Dt((n,i)=>{let o=0;n.subscribe(ht(i,r=>{i.next(e.call(t,r,o++))}))})}function K(e){for(let t in e)if(e[t]===K)return t;throw Error("Could not find renamed property on target object.")}function os(e,t){for(const n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Ee(e){if("string"==typeof e)return e;if(Array.isArray(e))return"["+e.map(Ee).join(", ")+"]";if(null==e)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;const t=e.toString();if(null==t)return""+t;const n=t.indexOf("\n");return-1===n?t:t.substring(0,n)}function Vl(e,t){return null==e||""===e?null===t?"":t:null==t||""===t?e:e+" "+t}const iw=K({__forward_ref__:K});function J(e){return e.__forward_ref__=J,e.toString=function(){return Ee(this())},e}function T(e){return rs(e)?e():e}function rs(e){return"function"==typeof e&&e.hasOwnProperty(iw)&&e.__forward_ref__===J}function jl(e){return e&&!!e.\u0275providers}class D extends Error{constructor(t,n){super(function gi(e,t){return`NG0${Math.abs(e)}${t?": "+t:""}`}(t,n)),this.code=t}}const So=K({\u0275cmp:K}),Bl=K({\u0275dir:K}),Hl=K({\u0275pipe:K}),on=K({\u0275fac:K}),No=K({__NG_ELEMENT_ID__:K}),Ah=K({__NG_ENV_ID__:K});function R(e){return"string"==typeof e?e:null==e?"":String(e)}function Ul(e,t){throw new D(-201,!1)}function tt(e,t){null==e&&function S(e,t,n,i){throw new Error(`ASSERTION ERROR: ${e}`+(null==i?"":` [Expected=> ${n} ${i} ${t} <=Actual]`))}(t,e,null,"!=")}function O(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Oe(e){return{providers:e.providers||[],imports:e.imports||[]}}function ss(e){return Th(e,ls)||Th(e,Sh)}function Th(e,t){return e.hasOwnProperty(t)?e[t]:null}function as(e){return e&&(e.hasOwnProperty($l)||e.hasOwnProperty(uw))?e[$l]:null}const ls=K({\u0275prov:K}),$l=K({\u0275inj:K}),Sh=K({ngInjectableDef:K}),uw=K({ngInjectorDef:K});var U=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(U||{});let zl;function nt(e){const t=zl;return zl=e,t}function Fh(e,t,n){const i=ss(e);return i&&"root"==i.providedIn?void 0===i.value?i.value=i.factory():i.value:n&U.Optional?null:void 0!==t?t:void Ul()}const ee=globalThis;class w{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof n?this.__NG_ELEMENT_ID__=n:void 0!==n&&(this.\u0275prov=O({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}}const Fo={},Yl="__NG_DI_FLAG__",cs="ngTempTokenPath",mw=/\n/gm,kh="__source";let bi;function yn(e){const t=bi;return bi=e,t}function bw(e,t=U.Default){if(void 0===bi)throw new D(-203,!1);return null===bi?Fh(e,void 0,t):bi.get(e,t&U.Optional?null:void 0,t)}function C(e,t=U.Default){return(function Nh(){return zl}()||bw)(T(e),t)}function N(e,t=U.Default){return C(e,ds(t))}function ds(e){return typeof e>"u"||"number"==typeof e?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Kl(e){const t=[];for(let n=0;n<e.length;n++){const i=T(e[n]);if(Array.isArray(i)){if(0===i.length)throw new D(900,!1);let o,r=U.Default;for(let s=0;s<i.length;s++){const a=i[s],l=_w(a);"number"==typeof l?-1===l?o=a.token:r|=l:o=a}t.push(C(o,r))}else t.push(C(i))}return t}function _w(e){return e[Yl]}function rn(e){return{toString:e}.toString()}var us=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(us||{}),It=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(It||{});const jt={},z=[];function Ph(e,t,n){let i=e.length;for(;;){const o=e.indexOf(t,n);if(-1===o)return o;if(0===o||e.charCodeAt(o-1)<=32){const r=t.length;if(o+r===i||e.charCodeAt(o+r)<=32)return o}n=o+1}}function Ql(e,t,n){let i=0;for(;i<n.length;){const o=n[i];if("number"==typeof o){if(0!==o)break;i++;const r=n[i++],s=n[i++],a=n[i++];e.setAttribute(t,s,a,r)}else{const r=o,s=n[++i];Vh(r)?e.setProperty(t,r,s):e.setAttribute(t,r,s),i++}}return i}function Lh(e){return 3===e||4===e||6===e}function Vh(e){return 64===e.charCodeAt(0)}function ko(e,t){if(null!==t&&0!==t.length)if(null===e||0===e.length)e=t.slice();else{let n=-1;for(let i=0;i<t.length;i++){const o=t[i];"number"==typeof o?n=o:0===n||jh(e,n,o,null,-1===n||2===n?t[++i]:null)}}return e}function jh(e,t,n,i,o){let r=0,s=e.length;if(-1===t)s=-1;else for(;r<e.length;){const a=e[r++];if("number"==typeof a){if(a===t){s=-1;break}if(a>t){s=r-1;break}}}for(;r<e.length;){const a=e[r];if("number"==typeof a)break;if(a===n){if(null===i)return void(null!==o&&(e[r+1]=o));if(i===e[r+1])return void(e[r+2]=o)}r++,null!==i&&r++,null!==o&&r++}-1!==s&&(e.splice(s,0,t),r=s+1),e.splice(r++,0,n),null!==i&&e.splice(r++,0,i),null!==o&&e.splice(r++,0,o)}const Bh="ng-template";function xw(e,t,n){let i=0,o=!0;for(;i<e.length;){let r=e[i++];if("string"==typeof r&&o){const s=e[i++];if(n&&"class"===r&&-1!==Ph(s.toLowerCase(),t,0))return!0}else{if(1===r){for(;i<e.length&&"string"==typeof(r=e[i++]);)if(r.toLowerCase()===t)return!0;return!1}"number"==typeof r&&(o=!1)}}return!1}function Hh(e){return 4===e.type&&e.value!==Bh}function Dw(e,t,n){return t===(4!==e.type||n?e.value:Bh)}function ww(e,t,n){let i=4;const o=e.attrs||[],r=function Mw(e){for(let t=0;t<e.length;t++)if(Lh(e[t]))return t;return e.length}(o);let s=!1;for(let a=0;a<t.length;a++){const l=t[a];if("number"!=typeof l){if(!s)if(4&i){if(i=2|1&i,""!==l&&!Dw(e,l,n)||""===l&&1===t.length){if(At(i))return!1;s=!0}}else{const c=8&i?l:t[++a];if(8&i&&null!==e.attrs){if(!xw(e.attrs,c,n)){if(At(i))return!1;s=!0}continue}const u=Cw(8&i?"class":l,o,Hh(e),n);if(-1===u){if(At(i))return!1;s=!0;continue}if(""!==c){let f;f=u>r?"":o[u+1].toLowerCase();const h=8&i?f:null;if(h&&-1!==Ph(h,c,0)||2&i&&c!==f){if(At(i))return!1;s=!0}}}}else{if(!s&&!At(i)&&!At(l))return!1;if(s&&At(l))continue;s=!1,i=l|1&i}}return At(i)||s}function At(e){return 0==(1&e)}function Cw(e,t,n,i){if(null===t)return-1;let o=0;if(i||!n){let r=!1;for(;o<t.length;){const s=t[o];if(s===e)return o;if(3===s||6===s)r=!0;else{if(1===s||2===s){let a=t[++o];for(;"string"==typeof a;)a=t[++o];continue}if(4===s)break;if(0===s){o+=4;continue}}o+=r?1:2}return-1}return function Iw(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){const i=e[n];if("number"==typeof i)return-1;if(i===t)return n;n++}return-1}(t,e)}function Uh(e,t,n=!1){for(let i=0;i<t.length;i++)if(ww(e,t[i],n))return!0;return!1}function Aw(e,t){e:for(let n=0;n<t.length;n++){const i=t[n];if(e.length===i.length){for(let o=0;o<e.length;o++)if(e[o]!==i[o])continue e;return!0}}return!1}function $h(e,t){return e?":not("+t.trim()+")":t}function Tw(e){let t=e[0],n=1,i=2,o="",r=!1;for(;n<e.length;){let s=e[n];if("string"==typeof s)if(2&i){const a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else 8&i?o+="."+s:4&i&&(o+=" "+s);else""!==o&&!At(s)&&(t+=$h(r,o),o=""),i=s,r=r||!At(i);n++}return""!==o&&(t+=$h(r,o)),t}function _i(e){return rn(()=>{const t=Gh(e),n={...t,decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===us.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||It.Emulated,styles:e.styles||z,_:null,schemas:e.schemas||null,tView:null,id:""};Wh(n);const i=e.dependencies;return n.directiveDefs=fs(i,!1),n.pipeDefs=fs(i,!0),n.id=function Rw(e){let t=0;const n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(const o of n)t=Math.imul(31,t)+o.charCodeAt(0)<<0;return t+=2147483648,"c"+t}(n),n})}function Fw(e){return B(e)||Me(e)}function Ow(e){return null!==e}function Pe(e){return rn(()=>({type:e.type,bootstrap:e.bootstrap||z,declarations:e.declarations||z,imports:e.imports||z,exports:e.exports||z,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function zh(e,t){if(null==e)return jt;const n={};for(const i in e)if(e.hasOwnProperty(i)){let o=e[i],r=o;Array.isArray(o)&&(r=o[1],o=o[0]),n[o]=i,t&&(t[o]=r)}return n}function I(e){return rn(()=>{const t=Gh(e);return Wh(t),t})}function B(e){return e[So]||null}function Me(e){return e[Bl]||null}function ke(e){return e[Hl]||null}function Gh(e){const t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||jt,exportAs:e.exportAs||null,standalone:!0===e.standalone,signals:!0===e.signals,selectors:e.selectors||z,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:zh(e.inputs,t),outputs:zh(e.outputs),debugInfo:null}}function Wh(e){e.features?.forEach(t=>t(e))}function fs(e,t){if(!e)return null;const n=t?ke:Fw;return()=>("function"==typeof e?e():e).map(i=>n(i)).filter(Ow)}const ue=0,x=1,M=2,me=3,Tt=4,Ve=5,St=6,vi=7,te=8,Ge=9,Bt=10,L=11,Ro=12,qh=13,yi=14,ce=15,Po=16,xi=17,Ht=18,Lo=19,Zh=20,xn=21,hs=22,Un=23,P=25,Xl=1,Ut=7,Di=9,pe=10;var Jl=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(Jl||{});function je(e){return Array.isArray(e)&&"object"==typeof e[Xl]}function Be(e){return Array.isArray(e)&&!0===e[Xl]}function ec(e){return 0!=(4&e.flags)}function $n(e){return e.componentOffset>-1}function ps(e){return 1==(1&e.flags)}function Nt(e){return!!e.template}function tc(e){return 0!=(512&e[M])}function zn(e,t){return e.hasOwnProperty(on)?e[on]:null}class Xh{constructor(t,n,i){this.previousValue=t,this.currentValue=n,this.firstChange=i}isFirstChange(){return this.firstChange}}function $t(){return Jh}function Jh(e){return e.type.prototype.ngOnChanges&&(e.setInput=Bw),jw}function jw(){const e=tm(this),t=e?.current;if(t){const n=e.previous;if(n===jt)e.previous=t;else for(let i in t)n[i]=t[i];e.current=null,this.ngOnChanges(t)}}function Bw(e,t,n,i){const o=this.declaredInputs[n],r=tm(e)||function Hw(e,t){return e[em]=t}(e,{previous:jt,current:null}),s=r.current||(r.current={}),a=r.previous,l=a[o];s[o]=new Xh(l&&l.currentValue,t,a===jt),e[i]=t}$t.ngInherit=!0;const em="__ngSimpleChanges__";function tm(e){return e[em]||null}const zt=function(e,t,n){};let om=!1;function ne(e){for(;Array.isArray(e);)e=e[ue];return e}function jo(e,t){return ne(t[e])}function We(e,t){return ne(t[e.index])}function Bo(e,t){return e.data[t]}function pt(e,t){const n=t[e];return je(n)?n:n[ue]}function sc(e){return 128==(128&e[M])}function Gt(e,t){return null==t?null:e[t]}function rm(e){e[xi]=0}function qw(e){1024&e[M]||(e[M]|=1024,sc(e)&&Ho(e))}function am(e){return 9216&e[M]||e[Un]?.dirty}function ac(e){am(e)?Ho(e):64&e[M]&&(function $w(){return om}()?(e[M]|=1024,Ho(e)):e[Bt].changeDetectionScheduler?.notify())}function Ho(e){e[Bt].changeDetectionScheduler?.notify();let t=Gn(e);for(;null!==t&&!(8192&t[M])&&(t[M]|=8192,sc(t));)t=Gn(t)}function Gn(e){const t=e[me];return Be(t)?t[me]:t}const F={lFrame:bm(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function cm(){return F.bindingsEnabled}function Ci(){return null!==F.skipHydrationRootTNode}function b(){return F.lFrame.lView}function $(){return F.lFrame.tView}function Q(){let e=dm();for(;null!==e&&64===e.type;)e=e.parent;return e}function dm(){return F.lFrame.currentTNode}function Wt(e,t){const n=F.lFrame;n.currentTNode=e,n.isParent=t}function cc(){return F.lFrame.isParent}function dc(){F.lFrame.isParent=!1}function qt(){return F.lFrame.bindingIndex++}function an(e){const t=F.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function sC(e,t){const n=F.lFrame;n.bindingIndex=n.bindingRootIndex=e,uc(t)}function uc(e){F.lFrame.currentDirectiveIndex=e}function mm(){return F.lFrame.currentQueryIndex}function hc(e){F.lFrame.currentQueryIndex=e}function lC(e){const t=e[x];return 2===t.type?t.declTNode:1===t.type?e[Ve]:null}function pm(e,t,n){if(n&U.SkipSelf){let o=t,r=e;for(;!(o=o.parent,null!==o||n&U.Host||(o=lC(r),null===o||(r=r[yi],10&o.type))););if(null===o)return!1;t=o,e=r}const i=F.lFrame=gm();return i.currentTNode=t,i.lView=e,!0}function mc(e){const t=gm(),n=e[x];F.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function gm(){const e=F.lFrame,t=null===e?null:e.child;return null===t?bm(e):t}function bm(e){const t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return null!==e&&(e.child=t),t}function _m(){const e=F.lFrame;return F.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}const vm=_m;function pc(){const e=_m();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function He(){return F.lFrame.selectedIndex}function Wn(e){F.lFrame.selectedIndex=e}function se(){const e=F.lFrame;return Bo(e.tView,e.selectedIndex)}let xm=!0;function bs(){return xm}function Dn(e){xm=e}function _s(e,t){for(let n=t.directiveStart,i=t.directiveEnd;n<i;n++){const r=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:c,ngOnDestroy:d}=r;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),l&&(e.viewHooks??=[]).push(-n,l),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),null!=d&&(e.destroyHooks??=[]).push(n,d)}}function vs(e,t,n){Dm(e,t,3,n)}function ys(e,t,n,i){(3&e[M])===n&&Dm(e,t,n,i)}function gc(e,t){let n=e[M];(3&n)===t&&(n&=16383,n+=1,e[M]=n)}function Dm(e,t,n,i){const r=i??-1,s=t.length-1;let a=0;for(let l=void 0!==i?65535&e[xi]:0;l<s;l++)if("number"==typeof t[l+1]){if(a=t[l],null!=i&&a>=i)break}else t[l]<0&&(e[xi]+=65536),(a<r||-1==r)&&(pC(e,n,t,l),e[xi]=(**********&e[xi])+l+2),l++}function wm(e,t){zt(4,e,t);const n=de(null);try{t.call(e)}finally{de(n),zt(5,e,t)}}function pC(e,t,n,i){const o=n[i]<0,r=n[i+1],a=e[o?-n[i]:n[i]];o?e[M]>>14<e[xi]>>16&&(3&e[M])===t&&(e[M]+=16384,wm(a,r)):wm(a,r)}const Ei=-1;class $o{constructor(t,n,i){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=i}}function _c(e){return e!==Ei}function zo(e){return 32767&e}function Go(e,t){let n=function yC(e){return e>>16}(e),i=t;for(;n>0;)i=i[yi],n--;return i}let vc=!0;function xs(e){const t=vc;return vc=e,t}const Cm=255,Em=5;let xC=0;const Zt={};function Ds(e,t){const n=Mm(e,t);if(-1!==n)return n;const i=t[x];i.firstCreatePass&&(e.injectorIndex=t.length,yc(i.data,e),yc(t,null),yc(i.blueprint,null));const o=ws(e,t),r=e.injectorIndex;if(_c(o)){const s=zo(o),a=Go(o,t),l=a[x].data;for(let c=0;c<8;c++)t[r+c]=a[s+c]|l[s+c]}return t[r+8]=o,r}function yc(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Mm(e,t){return-1===e.injectorIndex||e.parent&&e.parent.injectorIndex===e.injectorIndex||null===t[e.injectorIndex+8]?-1:e.injectorIndex}function ws(e,t){if(e.parent&&-1!==e.parent.injectorIndex)return e.parent.injectorIndex;let n=0,i=null,o=t;for(;null!==o;){if(i=Om(o),null===i)return Ei;if(n++,o=o[yi],-1!==i.injectorIndex)return i.injectorIndex|n<<16}return Ei}function xc(e,t,n){!function DC(e,t,n){let i;"string"==typeof n?i=n.charCodeAt(0)||0:n.hasOwnProperty(No)&&(i=n[No]),null==i&&(i=n[No]=xC++);const o=i&Cm;t.data[e+(o>>Em)]|=1<<o}(e,t,n)}function Im(e,t,n){if(n&U.Optional||void 0!==e)return e;Ul()}function Am(e,t,n,i){if(n&U.Optional&&void 0===i&&(i=null),!(n&(U.Self|U.Host))){const o=e[Ge],r=nt(void 0);try{return o?o.get(t,i,n&U.Optional):Fh(t,i,n&U.Optional)}finally{nt(r)}}return Im(i,0,n)}function Tm(e,t,n,i=U.Default,o){if(null!==e){if(2048&t[M]&&!(i&U.Self)){const s=function IC(e,t,n,i,o){let r=e,s=t;for(;null!==r&&null!==s&&2048&s[M]&&!(512&s[M]);){const a=Sm(r,s,n,i|U.Self,Zt);if(a!==Zt)return a;let l=r.parent;if(!l){const c=s[Zh];if(c){const d=c.get(n,Zt,i);if(d!==Zt)return d}l=Om(s),s=s[yi]}r=l}return o}(e,t,n,i,Zt);if(s!==Zt)return s}const r=Sm(e,t,n,i,Zt);if(r!==Zt)return r}return Am(t,n,i,o)}function Sm(e,t,n,i,o){const r=function EC(e){if("string"==typeof e)return e.charCodeAt(0)||0;const t=e.hasOwnProperty(No)?e[No]:void 0;return"number"==typeof t?t>=0?t&Cm:MC:t}(n);if("function"==typeof r){if(!pm(t,e,i))return i&U.Host?Im(o,0,i):Am(t,n,i,o);try{let s;if(s=r(i),null!=s||i&U.Optional)return s;Ul()}finally{vm()}}else if("number"==typeof r){let s=null,a=Mm(e,t),l=Ei,c=i&U.Host?t[ce][Ve]:null;for((-1===a||i&U.SkipSelf)&&(l=-1===a?ws(e,t):t[a+8],l!==Ei&&Fm(i,!1)?(s=t[x],a=zo(l),t=Go(l,t)):a=-1);-1!==a;){const d=t[x];if(Nm(r,a,d.data)){const u=CC(a,t,n,s,i,c);if(u!==Zt)return u}l=t[a+8],l!==Ei&&Fm(i,t[x].data[a+8]===c)&&Nm(r,a,t)?(s=d,a=zo(l),t=Go(l,t)):a=-1}}return o}function CC(e,t,n,i,o,r){const s=t[x],a=s.data[e+8],d=Cs(a,s,n,null==i?$n(a)&&vc:i!=s&&0!=(3&a.type),o&U.Host&&r===a);return null!==d?qn(t,s,d,a):Zt}function Cs(e,t,n,i,o){const r=e.providerIndexes,s=t.data,a=1048575&r,l=e.directiveStart,d=r>>20,f=o?a+d:e.directiveEnd;for(let h=i?a:a+d;h<f;h++){const m=s[h];if(h<l&&n===m||h>=l&&m.type===n)return h}if(o){const h=s[l];if(h&&Nt(h)&&h.type===n)return l}return null}function qn(e,t,n,i){let o=e[n];const r=t.data;if(function gC(e){return e instanceof $o}(o)){const s=o;s.resolving&&function rw(e,t){const n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new D(-200,`Circular dependency in DI detected for ${e}${n}`)}(function G(e){return"function"==typeof e?e.name||e.toString():"object"==typeof e&&null!=e&&"function"==typeof e.type?e.type.name||e.type.toString():R(e)}(r[n]));const a=xs(s.canSeeViewProviders);s.resolving=!0;const c=s.injectImpl?nt(s.injectImpl):null;pm(e,i,U.Default);try{o=e[n]=s.factory(void 0,r,e,i),t.firstCreatePass&&n>=i.directiveStart&&function mC(e,t,n){const{ngOnChanges:i,ngOnInit:o,ngDoCheck:r}=t.type.prototype;if(i){const s=Jh(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),r&&((n.preOrderHooks??=[]).push(e,r),(n.preOrderCheckHooks??=[]).push(e,r))}(n,r[n],t)}finally{null!==c&&nt(c),xs(a),s.resolving=!1,vm()}}return o}function Nm(e,t,n){return!!(n[t+(e>>Em)]&1<<e)}function Fm(e,t){return!(e&U.Self||e&U.Host&&t)}class Ie{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,i){return Tm(this._tNode,this._lView,t,ds(i),n)}}function MC(){return new Ie(Q(),b())}function Dc(e){return rs(e)?()=>{const t=Dc(T(e));return t&&t()}:zn(e)}function Om(e){const t=e[x],n=t.type;return 2===n?t.declTNode:1===n?e[Ve]:null}function Oi(e,t){e.forEach(n=>Array.isArray(n)?Oi(n,t):t(n))}function Rm(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Es(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function gt(e,t,n){let i=ki(e,t);return i>=0?e[1|i]=n:(i=~i,function Pm(e,t,n,i){let o=e.length;if(o==t)e.push(n,i);else if(1===o)e.push(i,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;)e[o]=e[o-2],o--;e[t]=n,e[t+1]=i}}(e,i,t,n)),i}function Mc(e,t){const n=ki(e,t);if(n>=0)return e[1|n]}function ki(e,t){return function Lm(e,t,n){let i=0,o=e.length>>n;for(;o!==i;){const r=i+(o-i>>1),s=e[r<<n];if(t===s)return r<<n;s>t?o=r:i=r+1}return~(o<<n)}(e,t,1)}const Pi=new w("ENVIRONMENT_INITIALIZER"),Hm=new w("INJECTOR",-1),Nc=new w("INJECTOR_DEF_TYPES");class Ts{get(t,n=Fo){if(n===Fo){const i=new Error(`NullInjectorError: No provider for ${Ee(t)}!`);throw i.name="NullInjectorError",i}return n}}function Um(...e){return{\u0275providers:$m(0,e),\u0275fromNgModule:!0}}function $m(e,...t){const n=[],i=new Set;let o;const r=s=>{n.push(s)};return Oi(t,s=>{const a=s;Ss(a,r,[],i)&&(o||=[],o.push(a))}),void 0!==o&&zm(o,r),n}function zm(e,t){for(let n=0;n<e.length;n++){const{ngModule:i,providers:o}=e[n];Oc(o,r=>{t(r,i)})}}function Ss(e,t,n,i){if(!(e=T(e)))return!1;let o=null,r=as(e);const s=!r&&B(e);if(r||s){if(s&&!s.standalone)return!1;o=e}else{const l=e.ngModule;if(r=as(l),!r)return!1;o=l}const a=i.has(o);if(s){if(a)return!1;if(i.add(o),s.dependencies){const l="function"==typeof s.dependencies?s.dependencies():s.dependencies;for(const c of l)Ss(c,t,n,i)}}else{if(!r)return!1;{if(null!=r.imports&&!a){let c;i.add(o);try{Oi(r.imports,d=>{Ss(d,t,n,i)&&(c||=[],c.push(d))})}finally{}void 0!==c&&zm(c,t)}if(!a){const c=zn(o)||(()=>new o);t({provide:o,useFactory:c,deps:z},o),t({provide:Nc,useValue:o,multi:!0},o),t({provide:Pi,useValue:()=>C(o),multi:!0},o)}const l=r.providers;if(null!=l&&!a){const c=e;Oc(l,d=>{t(d,c)})}}}return o!==e&&void 0!==e.providers}function Oc(e,t){for(let n of e)jl(n)&&(n=n.\u0275providers),Array.isArray(n)?Oc(n,t):t(n)}const YC=K({provide:String,useValue:K});function kc(e){return null!==e&&"object"==typeof e&&YC in e}function Zn(e){return"function"==typeof e}const Rc=new w("Set Injector scope."),Ns={},QC={};let Pc;function Fs(){return void 0===Pc&&(Pc=new Ts),Pc}class ln{}class Li extends ln{get destroyed(){return this._destroyed}constructor(t,n,i,o){super(),this.parent=n,this.source=i,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,Vc(t,s=>this.processProvider(s)),this.records.set(Hm,Vi(void 0,this)),o.has("environment")&&this.records.set(ln,Vi(void 0,this));const r=this.records.get(Rc);null!=r&&"string"==typeof r.value&&this.scopes.add(r.value),this.injectorDefTypes=new Set(this.get(Nc,z,U.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;try{for(const n of this._ngOnDestroyHooks)n.ngOnDestroy();const t=this._onDestroyHooks;this._onDestroyHooks=[];for(const n of t)n()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear()}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();const n=yn(this),i=nt(void 0);try{return t()}finally{yn(n),nt(i)}}get(t,n=Fo,i=U.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(Ah))return t[Ah](this);i=ds(i);const r=yn(this),s=nt(void 0);try{if(!(i&U.SkipSelf)){let l=this.records.get(t);if(void 0===l){const c=function nE(e){return"function"==typeof e||"object"==typeof e&&e instanceof w}(t)&&ss(t);l=c&&this.injectableDefInScope(c)?Vi(Lc(t),Ns):null,this.records.set(t,l)}if(null!=l)return this.hydrate(t,l)}return(i&U.Self?Fs():this.parent).get(t,n=i&U.Optional&&n===Fo?null:n)}catch(a){if("NullInjectorError"===a.name){if((a[cs]=a[cs]||[]).unshift(Ee(t)),r)throw a;return function vw(e,t,n,i){const o=e[cs];throw t[kh]&&o.unshift(t[kh]),e.message=function yw(e,t,n,i=null){e=e&&"\n"===e.charAt(0)&&"\u0275"==e.charAt(1)?e.slice(2):e;let o=Ee(t);if(Array.isArray(t))o=t.map(Ee).join(" -> ");else if("object"==typeof t){let r=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];r.push(s+":"+("string"==typeof a?JSON.stringify(a):Ee(a)))}o=`{${r.join(", ")}}`}return`${n}${i?"("+i+")":""}[${o}]: ${e.replace(mw,"\n  ")}`}("\n"+e.message,o,n,i),e.ngTokenPath=o,e[cs]=null,e}(a,t,"R3InjectorError",this.source)}throw a}finally{nt(s),yn(r)}}resolveInjectorInitializers(){const t=yn(this),n=nt(void 0);try{const o=this.get(Pi,z,U.Self);for(const r of o)r()}finally{yn(t),nt(n)}}toString(){const t=[],n=this.records;for(const i of n.keys())t.push(Ee(i));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new D(205,!1)}processProvider(t){let n=Zn(t=T(t))?t:T(t&&t.provide);const i=function JC(e){return kc(e)?Vi(void 0,e.useValue):Vi(qm(e),Ns)}(t);if(!Zn(t)&&!0===t.multi){let o=this.records.get(n);o||(o=Vi(void 0,Ns,!0),o.factory=()=>Kl(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,i)}hydrate(t,n){return n.value===Ns&&(n.value=QC,n.value=n.factory()),"object"==typeof n.value&&n.value&&function tE(e){return null!==e&&"object"==typeof e&&"function"==typeof e.ngOnDestroy}(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}injectableDefInScope(t){if(!t.providedIn)return!1;const n=T(t.providedIn);return"string"==typeof n?"any"===n||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){const n=this._onDestroyHooks.indexOf(t);-1!==n&&this._onDestroyHooks.splice(n,1)}}function Lc(e){const t=ss(e),n=null!==t?t.factory:zn(e);if(null!==n)return n;if(e instanceof w)throw new D(204,!1);if(e instanceof Function)return function XC(e){if(e.length>0)throw new D(204,!1);const n=function dw(e){return e&&(e[ls]||e[Sh])||null}(e);return null!==n?()=>n.factory(e):()=>new e}(e);throw new D(204,!1)}function qm(e,t,n){let i;if(Zn(e)){const o=T(e);return zn(o)||Lc(o)}if(kc(e))i=()=>T(e.useValue);else if(function Wm(e){return!(!e||!e.useFactory)}(e))i=()=>e.useFactory(...Kl(e.deps||[]));else if(function Gm(e){return!(!e||!e.useExisting)}(e))i=()=>C(T(e.useExisting));else{const o=T(e&&(e.useClass||e.provide));if(!function eE(e){return!!e.deps}(e))return zn(o)||Lc(o);i=()=>new o(...Kl(e.deps))}return i}function Vi(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Vc(e,t){for(const n of e)Array.isArray(n)?Vc(n,t):n&&jl(n)?Vc(n.\u0275providers,t):t(n)}function Qm(e,t=null,n=null,i){const o=function Xm(e,t=null,n=null,i,o=new Set){const r=[n||z,Um(e)];return i=i||("object"==typeof e?void 0:Ee(e)),new Li(r,t||Fs(),i||null,o)}(e,t,n,i);return o.resolveInjectorInitializers(),o}let Hc,bt=(()=>{class e{static#e=this.THROW_IF_NOT_FOUND=Fo;static#t=this.NULL=new Ts;static create(n,i){if(Array.isArray(n))return Qm({name:""},i,n,"");{const o=n.name??"";return Qm({name:o},n.parent,n.providers,o)}}static#n=this.\u0275prov=O({token:e,providedIn:"any",factory:()=>C(Hm)});static#i=this.__NG_ELEMENT_ID__=-1}return e})();const ks=new w("AppId",{providedIn:"root",factory:()=>uE}),uE="ng",ep=new w("Platform Initializer"),Kn=new w("Platform ID",{providedIn:"platform",factory:()=>"unknown"}),Rs=new w("AnimationModuleType"),Uc=new w("CSP nonce",{providedIn:"root",factory:()=>function wn(){if(void 0!==Hc)return Hc;if(typeof document<"u")return document;throw new D(210,!1)}().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});function js(e){return 128==(128&e.flags)}var Mn=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Mn||{});const qc=new Map;let CE=0;const Yc="__ngContext__";function $e(e,t){je(t)?(e[Yc]=t[Lo],function ME(e){qc.set(e[Lo],e)}(t)):e[Yc]=t}let Kc;function Qc(e,t){return Kc(e,t)}function Bi(e,t,n,i,o){if(null!=i){let r,s=!1;Be(i)?r=i:je(i)&&(s=!0,i=i[ue]);const a=ne(i);0===e&&null!==n?null==o?Dp(t,n,a):Qn(t,n,a,o||null,!0):1===e&&null!==n?Qn(t,n,a,o||null,!0):2===e?function Ws(e,t,n){const i=zs(e,t);i&&function UE(e,t,n,i){e.removeChild(t,n,i)}(e,i,t,n)}(t,a,s):3===e&&t.destroyNode(a),null!=r&&function GE(e,t,n,i,o){const r=n[Ut];r!==ne(n)&&Bi(t,e,i,r,o);for(let a=pe;a<n.length;a++){const l=n[a];qs(l[x],l,e,t,i,r)}}(t,e,r,n,o)}}function Us(e,t,n){return e.createElement(t,n)}function vp(e,t){t[Bt].changeDetectionScheduler?.notify(),qs(e,t,t[L],2,null,null)}function yp(e,t){const n=e[Di],i=n.indexOf(t);n.splice(i,1)}function Xo(e,t){if(e.length<=pe)return;const n=pe+t,i=e[n];if(i){const o=i[Po];null!==o&&o!==e&&yp(o,i),t>0&&(e[n-1][Tt]=i[Tt]);const r=Es(e,pe+t);!function RE(e,t){vp(e,t),t[ue]=null,t[Ve]=null}(i[x],i);const s=r[Ht];null!==s&&s.detachView(r[x]),i[me]=null,i[Tt]=null,i[M]&=-129}return i}function $s(e,t){if(!(256&t[M])){const n=t[L];n.destroyNode&&qs(e,t,n,3,null,null),function LE(e){let t=e[Ro];if(!t)return Jc(e[x],e);for(;t;){let n=null;if(je(t))n=t[Ro];else{const i=t[pe];i&&(n=i)}if(!n){for(;t&&!t[Tt]&&t!==e;)je(t)&&Jc(t[x],t),t=t[me];null===t&&(t=e),je(t)&&Jc(t[x],t),n=t&&t[Tt]}t=n}}(t)}}function Jc(e,t){if(!(256&t[M])){t[M]&=-129,t[M]|=256,t[Un]&&function uh(e){if(pi(e),Io(e))for(let t=0;t<e.producerNode.length;t++)Jr(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}(t[Un]),function HE(e,t){let n;if(null!=e&&null!=(n=e.destroyHooks))for(let i=0;i<n.length;i+=2){const o=t[n[i]];if(!(o instanceof $o)){const r=n[i+1];if(Array.isArray(r))for(let s=0;s<r.length;s+=2){const a=o[r[s]],l=r[s+1];zt(4,a,l);try{l.call(a)}finally{zt(5,a,l)}}else{zt(4,o,r);try{r.call(o)}finally{zt(5,o,r)}}}}}(e,t),function BE(e,t){const n=e.cleanup,i=t[vi];if(null!==n)for(let r=0;r<n.length-1;r+=2)if("string"==typeof n[r]){const s=n[r+3];s>=0?i[s]():i[-s].unsubscribe(),r+=2}else n[r].call(i[n[r+1]]);null!==i&&(t[vi]=null);const o=t[xn];if(null!==o){t[xn]=null;for(let r=0;r<o.length;r++)(0,o[r])()}}(e,t),1===t[x].type&&t[L].destroy();const n=t[Po];if(null!==n&&Be(t[me])){n!==t[me]&&yp(n,t);const i=t[Ht];null!==i&&i.detachView(e)}!function IE(e){qc.delete(e[Lo])}(t)}}function ed(e,t,n){return function xp(e,t,n){let i=t;for(;null!==i&&40&i.type;)i=(t=i).parent;if(null===i)return n[ue];{const{componentOffset:o}=i;if(o>-1){const{encapsulation:r}=e.data[i.directiveStart+o];if(r===It.None||r===It.Emulated)return null}return We(i,n)}}(e,t.parent,n)}function Qn(e,t,n,i,o){e.insertBefore(t,n,i,o)}function Dp(e,t,n){e.appendChild(t,n)}function wp(e,t,n,i,o){null!==i?Qn(e,t,n,i,o):Dp(e,t,n)}function zs(e,t){return e.parentNode(t)}function Cp(e,t,n){return Mp(e,t,n)}let td,Mp=function Ep(e,t,n){return 40&e.type?We(e,n):null};function Gs(e,t,n,i){const o=ed(e,i,t),r=t[L],a=Cp(i.parent||t[Ve],i,t);if(null!=o)if(Array.isArray(n))for(let l=0;l<n.length;l++)wp(r,o,n[l],a,!1);else wp(r,o,n,a,!1);void 0!==td&&td(r,i,t,n,o)}function Jo(e,t){if(null!==t){const n=t.type;if(3&n)return We(t,e);if(4&n)return nd(-1,e[t.index]);if(8&n){const i=t.child;if(null!==i)return Jo(e,i);{const o=e[t.index];return Be(o)?nd(-1,o):ne(o)}}if(32&n)return Qc(t,e)()||ne(e[t.index]);{const i=Ap(e,t);return null!==i?Array.isArray(i)?i[0]:Jo(Gn(e[ce]),i):Jo(e,t.next)}}return null}function Ap(e,t){return null!==t?e[ce][Ve].projection[t.projection]:null}function nd(e,t){const n=pe+e+1;if(n<t.length){const i=t[n],o=i[x].firstChild;if(null!==o)return Jo(i,o)}return t[Ut]}function id(e,t,n,i,o,r,s){for(;null!=n;){const a=i[n.index],l=n.type;if(s&&0===t&&(a&&$e(ne(a),i),n.flags|=2),32!=(32&n.flags))if(8&l)id(e,t,n.child,i,o,r,!1),Bi(t,e,o,a,r);else if(32&l){const c=Qc(n,i);let d;for(;d=c();)Bi(t,e,o,d,r);Bi(t,e,o,a,r)}else 16&l?Sp(e,t,i,n,o,r):Bi(t,e,o,a,r);n=s?n.projectionNext:n.next}}function qs(e,t,n,i,o,r){id(n,i,e.firstChild,t,o,r,!1)}function Sp(e,t,n,i,o,r){const s=n[ce],l=s[Ve].projection[i.projection];if(Array.isArray(l))for(let c=0;c<l.length;c++)Bi(t,e,o,l[c],r);else{let c=l;const d=s[me];js(i)&&(c.flags|=128),id(e,t,c,d,o,r,!0)}}function Np(e,t,n){""===n?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Fp(e,t,n){const{mergedAttrs:i,classes:o,styles:r}=n;null!==i&&Ql(e,t,i),null!==o&&Np(e,t,o),null!==r&&function qE(e,t,n){e.setAttribute(t,"style",n)}(e,t,r)}class Pp{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see https://g.co/ng/security#xss)`}}function In(e){return e instanceof Pp?e.changingThisBreaksApplicationSecurity:e}class Gp{}let Wp=()=>null;function bd(e,t,n=!1){return Wp(e,t,n)}class AM{}class Yp{}class SM{resolveComponentFactory(t){throw function TM(e){const t=Error(`No component factory found for ${Ee(e)}.`);return t.ngComponent=e,t}(t)}}let Gi=(()=>{class e{static#e=this.NULL=new SM}return e})();function NM(){return Wi(Q(),b())}function Wi(e,t){return new ye(We(e,t))}let ye=(()=>{class e{constructor(n){this.nativeElement=n}static#e=this.__NG_ELEMENT_ID__=NM}return e})();function FM(e){return e instanceof ye?e.nativeElement:e}class Qp{}let Xn=(()=>{class e{constructor(){this.destroyNode=null}static#e=this.__NG_ELEMENT_ID__=()=>function OM(){const e=b(),n=pt(Q().index,e);return(je(n)?n:e)[L]}()}return e})(),kM=(()=>{class e{static#e=this.\u0275prov=O({token:e,providedIn:"root",factory:()=>null})}return e})();const yd={};function rr(e,t,n,i,o=!1){for(;null!==n;){const r=t[n.index];null!==r&&i.push(ne(r)),Be(r)&&rg(r,i);const s=n.type;if(8&s)rr(e,t,n.child,i);else if(32&s){const a=Qc(n,t);let l;for(;l=a();)i.push(l)}else if(16&s){const a=Ap(t,n);if(Array.isArray(a))i.push(...a);else{const l=Gn(t[ce]);rr(l[x],l,a,i,!0)}}n=o?n.projectionNext:n.next}return i}function rg(e,t){for(let n=pe;n<e.length;n++){const i=e[n],o=i[x].firstChild;null!==o&&rr(i[x],i,o,t)}e[Ut]!==e[ue]&&t.push(e[Ut])}let sg=[];const ZM={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{},consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{Ho(e.lView)},consumerOnSignalRead(){this.lView[Un]=this}};function ag(e){return cg(e[Ro])}function lg(e){return cg(e[Tt])}function cg(e){for(;null!==e&&!Be(e);)e=e[Tt];return e}function wd(e){return e.ngOriginalError}class dn{constructor(){this._console=console}handleError(t){const n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&wd(t);for(;n&&wd(n);)n=wd(n);return n||null}}const ug=new w("",{providedIn:"root",factory:()=>N(dn).handleError.bind(void 0)}),hg=new w("",{providedIn:"root",factory:()=>!1}),V={};function ge(e=1){bg($(),b(),He()+e,!1)}function bg(e,t,n,i){if(!i)if(3==(3&t[M])){const r=e.preOrderCheckHooks;null!==r&&vs(t,r,n)}else{const r=e.preOrderHooks;null!==r&&ys(t,r,0,n)}Wn(n)}function _(e,t=U.Default){const n=b();return null===n?C(e,t):Tm(Q(),n,T(e),t)}function ra(e,t,n,i,o,r,s,a,l,c,d){const u=t.blueprint.slice();return u[ue]=o,u[M]=204|i,(null!==c||e&&2048&e[M])&&(u[M]|=2048),rm(u),u[me]=u[yi]=e,u[te]=n,u[Bt]=s||e&&e[Bt],u[L]=a||e&&e[L],u[Ge]=l||e&&e[Ge]||null,u[Ve]=r,u[Lo]=function EE(){return CE++}(),u[St]=d,u[Zh]=c,u[ce]=2==t.type?e[ce]:u,u}function qi(e,t,n,i,o){let r=e.data[t];if(null===r)r=function Cd(e,t,n,i,o){const r=dm(),s=cc(),l=e.data[t]=function cI(e,t,n,i,o,r){let s=t?t.injectorIndex:-1,a=0;return Ci()&&(a|=128),{type:n,index:i,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:r,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,s?r:r&&r.parent,n,t,i,o);return null===e.firstChild&&(e.firstChild=l),null!==r&&(s?null==r.child&&null!==l.parent&&(r.child=l):null===r.next&&(r.next=l,l.prev=r)),l}(e,t,n,i,o),function rC(){return F.lFrame.inI18n}()&&(r.flags|=32);else if(64&r.type){r.type=n,r.value=i,r.attrs=o;const s=function Uo(){const e=F.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}();r.injectorIndex=null===s?-1:s.injectorIndex}return Wt(r,!0),r}function sr(e,t,n,i){if(0===n)return-1;const o=t.length;for(let r=0;r<n;r++)t.push(i),e.blueprint.push(i),e.data.push(null);return o}function vg(e,t,n,i,o){const r=He(),s=2&i;try{Wn(-1),s&&t.length>P&&bg(e,t,P,!1),zt(s?2:0,o),n(i,o)}finally{Wn(r),zt(s?3:1,o)}}function Ed(e,t,n){if(ec(t)){const i=de(null);try{const r=t.directiveEnd;for(let s=t.directiveStart;s<r;s++){const a=e.data[s];a.contentQueries&&a.contentQueries(1,n[s],s)}}finally{de(i)}}}function Md(e,t,n){cm()&&(function gI(e,t,n,i){const o=n.directiveStart,r=n.directiveEnd;$n(n)&&function wI(e,t,n){const i=We(t,e),o=yg(n);let s=16;n.signals?s=4096:n.onPush&&(s=64);const a=sa(e,ra(e,o,null,s,i,t,null,e[Bt].rendererFactory.createRenderer(i,n),null,null,null));e[t.index]=a}(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||Ds(n,t),$e(i,t);const s=n.initialInputs;for(let a=o;a<r;a++){const l=e.data[a],c=qn(t,e,a,n);$e(c,t),null!==s&&CI(0,a-o,c,l,0,s),Nt(l)&&(pt(n.index,t)[te]=qn(t,e,a,n))}}(e,t,n,We(n,t)),64==(64&n.flags)&&Eg(e,t,n))}function Id(e,t,n=We){const i=t.localNames;if(null!==i){let o=t.index+1;for(let r=0;r<i.length;r+=2){const s=i[r+1],a=-1===s?n(t,e):e[s];e[o++]=a}}}function yg(e){const t=e.tView;return null===t||t.incompleteFirstPass?e.tView=Ad(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Ad(e,t,n,i,o,r,s,a,l,c,d){const u=P+i,f=u+o,h=function iI(e,t){const n=[];for(let i=0;i<t;i++)n.push(i<e?null:V);return n}(u,f),m="function"==typeof c?c():c;return h[x]={type:e,blueprint:h,template:n,queries:null,viewQuery:a,declTNode:t,data:h.slice().fill(null,u),bindingStartIndex:u,expandoStartIndex:f,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof r?r():r,pipeRegistry:"function"==typeof s?s():s,firstChild:null,schemas:l,consts:m,incompleteFirstPass:!1,ssrId:d}}let xg=()=>null;function Dg(e,t,n,i){for(let o in e)if(e.hasOwnProperty(o)){n=null===n?{}:n;const r=e[o];null===i?wg(n,t,o,r):i.hasOwnProperty(o)&&wg(n,t,i[o],r)}return n}function wg(e,t,n,i){e.hasOwnProperty(n)?e[n].push(t,i):e[n]=[t,i]}function vt(e,t,n,i,o,r,s,a){const l=We(t,n);let d,c=t.inputs;!a&&null!=c&&(d=c[i])?(Od(e,n,d,i,o),$n(t)&&function fI(e,t){const n=pt(t,e);16&n[M]||(n[M]|=64)}(n,t.index)):3&t.type&&(i=function uI(e){return"class"===e?"className":"for"===e?"htmlFor":"formaction"===e?"formAction":"innerHtml"===e?"innerHTML":"readonly"===e?"readOnly":"tabindex"===e?"tabIndex":e}(i),o=null!=s?s(o,t.value||"",i):o,r.setProperty(l,i,o))}function Td(e,t,n,i){if(cm()){const o=null===i?null:{"":-1},r=function _I(e,t){const n=e.directiveRegistry;let i=null,o=null;if(n)for(let r=0;r<n.length;r++){const s=n[r];if(Uh(t,s.selectors,!1))if(i||(i=[]),Nt(s))if(null!==s.findHostDirectiveDefs){const a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),i.unshift(...a,s),Sd(e,t,a.length)}else i.unshift(s),Sd(e,t,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,i,o),i.push(s)}return null===i?null:[i,o]}(e,n);let s,a;null===r?s=a=null:[s,a]=r,null!==s&&Cg(e,t,n,s,o,a),o&&function vI(e,t,n){if(t){const i=e.localNames=[];for(let o=0;o<t.length;o+=2){const r=n[t[o+1]];if(null==r)throw new D(-301,!1);i.push(t[o],r)}}}(n,i,o)}n.mergedAttrs=ko(n.mergedAttrs,n.attrs)}function Cg(e,t,n,i,o,r){for(let c=0;c<i.length;c++)xc(Ds(n,t),e,i[c].type);!function xI(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}(n,e.data.length,i.length);for(let c=0;c<i.length;c++){const d=i[c];d.providersResolver&&d.providersResolver(d)}let s=!1,a=!1,l=sr(e,t,i.length,null);for(let c=0;c<i.length;c++){const d=i[c];n.mergedAttrs=ko(n.mergedAttrs,d.hostAttrs),DI(e,n,t,l,d),yI(l,d,o),null!==d.contentQueries&&(n.flags|=4),(null!==d.hostBindings||null!==d.hostAttrs||0!==d.hostVars)&&(n.flags|=64);const u=d.type.prototype;!s&&(u.ngOnChanges||u.ngOnInit||u.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(u.ngOnChanges||u.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),l++}!function dI(e,t,n){const o=t.directiveEnd,r=e.data,s=t.attrs,a=[];let l=null,c=null;for(let d=t.directiveStart;d<o;d++){const u=r[d],f=n?n.get(u):null,m=f?f.outputs:null;l=Dg(u.inputs,d,l,f?f.inputs:null),c=Dg(u.outputs,d,c,m);const p=null===l||null===s||Hh(t)?null:EI(l,d,s);a.push(p)}null!==l&&(l.hasOwnProperty("class")&&(t.flags|=8),l.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=l,t.outputs=c}(e,n,r)}function Eg(e,t,n){const i=n.directiveStart,o=n.directiveEnd,r=n.index,s=function aC(){return F.lFrame.currentDirectiveIndex}();try{Wn(r);for(let a=i;a<o;a++){const l=e.data[a],c=t[a];uc(a),(null!==l.hostBindings||0!==l.hostVars||null!==l.hostAttrs)&&bI(l,c)}}finally{Wn(-1),uc(s)}}function bI(e,t){null!==e.hostBindings&&e.hostBindings(1,t)}function Sd(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function yI(e,t,n){if(n){if(t.exportAs)for(let i=0;i<t.exportAs.length;i++)n[t.exportAs[i]]=e;Nt(t)&&(n[""]=e)}}function DI(e,t,n,i,o){e.data[i]=o;const r=o.factory||(o.factory=zn(o.type)),s=new $o(r,Nt(o),_);e.blueprint[i]=s,n[i]=s,function mI(e,t,n,i,o){const r=o.hostBindings;if(r){let s=e.hostBindingOpCodes;null===s&&(s=e.hostBindingOpCodes=[]);const a=~t.index;(function pI(e){let t=e.length;for(;t>0;){const n=e[--t];if("number"==typeof n&&n<0)return n}return 0})(s)!=a&&s.push(a),s.push(n,i,r)}}(e,t,i,sr(e,n,o.hostVars,V),o)}function Yt(e,t,n,i,o,r){const s=We(e,t);!function Nd(e,t,n,i,o,r,s){if(null==r)e.removeAttribute(t,o,n);else{const a=null==s?R(r):s(r,i||"",o);e.setAttribute(t,o,a,n)}}(t[L],s,r,e.value,n,i,o)}function CI(e,t,n,i,o,r){const s=r[t];if(null!==s)for(let a=0;a<s.length;)Mg(i,n,s[a++],s[a++],s[a++])}function Mg(e,t,n,i,o){const r=de(null);try{const s=e.inputTransforms;null!==s&&s.hasOwnProperty(i)&&(o=s[i].call(t,o)),null!==e.setInput?e.setInput(t,o,n,i):t[i]=o}finally{de(r)}}function EI(e,t,n){let i=null,o=0;for(;o<n.length;){const r=n[o];if(0!==r)if(5!==r){if("number"==typeof r)break;if(e.hasOwnProperty(r)){null===i&&(i=[]);const s=e[r];for(let a=0;a<s.length;a+=2)if(s[a]===t){i.push(r,s[a+1],n[o+1]);break}}o+=2}else o+=2;else o+=4}return i}function Ig(e,t,n,i){return[e,!0,0,t,null,i,null,n,null,null]}function Ag(e,t){const n=e.contentQueries;if(null!==n){const i=de(null);try{for(let o=0;o<n.length;o+=2){const s=n[o+1];if(-1!==s){const a=e.data[s];hc(n[o]),a.contentQueries(2,t[s],s)}}}finally{de(i)}}}function sa(e,t){return e[Ro]?e[qh][Tt]=t:e[Ro]=t,e[qh]=t,t}function Fd(e,t,n){hc(0);const i=de(null);try{t(e,n)}finally{de(i)}}function Tg(e){return e[vi]||(e[vi]=[])}function Sg(e){return e.cleanup||(e.cleanup=[])}function aa(e,t){const n=e[Ge],i=n?n.get(dn,null):null;i&&i.handleError(t)}function Od(e,t,n,i,o){for(let r=0;r<n.length;){const s=n[r++],a=n[r++];Mg(e.data[s],t[s],i,a,o)}}function AI(e,t,n,i){const o=t[M];if(256==(256&o))return;t[Bt].inlineEffectRunner?.flush(),mc(t);let s=null,a=null;(function TI(e){return 2!==e.type})(e)&&(a=function GM(e){return e[Un]??function WM(e){const t=sg.pop()??Object.create(ZM);return t.lView=e,t}(e)}(t),s=function ch(e){return e&&(e.nextProducerIndex=0),de(e)}(a));try{rm(t),function fm(e){return F.lFrame.bindingIndex=e}(e.bindingStartIndex),null!==n&&vg(e,t,n,2,i);const l=3==(3&o);if(l){const u=e.preOrderCheckHooks;null!==u&&vs(t,u,null)}else{const u=e.preOrderHooks;null!==u&&ys(t,u,0,null),gc(t,0)}if(function SI(e){for(let t=ag(e);null!==t;t=lg(t)){if(!(t[M]&Jl.HasTransplantedViews))continue;const n=t[Di];for(let i=0;i<n.length;i++){qw(n[i])}}}(t),Fg(t,0),null!==e.contentQueries&&Ag(e,t),l){const u=e.contentCheckHooks;null!==u&&vs(t,u)}else{const u=e.contentHooks;null!==u&&ys(t,u,1),gc(t,1)}!function nI(e,t){const n=e.hostBindingOpCodes;if(null!==n)try{for(let i=0;i<n.length;i++){const o=n[i];if(o<0)Wn(~o);else{const r=o,s=n[++i],a=n[++i];sC(s,r),a(2,t[r])}}}finally{Wn(-1)}}(e,t);const c=e.components;null!==c&&kg(t,c,0);const d=e.viewQuery;if(null!==d&&Fd(2,d,i),l){const u=e.viewCheckHooks;null!==u&&vs(t,u)}else{const u=e.viewHooks;null!==u&&ys(t,u,2),gc(t,2)}if(!0===e.firstUpdatePass&&(e.firstUpdatePass=!1),t[hs]){for(const u of t[hs])u();t[hs]=null}t[M]&=-73}catch(l){throw Ho(t),l}finally{null!==a&&(function dh(e,t){if(de(t),e&&void 0!==e.producerNode&&void 0!==e.producerIndexOfThis&&void 0!==e.producerLastReadVersion){if(Io(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Jr(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}(a,s),function qM(e){e.lView[Un]!==e&&(e.lView=null,sg.push(e))}(a)),pc()}}function Fg(e,t){for(let n=ag(e);null!==n;n=lg(n))for(let i=pe;i<n.length;i++)Og(n[i],t)}function NI(e,t,n){Og(pt(t,e),n)}function Og(e,t){sc(e)&&Rd(e,t)}function Rd(e,t){const i=e[x],o=e[M],r=e[Un];let s=!!(0===t&&16&o);if(s||=!!(64&o&&0===t),s||=!!(1024&o),s||=!(!r?.dirty||!Al(r)),r&&(r.dirty=!1),e[M]&=-9217,s)AI(i,e,i.template,e[te]);else if(8192&o){Fg(e,1);const a=i.components;null!==a&&kg(e,a,1)}}function kg(e,t,n){for(let i=0;i<t.length;i++)NI(e,t[i],n)}function ar(e){for(e[Bt].changeDetectionScheduler?.notify();e;){e[M]|=64;const t=Gn(e);if(tc(e)&&!t)return e;e=t}return null}class lr{get rootNodes(){const t=this._lView,n=t[x];return rr(n,t,n.firstChild,[])}constructor(t,n,i=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=i,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[te]}set context(t){this._lView[te]=t}get destroyed(){return 256==(256&this._lView[M])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const t=this._lView[me];if(Be(t)){const n=t[8],i=n?n.indexOf(this):-1;i>-1&&(Xo(t,i),Es(n,i))}this._attachedToViewContainer=!1}$s(this._lView[x],this._lView)}onDestroy(t){!function gs(e,t){if(256==(256&e[M]))throw new D(911,!1);null===e[xn]&&(e[xn]=[]),e[xn].push(t)}(this._lView,t)}markForCheck(){ar(this._cdRefInjectingView||this._lView)}detach(){this._lView[M]&=-129}reattach(){ac(this._lView),this._lView[M]|=128}detectChanges(){this._lView[M]|=1024,function kd(e,t=!0){const n=e[Bt],i=n.rendererFactory;i.begin?.();try{!function II(e){Rd(e,0);let t=0;for(;am(e);){if(100===t)throw new D(103,!1);t++,Rd(e,1)}}(e)}catch(r){throw t&&aa(e,r),r}finally{i.end?.(),n.inlineEffectRunner?.flush()}}(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new D(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,vp(this._lView[x],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new D(902,!1);this._appRef=t,ac(this._lView)}}let cr=(()=>{class e{static#e=this.__NG_ELEMENT_ID__=FI}return e})();function FI(e){return function OI(e,t,n){if($n(e)&&!n){const i=pt(e.index,t);return new lr(i,i)}return 47&e.type?new lr(t[ce],t):null}(Q(),b(),16==(16&e))}const Rg=new Set;function Jn(e){Rg.has(e)||(Rg.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}function Ld(e){return t=>{setTimeout(e,void 0,t)}}const Ne=class $I extends et{constructor(t=!1){super(),this.__isAsync=t}emit(t){super.next(t)}subscribe(t,n,i){let o=t,r=n||(()=>null),s=i;if(t&&"object"==typeof t){const l=t;o=l.next?.bind(l),r=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(r=Ld(r),o&&(o=Ld(o)),s&&(s=Ld(s)));const a=super.subscribe({next:o,error:r,complete:s});return t instanceof ft&&t.add(a),a}};function Pg(...e){}class H{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:i=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new Ne(!1),this.onMicrotaskEmpty=new Ne(!1),this.onStable=new Ne(!1),this.onError=new Ne(!1),typeof Zone>"u")throw new D(908,!1);Zone.assertZonePatched();const o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!i&&n,o.shouldCoalesceRunChangeDetection=i,o.lastRequestAnimationFrameId=-1,o.nativeRequestAnimationFrame=function zI(){const e="function"==typeof ee.requestAnimationFrame;let t=ee[e?"requestAnimationFrame":"setTimeout"],n=ee[e?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&t&&n){const i=t[Zone.__symbol__("OriginalDelegate")];i&&(t=i);const o=n[Zone.__symbol__("OriginalDelegate")];o&&(n=o)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:n}}().nativeRequestAnimationFrame,function qI(e){const t=()=>{!function WI(e){e.isCheckStableRunning||-1!==e.lastRequestAnimationFrameId||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(ee,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,jd(e),e.isCheckStableRunning=!0,Vd(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),jd(e))}(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,i,o,r,s,a)=>{if(function ZI(e){return!(!Array.isArray(e)||1!==e.length)&&!0===e[0].data?.__ignore_ng_zone__}(a))return n.invokeTask(o,r,s,a);try{return Lg(e),n.invokeTask(o,r,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&"eventTask"===r.type||e.shouldCoalesceRunChangeDetection)&&t(),Vg(e)}},onInvoke:(n,i,o,r,s,a,l)=>{try{return Lg(e),n.invoke(o,r,s,a,l)}finally{e.shouldCoalesceRunChangeDetection&&t(),Vg(e)}},onHasTask:(n,i,o,r)=>{n.hasTask(o,r),i===o&&("microTask"==r.change?(e._hasPendingMicrotasks=r.microTask,jd(e),Vd(e)):"macroTask"==r.change&&(e.hasPendingMacrotasks=r.macroTask))},onHandleError:(n,i,o,r)=>(n.handleError(o,r),e.runOutsideAngular(()=>e.onError.emit(r)),!1)})}(o)}static isInAngularZone(){return typeof Zone<"u"&&!0===Zone.current.get("isAngularZone")}static assertInAngularZone(){if(!H.isInAngularZone())throw new D(909,!1)}static assertNotInAngularZone(){if(H.isInAngularZone())throw new D(909,!1)}run(t,n,i){return this._inner.run(t,n,i)}runTask(t,n,i,o){const r=this._inner,s=r.scheduleEventTask("NgZoneEvent: "+o,t,GI,Pg,Pg);try{return r.runTask(s,n,i)}finally{r.cancelTask(s)}}runGuarded(t,n,i){return this._inner.runGuarded(t,n,i)}runOutsideAngular(t){return this._outer.run(t)}}const GI={};function Vd(e){if(0==e._nesting&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function jd(e){e.hasPendingMicrotasks=!!(e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&-1!==e.lastRequestAnimationFrameId)}function Lg(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Vg(e){e._nesting--,Vd(e)}let dr=(()=>{class e{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){const n=[...this.internalCallbacks];this.internalCallbacks.length=0;for(const o of n)o();return!!this.handler?.execute()||n.length>0}ngOnDestroy(){this.handler?.destroy(),this.handler=null,this.internalCallbacks.length=0}static#e=this.\u0275prov=O({token:e,providedIn:"root",factory:()=>new e})}return e})();function XI(e,t){const n=pt(t,e),i=n[x];!function JI(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}(i,n);const o=n[ue];null!==o&&null===n[St]&&(n[St]=bd(o,n[Ge])),Bd(i,n,n[te])}function Bd(e,t,n){mc(t);try{const i=e.viewQuery;null!==i&&Fd(1,i,n);const o=e.template;null!==o&&vg(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),e.staticContentQueries&&Ag(e,t),e.staticViewQueries&&Fd(2,e.viewQuery,n);const r=e.components;null!==r&&function eA(e,t){for(let n=0;n<t.length;n++)XI(e,t[n])}(t,r)}catch(i){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),i}finally{t[M]&=-5,pc()}}function la(e,t,n){let i=n?e.styles:null,o=n?e.classes:null,r=0;if(null!==t)for(let s=0;s<t.length;s++){const a=t[s];"number"==typeof a?r=a:1==r?o=Vl(o,a):2==r&&(i=Vl(i,a+": "+t[++s]+";"))}n?e.styles=i:e.stylesWithoutHost=i,n?e.classes=o:e.classesWithoutHost=o}class zg extends Gi{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){const n=B(t);return new ur(n,this.ngModule)}}function Gg(e){const t=[];for(let n in e)e.hasOwnProperty(n)&&t.push({propName:e[n],templateName:n});return t}class nA{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,i){i=ds(i);const o=this.injector.get(t,yd,i);return o!==yd||n===yd?o:this.parentInjector.get(t,n,i)}}class ur extends Yp{get inputs(){const t=this.componentDef,n=t.inputTransforms,i=Gg(t.inputs);if(null!==n)for(const o of i)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return i}get outputs(){return Gg(this.componentDef.outputs)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=function Sw(e){return e.map(Tw).join(",")}(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,i,o){let r=(o=o||this.ngModule)instanceof ln?o:o?.injector;r&&null!==this.componentDef.getStandaloneInjector&&(r=this.componentDef.getStandaloneInjector(r)||r);const s=r?new nA(t,r):t,a=s.get(Qp,null);if(null===a)throw new D(407,!1);const u={rendererFactory:a,sanitizer:s.get(kM,null),inlineEffectRunner:null,afterRenderEventManager:s.get(dr,null),changeDetectionScheduler:s.get(Gp,null)},f=a.createRenderer(null,this.componentDef),h=this.componentDef.selectors[0][0]||"div",m=i?function oI(e,t,n,i){const r=i.get(hg,!1)||n===It.ShadowDom,s=e.selectRootElement(t,r);return function rI(e){xg(e)}(s),s}(f,i,this.componentDef.encapsulation,s):Us(f,h,function tA(e){const t=e.toLowerCase();return"svg"===t?"svg":"math"===t?"math":null}(h));let p=512;this.componentDef.signals?p|=4096:this.componentDef.onPush||(p|=16);let v=null;null!==m&&(v=bd(m,s,!0));const y=Ad(0,null,null,1,0,null,null,null,null,null,null),g=ra(null,y,null,p,null,null,u,f,s,null,v);let E,k;mc(g);try{const j=this.componentDef;let Ce,Cl=null;j.findHostDirectiveDefs?(Ce=[],Cl=new Map,j.findHostDirectiveDefs(j,Ce,Cl),Ce.push(j)):Ce=[j];const oj=function oA(e,t){const n=e[x],i=P;return e[i]=t,qi(n,i,2,"#host",null)}(g,m),rj=function rA(e,t,n,i,o,r,s){const a=o[x];!function sA(e,t,n,i){for(const o of e)t.mergedAttrs=ko(t.mergedAttrs,o.hostAttrs);null!==t.mergedAttrs&&(la(t,t.mergedAttrs,!0),null!==n&&Fp(i,n,t))}(i,e,t,s);let l=null;null!==t&&(l=bd(t,o[Ge]));const c=r.rendererFactory.createRenderer(t,n);let d=16;n.signals?d=4096:n.onPush&&(d=64);const u=ra(o,yg(n),null,d,o[e.index],e,r,c,null,null,l);return a.firstCreatePass&&Sd(a,e,i.length-1),sa(o,u),o[e.index]=u}(oj,m,j,Ce,g,u,f);k=Bo(y,P),m&&function lA(e,t,n,i){if(i)Ql(e,n,["ng-version","17.0.9"]);else{const{attrs:o,classes:r}=function Nw(e){const t=[],n=[];let i=1,o=2;for(;i<e.length;){let r=e[i];if("string"==typeof r)2===o?""!==r&&t.push(r,e[++i]):8===o&&n.push(r);else{if(!At(o))break;o=r}i++}return{attrs:t,classes:n}}(t.selectors[0]);o&&Ql(e,n,o),r&&r.length>0&&Np(e,n,r.join(" "))}}(f,j,m,i),void 0!==n&&function cA(e,t,n){const i=e.projection=[];for(let o=0;o<t.length;o++){const r=n[o];i.push(null!=r?Array.from(r):null)}}(k,this.ngContentSelectors,n),E=function aA(e,t,n,i,o,r){const s=Q(),a=o[x],l=We(s,o);Cg(a,o,s,n,null,i);for(let d=0;d<n.length;d++)$e(qn(o,a,s.directiveStart+d,s),o);Eg(a,o,s),l&&$e(l,o);const c=qn(o,a,s.directiveStart+s.componentOffset,s);if(e[te]=o[te]=c,null!==r)for(const d of r)d(c,t);return Ed(a,s,e),c}(rj,j,Ce,Cl,g,[dA]),Bd(y,g,null)}finally{pc()}return new iA(this.componentType,E,Wi(k,g),g,k)}}class iA extends AM{constructor(t,n,i,o,r){super(),this.location=i,this._rootLView=o,this._tNode=r,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new lr(o,void 0,!1),this.componentType=t}setInput(t,n){const i=this._tNode.inputs;let o;if(null!==i&&(o=i[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;const r=this._rootLView;Od(r[x],r,o,t,n),this.previousInputValues.set(t,n),ar(pt(this._tNode.index,r))}}get injector(){return new Ie(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}}function dA(){const e=Q();_s(b()[x],e)}function W(e){let t=function Wg(e){return Object.getPrototypeOf(e.prototype).constructor}(e.type),n=!0;const i=[e];for(;t;){let o;if(Nt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new D(903,!1);o=t.\u0275dir}if(o){if(n){i.push(o);const s=e;s.inputs=ca(e.inputs),s.inputTransforms=ca(e.inputTransforms),s.declaredInputs=ca(e.declaredInputs),s.outputs=ca(e.outputs);const a=o.hostBindings;a&&mA(e,a);const l=o.viewQuery,c=o.contentQueries;if(l&&fA(e,l),c&&hA(e,c),os(e.inputs,o.inputs),os(e.declaredInputs,o.declaredInputs),os(e.outputs,o.outputs),null!==o.inputTransforms&&(null===s.inputTransforms&&(s.inputTransforms={}),os(s.inputTransforms,o.inputTransforms)),Nt(o)&&o.data.animation){const d=e.data;d.animation=(d.animation||[]).concat(o.data.animation)}}const r=o.features;if(r)for(let s=0;s<r.length;s++){const a=r[s];a&&a.ngInherit&&a(e),a===W&&(n=!1)}}t=Object.getPrototypeOf(t)}!function uA(e){let t=0,n=null;for(let i=e.length-1;i>=0;i--){const o=e[i];o.hostVars=t+=o.hostVars,o.hostAttrs=ko(o.hostAttrs,n=ko(n,o.hostAttrs))}}(i)}function ca(e){return e===jt?{}:e===z?[]:e}function fA(e,t){const n=e.viewQuery;e.viewQuery=n?(i,o)=>{t(i,o),n(i,o)}:t}function hA(e,t){const n=e.contentQueries;e.contentQueries=n?(i,o,r)=>{t(i,o,r),n(i,o,r)}:t}function mA(e,t){const n=e.hostBindings;e.hostBindings=n?(i,o)=>{t(i,o),n(i,o)}:t}function Kg(e){const t=e.inputConfig,n={};for(const i in t)if(t.hasOwnProperty(i)){const o=t[i];Array.isArray(o)&&o[2]&&(n[i]=o[2])}e.inputTransforms=n}function xe(e,t,n){return!Object.is(e[t],n)&&(e[t]=n,!0)}function yt(e,t,n,i){const o=b();return xe(o,qt(),t)&&($(),Yt(se(),o,e,t,n,i)),yt}function ua(e,t){return e<<17|t<<2}function An(e){return e>>17&32767}function Hd(e){return 2|e}function ni(e){return(131068&e)>>2}function Ud(e,t){return-131069&e|t<<2}function $d(e){return 1|e}function sb(e,t,n,i){const o=e[n+1],r=null===t;let s=i?An(o):ni(o),a=!1;for(;0!==s&&(!1===a||r);){const c=e[s+1];EA(e[s],t)&&(a=!0,e[s+1]=i?$d(c):Hd(c)),s=i?An(c):ni(c)}a&&(e[n+1]=i?Hd(o):$d(o))}function EA(e,t){return null===e||null==t||(Array.isArray(e)?e[1]:e)===t||!(!Array.isArray(e)||"string"!=typeof t)&&ki(e,t)>=0}const Te={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function ab(e){return e.substring(Te.key,Te.keyEnd)}function lb(e,t){const n=Te.textEnd;return n===t?-1:(t=Te.keyEnd=function TA(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}(e,Te.key=t,n),io(e,t,n))}function io(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function Ft(e,t,n){const i=b();return xe(i,qt(),t)&&vt($(),se(),i,e,t,i[L],n,!1),Ft}function zd(e,t,n,i,o){const s=o?"class":"style";Od(e,n,t.inputs[s],s,i)}function st(e,t){return function Ot(e,t,n,i){const o=b(),r=$(),s=an(2);r.firstUpdatePass&&gb(r,e,s,i),t!==V&&xe(o,s,t)&&_b(r,r.data[He()],o,o[L],e,o[s+1]=function BA(e,t){return null==e||""===e||("string"==typeof t?e+=t:"object"==typeof e&&(e=Ee(In(e)))),e}(t,n),i,s)}(e,t,null,!0),st}function mb(e){!function kt(e,t,n,i){const o=$(),r=an(2);o.firstUpdatePass&&gb(o,null,r,i);const s=b();if(n!==V&&xe(s,r,n)){const a=o.data[He()];if(yb(a,i)&&!pb(o,r)){let l=i?a.classesWithoutHost:a.stylesWithoutHost;null!==l&&(n=Vl(l,n||"")),zd(o,a,s,n,i)}else!function jA(e,t,n,i,o,r,s,a){o===V&&(o=z);let l=0,c=0,d=0<o.length?o[0]:null,u=0<r.length?r[0]:null;for(;null!==d||null!==u;){const f=l<o.length?o[l+1]:void 0,h=c<r.length?r[c+1]:void 0;let p,m=null;d===u?(l+=2,c+=2,f!==h&&(m=u,p=h)):null===u||null!==d&&d<u?(l+=2,m=d):(c+=2,m=u,p=h),null!==m&&_b(e,t,n,i,m,p,s,a),d=l<o.length?o[l]:null,u=c<r.length?r[c]:null}}(o,a,s,s[L],s[r+1],s[r+1]=function LA(e,t,n){if(null==n||""===n)return z;const i=[],o=In(n);if(Array.isArray(o))for(let r=0;r<o.length;r++)e(i,o[r],!0);else if("object"==typeof o)for(const r in o)o.hasOwnProperty(r)&&e(i,r,o[r]);else"string"==typeof o&&t(i,o);return i}(e,t,n),i,r)}}(VA,Xt,e,!0)}function Xt(e,t){for(let n=function IA(e){return function db(e){Te.key=0,Te.keyEnd=0,Te.value=0,Te.valueEnd=0,Te.textEnd=e.length}(e),lb(e,io(e,0,Te.textEnd))}(t);n>=0;n=lb(t,n))gt(e,ab(t),!0)}function pb(e,t){return t>=e.expandoStartIndex}function gb(e,t,n,i){const o=e.data;if(null===o[n+1]){const r=o[He()],s=pb(e,n);yb(r,i)&&null===t&&!s&&(t=!1),t=function OA(e,t,n,i){const o=function fc(e){const t=F.lFrame.currentDirectiveIndex;return-1===t?null:e[t]}(e);let r=i?t.residualClasses:t.residualStyles;if(null===o)0===(i?t.classBindings:t.styleBindings)&&(n=hr(n=Gd(null,e,t,n,i),t.attrs,i),r=null);else{const s=t.directiveStylingLast;if(-1===s||e[s]!==o)if(n=Gd(o,e,t,n,i),null===r){let l=function kA(e,t,n){const i=n?t.classBindings:t.styleBindings;if(0!==ni(i))return e[An(i)]}(e,t,i);void 0!==l&&Array.isArray(l)&&(l=Gd(null,e,t,l[1],i),l=hr(l,t.attrs,i),function RA(e,t,n,i){e[An(n?t.classBindings:t.styleBindings)]=i}(e,t,i,l))}else r=function PA(e,t,n){let i;const o=t.directiveEnd;for(let r=1+t.directiveStylingLast;r<o;r++)i=hr(i,e[r].hostAttrs,n);return hr(i,t.attrs,n)}(e,t,i)}return void 0!==r&&(i?t.residualClasses=r:t.residualStyles=r),n}(o,r,t,i),function wA(e,t,n,i,o,r){let s=r?t.classBindings:t.styleBindings,a=An(s),l=ni(s);e[i]=n;let d,c=!1;if(Array.isArray(n)?(d=n[1],(null===d||ki(n,d)>0)&&(c=!0)):d=n,o)if(0!==l){const f=An(e[a+1]);e[i+1]=ua(f,a),0!==f&&(e[f+1]=Ud(e[f+1],i)),e[a+1]=function xA(e,t){return 131071&e|t<<17}(e[a+1],i)}else e[i+1]=ua(a,0),0!==a&&(e[a+1]=Ud(e[a+1],i)),a=i;else e[i+1]=ua(l,0),0===a?a=i:e[l+1]=Ud(e[l+1],i),l=i;c&&(e[i+1]=Hd(e[i+1])),sb(e,d,i,!0),sb(e,d,i,!1),function CA(e,t,n,i,o){const r=o?e.residualClasses:e.residualStyles;null!=r&&"string"==typeof t&&ki(r,t)>=0&&(n[i+1]=$d(n[i+1]))}(t,d,e,i,r),s=ua(a,l),r?t.classBindings=s:t.styleBindings=s}(o,r,t,n,s,i)}}function Gd(e,t,n,i,o){let r=null;const s=n.directiveEnd;let a=n.directiveStylingLast;for(-1===a?a=n.directiveStart:a++;a<s&&(r=t[a],i=hr(i,r.hostAttrs,o),r!==e);)a++;return null!==e&&(n.directiveStylingLast=a),i}function hr(e,t,n){const i=n?1:2;let o=-1;if(null!==t)for(let r=0;r<t.length;r++){const s=t[r];"number"==typeof s?o=s:o===i&&(Array.isArray(e)||(e=void 0===e?[]:["",e]),gt(e,s,!!n||t[++r]))}return void 0===e?null:e}function VA(e,t,n){const i=String(t);""!==i&&!i.includes(" ")&&gt(e,i,n)}function _b(e,t,n,i,o,r,s,a){if(!(3&t.type))return;const l=e.data,c=l[a+1],d=function DA(e){return 1==(1&e)}(c)?vb(l,t,n,o,ni(c),s):void 0;fa(d)||(fa(r)||function yA(e){return 2==(2&e)}(c)&&(r=vb(l,null,n,o,a,s)),function WE(e,t,n,i,o){if(t)o?e.addClass(n,i):e.removeClass(n,i);else{let r=-1===i.indexOf("-")?void 0:Mn.DashCase;null==o?e.removeStyle(n,i,r):("string"==typeof o&&o.endsWith("!important")&&(o=o.slice(0,-10),r|=Mn.Important),e.setStyle(n,i,o,r))}}(i,s,jo(He(),n),o,r))}function vb(e,t,n,i,o,r){const s=null===t;let a;for(;o>0;){const l=e[o],c=Array.isArray(l),d=c?l[1]:l,u=null===d;let f=n[o+1];f===V&&(f=u?z:void 0);let h=u?Mc(f,i):d===i?f:void 0;if(c&&!fa(h)&&(h=Mc(l,i)),fa(h)&&(a=h,s))return a;const m=e[o+1];o=s?An(m):ni(m)}if(null!==t){let l=r?t.residualClasses:t.residualStyles;null!=l&&(a=Mc(l,i))}return a}function fa(e){return void 0!==e}function yb(e,t){return 0!=(e.flags&(t?8:16))}let Mb=()=>null;function oo(e,t){return Mb(e,t)}function _r(e,t,n,i){const o=t.tView,a=ra(e,o,n,4096&e[M]?4096:16,null,t,null,null,null,i?.injector??null,i?.dehydratedView??null);a[Po]=e[t.index];const c=e[Ht];return null!==c&&(a[Ht]=c.createEmbeddedView(o)),Bd(o,a,n),a}function ro(e,t){return!t||null===t.firstChild||js(e)}function vr(e,t,n,i=!0){const o=t[x];if(function VE(e,t,n,i){const o=pe+i,r=n.length;i>0&&(n[o-1][Tt]=t),i<r-pe?(t[Tt]=n[o],Rm(n,pe+i,t)):(n.push(t),t[Tt]=null),t[me]=n;const s=t[Po];null!==s&&n!==s&&function jE(e,t){const n=e[Di];t[ce]!==t[me][me][ce]&&(e[M]|=Jl.HasTransplantedViews),null===n?e[Di]=[t]:n.push(t)}(s,t);const a=t[Ht];null!==a&&a.insertView(e),ac(t),t[M]|=128}(o,t,e,n),i){const s=nd(n,e),a=t[L],l=zs(a,e[Ut]);null!==l&&function PE(e,t,n,i,o,r){i[ue]=o,i[Ve]=t,qs(e,i,n,1,o,r)}(o,e[Ve],a,t,l,s)}const r=t[St];null!==r&&null!==r.firstChild&&(r.firstChild=null)}let Jt=(()=>{class e{static#e=this.__NG_ELEMENT_ID__=yT}return e})();function yT(){return Ob(Q(),b())}const xT=Jt,Nb=class extends xT{constructor(t,n,i){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=i}get element(){return Wi(this._hostTNode,this._hostLView)}get injector(){return new Ie(this._hostTNode,this._hostLView)}get parentInjector(){const t=ws(this._hostTNode,this._hostLView);if(_c(t)){const n=Go(t,this._hostLView),i=zo(t);return new Ie(n[x].data[i+8],n)}return new Ie(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){const n=Fb(this._lContainer);return null!==n&&n[t]||null}get length(){return this._lContainer.length-pe}createEmbeddedView(t,n,i){let o,r;"number"==typeof i?o=i:null!=i&&(o=i.index,r=i.injector);const s=oo(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},r,s);return this.insertImpl(a,o,ro(this._hostTNode,s)),a}createComponent(t,n,i,o,r){const s=t&&!function Wo(e){return"function"==typeof e}(t);let a;if(s)a=n;else{const m=n||{};a=m.index,i=m.injector,o=m.projectableNodes,r=m.environmentInjector||m.ngModuleRef}const l=s?t:new ur(B(t)),c=i||this.parentInjector;if(!r&&null==l.ngModule){const p=(s?c:this.parentInjector).get(ln,null);p&&(r=p)}const d=B(l.componentType??{}),u=oo(this._lContainer,d?.id??null),h=l.create(c,o,u?.firstChild??null,r);return this.insertImpl(h.hostView,a,ro(this._hostTNode,u)),h}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,i){const o=t._lView;if(function Ww(e){return Be(e[me])}(o)){const a=this.indexOf(t);if(-1!==a)this.detach(a);else{const l=o[me],c=new Nb(l,l[Ve],l[me]);c.detach(c.indexOf(t))}}const r=this._adjustIndex(n),s=this._lContainer;return vr(s,o,r,i),t.attachToViewContainerRef(),Rm(eu(s),r,t),t}move(t,n){return this.insert(t,n)}indexOf(t){const n=Fb(this._lContainer);return null!==n?n.indexOf(t):-1}remove(t){const n=this._adjustIndex(t,-1),i=Xo(this._lContainer,n);i&&(Es(eu(this._lContainer),n),$s(i[x],i))}detach(t){const n=this._adjustIndex(t,-1),i=Xo(this._lContainer,n);return i&&null!=Es(eu(this._lContainer),n)?new lr(i):null}_adjustIndex(t,n=0){return t??this.length+n}};function Fb(e){return e[8]}function eu(e){return e[8]||(e[8]=[])}function Ob(e,t){let n;const i=t[e.index];return Be(i)?n=i:(n=Ig(i,t,null,e),t[e.index]=n,sa(t,n)),kb(n,t,e,i),new Nb(n,e,t)}let kb=function Pb(e,t,n,i){if(e[Ut])return;let o;o=8&n.type?ne(i):function DT(e,t){const n=e[L],i=n.createComment(""),o=We(t,e);return Qn(n,zs(n,o),i,function $E(e,t){return e.nextSibling(t)}(n,o),!1),i}(t,n),e[Ut]=o},tu=()=>!1;function Fe(e,t,n,i,o,r,s,a){const l=b(),c=$(),d=e+P,u=c.firstCreatePass?function MT(e,t,n,i,o,r,s,a,l){const c=t.consts,d=qi(t,e,4,s||null,Gt(c,a));Td(t,n,d,Gt(c,l)),_s(t,d);const u=d.tView=Ad(2,d,i,o,r,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return null!==t.queries&&(t.queries.template(t,d),u.queries=t.queries.embeddedTView(d)),d}(d,c,l,t,n,i,o,r,s):c.data[d];Wt(u,!1);const f=Lb(c,l,u,e);bs()&&Gs(c,l,f,u),$e(f,l);const h=Ig(f,l,f,u);return l[d]=h,sa(l,h),function Rb(e,t,n){return tu(e,t,n)}(h,u,l),ps(u)&&Md(c,l,u),null!=s&&Id(l,u,a),Fe}let Lb=function Vb(e,t,n,i){return Dn(!0),t[L].createComment("")};function at(e,t,n){Jn("NgControlFlow");const i=b(),o=qt(),r=function nu(e,t){return e[t]}(i,P+e);if(xe(i,o,t)){const a=de(null);try{if(function Jd(e,t){const n=Xo(e,t);return void 0!==n&&$s(n[x],n),n}(r,0),-1!==t){const l=function iu(e,t){return Bo(e,t)}(i[x],P+t),c=oo(r,l.tView.ssrId);vr(r,_r(i,l,n,{dehydratedView:c}),0,ro(l,c))}}finally{de(a)}}else{const a=function Sb(e,t){const n=pe+t;if(n<e.length)return e[n]}(r,0);void 0!==a&&(a[te]=n)}}function ae(e,t,n,i){const o=b(),r=$(),s=P+e,a=o[L],l=r.firstCreatePass?function mS(e,t,n,i,o,r){const s=t.consts,l=qi(t,e,2,i,Gt(s,o));return Td(t,n,l,Gt(s,r)),null!==l.attrs&&la(l,l.attrs,!1),null!==l.mergedAttrs&&la(l,l.mergedAttrs,!0),null!==t.queries&&t.queries.elementStart(t,l),l}(s,r,o,t,n,i):r.data[s],c=a_(r,o,l,a,t,e);o[s]=c;const d=ps(l);return Wt(l,!0),Fp(a,c,l),32!=(32&l.flags)&&bs()&&Gs(r,o,c,l),0===function Zw(){return F.lFrame.elementDepthCount}()&&$e(c,o),function Yw(){F.lFrame.elementDepthCount++}(),d&&(Md(r,o,l),Ed(r,l,o)),null!==i&&Id(o,l),ae}function le(){let e=Q();cc()?dc():(e=e.parent,Wt(e,!1));const t=e;(function Qw(e){return F.skipHydrationRootTNode===e})(t)&&function tC(){F.skipHydrationRootTNode=null}(),function Kw(){F.lFrame.elementDepthCount--}();const n=$();return n.firstCreatePass&&(_s(n,e),ec(e)&&n.queries.elementEnd(e)),null!=t.classesWithoutHost&&function _C(e){return 0!=(8&e.flags)}(t)&&zd(n,t,b(),t.classesWithoutHost,!0),null!=t.stylesWithoutHost&&function vC(e){return 0!=(16&e.flags)}(t)&&zd(n,t,b(),t.stylesWithoutHost,!1),le}function Rt(e,t,n,i){return ae(e,t,n,i),le(),Rt}let a_=(e,t,n,i,o,r)=>(Dn(!0),Us(i,o,function ym(){return F.lFrame.currentNamespace}()));function Da(e,t,n){const i=b();return xe(i,qt(),t)&&vt($(),se(),i,e,t,i[L],n,!0),Da}const mo="en-US";let h_=mo;function Ia(e){return!!e&&"function"==typeof e.then}function V_(e){return!!e&&"function"==typeof e.subscribe}function ct(e,t,n,i){const o=b(),r=$(),s=Q();return function B_(e,t,n,i,o,r,s){const a=ps(i),c=e.firstCreatePass&&Sg(e),d=t[te],u=Tg(t);let f=!0;if(3&i.type||s){const p=We(i,t),v=s?s(p):p,y=u.length,g=s?k=>s(ne(k[i.index])):i.index;let E=null;if(!s&&a&&(E=function _1(e,t,n,i){const o=e.cleanup;if(null!=o)for(let r=0;r<o.length-1;r+=2){const s=o[r];if(s===n&&o[r+1]===i){const a=t[vi],l=o[r+2];return a.length>l?a[l]:null}"string"==typeof s&&(r+=2)}return null}(e,t,o,i.index)),null!==E)(E.__ngLastListenerFn__||E).__ngNextListenerFn__=r,E.__ngLastListenerFn__=r,f=!1;else{r=U_(i,t,d,r,!1);const k=n.listen(v,o,r);u.push(r,k),c&&c.push(o,g,y,y+1)}}else r=U_(i,t,d,r,!1);const h=i.outputs;let m;if(f&&null!==h&&(m=h[o])){const p=m.length;if(p)for(let v=0;v<p;v+=2){const j=t[m[v]][m[v+1]].subscribe(r),Ce=u.length;u.push(r,j),c&&c.push(o,i.index,Ce,-(Ce+1))}}}(r,o,o[L],s,e,t,i),ct}function H_(e,t,n,i){try{return zt(6,t,n),!1!==n(i)}catch(o){return aa(e,o),!1}finally{zt(7,t,n)}}function U_(e,t,n,i,o){return function r(s){if(s===Function)return i;ar(e.componentOffset>-1?pt(e.index,t):t);let l=H_(t,n,i,s),c=r.__ngNextListenerFn__;for(;c;)l=H_(t,n,c,s)&&l,c=c.__ngNextListenerFn__;return o&&!1===l&&s.preventDefault(),l}}function fn(e=1){return function cC(e){return(F.lFrame.contextLView=function sm(e,t){for(;e>0;)t=t[yi],e--;return t}(e,F.lFrame.contextLView))[te]}(e)}function v1(e,t){let n=null;const i=function Ew(e){const t=e.attrs;if(null!=t){const n=t.indexOf(5);if(!(1&n))return t[n+1]}return null}(e);for(let o=0;o<t.length;o++){const r=t[o];if("*"!==r){if(null===i?Uh(e,r,!0):Aw(i,r))return o}else n=o}return n}function Aa(e){const t=b()[ce][Ve];if(!t.projection){const i=t.projection=function Ms(e,t){const n=[];for(let i=0;i<e;i++)n.push(t);return n}(e?e.length:1,null),o=i.slice();let r=t.child;for(;null!==r;){const s=e?v1(r,e):0;null!==s&&(o[s]?o[s].projectionNext=r:i[s]=r,o[s]=r),r=r.next}}}function dt(e,t=0,n){const i=b(),o=$(),r=qi(o,P+e,16,null,n||null);null===r.projection&&(r.projection=t),dc(),(!i[St]||Ci())&&32!=(32&r.flags)&&function zE(e,t,n){Sp(t[L],0,t,n,ed(e,n,t),Cp(n.parent||t[Ve],n,t))}(o,i,r)}function bu(e){return function wi(e,t){return e[t]}(function oC(){return F.lFrame.contextLView}(),P+e)}function hn(e,t=""){const n=b(),i=$(),o=e+P,r=i.firstCreatePass?qi(i,o,1,t,null):i.data[o],s=av(i,n,r,t,e);n[o]=s,bs()&&Gs(i,n,s,r),Wt(r,!1)}let av=(e,t,n,i,o)=>(Dn(!0),function Hs(e,t){return e.createText(t)}(t[L],i));function Ta(e){return Ar("",e,""),Ta}function Ar(e,t,n){const i=b(),o=function Yi(e,t,n,i){return xe(e,qt(),n)?t+R(n)+i:V}(i,e,t,n);return o!==V&&function un(e,t,n){const i=jo(t,e);!function _p(e,t,n){e.setValue(t,n)}(e[L],i,n)}(i,He(),o),Ar}function _u(e,t,n,i,o){if(e=T(e),Array.isArray(e))for(let r=0;r<e.length;r++)_u(e[r],t,n,i,o);else{const r=$(),s=b(),a=Q();let l=Zn(e)?e:T(e.provide);const c=qm(e),d=1048575&a.providerIndexes,u=a.directiveStart,f=a.providerIndexes>>20;if(Zn(e)||!e.multi){const h=new $o(c,o,_),m=yu(l,t,o?d:d+f,u);-1===m?(xc(Ds(a,s),r,l),vu(r,e,t.length),t.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(h),s.push(h)):(n[m]=h,s[m]=h)}else{const h=yu(l,t,d+f,u),m=yu(l,t,d,d+f),v=m>=0&&n[m];if(o&&!v||!o&&!(h>=0&&n[h])){xc(Ds(a,s),r,l);const y=function R1(e,t,n,i,o){const r=new $o(e,n,_);return r.multi=[],r.index=t,r.componentProviders=0,gv(r,o,i&&!n),r}(o?k1:O1,n.length,o,i,c);!o&&v&&(n[m].providerFactory=y),vu(r,e,t.length,0),t.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(y),s.push(y)}else vu(r,e,h>-1?h:m,gv(n[o?m:h],c,!o&&i));!o&&i&&v&&n[m].componentProviders++}}}function vu(e,t,n,i){const o=Zn(t),r=function KC(e){return!!e.useClass}(t);if(o||r){const l=(r?T(t.useClass):t).prototype.ngOnDestroy;if(l){const c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){const d=c.indexOf(n);-1===d?c.push(n,[i,l]):c[d+1].push(i,l)}else c.push(n,l)}}}function gv(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function yu(e,t,n,i){for(let o=n;o<i;o++)if(t[o]===e)return o;return-1}function O1(e,t,n,i){return xu(this.multi,[])}function k1(e,t,n,i){const o=this.multi;let r;if(this.providerFactory){const s=this.providerFactory.componentProviders,a=qn(n,n[x],this.providerFactory.index,i);r=a.slice(0,s),xu(o,r);for(let l=s;l<a.length;l++)r.push(a[l])}else r=[],xu(o,r);return r}function xu(e,t){for(let n=0;n<e.length;n++)t.push((0,e[n])());return t}function oe(e,t=[]){return n=>{n.providersResolver=(i,o)=>function F1(e,t,n){const i=$();if(i.firstCreatePass){const o=Nt(e);_u(n,i.data,i.blueprint,o,!0),_u(t,i.data,i.blueprint,o,!1)}}(i,o?o(e):e,t)}}class ri{}class bv extends ri{constructor(t){super(),this.componentFactoryResolver=new zg(this),this.instance=null;const n=new Li([...t.providers,{provide:ri,useValue:this},{provide:Gi,useValue:this.componentFactoryResolver}],t.parent||Fs(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}}let B1=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){const i=$m(0,n.type),o=i.length>0?function j1(e,t,n=null){return new bv({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}([i],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(const n of this.cachedInjectors.values())null!==n&&n.destroy()}finally{this.cachedInjectors.clear()}}static#e=this.\u0275prov=O({token:e,providedIn:"environment",factory:()=>new e(C(ln))})}return e})();function Cu(e){Jn("NgStandalone"),e.getStandaloneInjector=t=>t.get(B1).getOrCreateStandaloneInjector(e)}function mN(){return this._results[Symbol.iterator]()}class Mu{static#e=Symbol.iterator;get changes(){return this._changes??=new Ne}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;const n=Mu.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=mN)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;const i=function ot(e){return e.flat(Number.POSITIVE_INFINITY)}(t);(this._changesDetected=!function FC(e,t,n){if(e.length!==t.length)return!1;for(let i=0;i<e.length;i++){let o=e[i],r=t[i];if(n&&(o=n(o),r=n(r)),r!==o)return!1}return!0}(this._results,i,n))&&(this._results=i,this.length=i.length,this.last=i[this.length-1],this.first=i[0])}notifyOnChanges(){void 0!==this._changes&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}setDirty(){this.dirty=!0}destroy(){void 0!==this._changes&&(this._changes.complete(),this._changes.unsubscribe())}}let mn=(()=>{class e{static#e=this.__NG_ELEMENT_ID__=bN}return e})();const pN=mn,gN=class extends pN{constructor(t,n,i){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=i}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,i){const o=_r(this._declarationLView,this._declarationTContainer,t,{injector:n,dehydratedView:i});return new lr(o)}};function bN(){return Fa(Q(),b())}function Fa(e,t){return 4&e.type?new gN(t,e,Wi(e,t)):null}class Iu{constructor(t){this.queryList=t,this.matches=null}clone(){return new Iu(this.queryList)}setDirty(){this.queryList.setDirty()}}class Au{constructor(t=[]){this.queries=t}createEmbeddedView(t){const n=t.queries;if(null!==n){const i=null!==t.contentQueries?t.contentQueries[0]:n.length,o=[];for(let r=0;r<i;r++){const s=n.getByIndex(r);o.push(this.queries[s.indexInDeclarationView].clone())}return new Au(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)null!==kv(t,n).matches&&this.queries[n].setDirty()}}class Sv{constructor(t,n,i=null){this.predicate=t,this.flags=n,this.read=i}}class Tu{constructor(t=[]){this.queries=t}elementStart(t,n){for(let i=0;i<this.queries.length;i++)this.queries[i].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let i=0;i<this.length;i++){const o=null!==n?n.length:0,r=this.getByIndex(i).embeddedTView(t,o);r&&(r.indexInDeclarationView=i,null!==n?n.push(r):n=[r])}return null!==n?new Tu(n):null}template(t,n){for(let i=0;i<this.queries.length;i++)this.queries[i].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}}class Su{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new Su(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&1!=(1&this.metadata.flags)){const n=this._declarationNodeIndex;let i=t.parent;for(;null!==i&&8&i.type&&i.index!==n;)i=i.parent;return n===(null!==i?i.index:-1)}return this._appliesToNextNode}matchTNode(t,n){const i=this.metadata.predicate;if(Array.isArray(i))for(let o=0;o<i.length;o++){const r=i[o];this.matchTNodeWithReadOption(t,n,_N(n,r)),this.matchTNodeWithReadOption(t,n,Cs(n,t,r,!1,!1))}else i===mn?4&n.type&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,Cs(n,t,i,!1,!1))}matchTNodeWithReadOption(t,n,i){if(null!==i){const o=this.metadata.read;if(null!==o)if(o===ye||o===Jt||o===mn&&4&n.type)this.addMatch(n.index,-2);else{const r=Cs(n,t,o,!1,!1);null!==r&&this.addMatch(n.index,r)}else this.addMatch(n.index,i)}}addMatch(t,n){null===this.matches?this.matches=[t,n]:this.matches.push(t,n)}}function _N(e,t){const n=e.localNames;if(null!==n)for(let i=0;i<n.length;i+=2)if(n[i]===t)return n[i+1];return null}function yN(e,t,n,i){return-1===n?function vN(e,t){return 11&e.type?Wi(e,t):4&e.type?Fa(e,t):null}(t,e):-2===n?function xN(e,t,n){return n===ye?Wi(t,e):n===mn?Fa(t,e):n===Jt?Ob(t,e):void 0}(e,t,i):qn(e,e[x],n,t)}function Nv(e,t,n,i){const o=t[Ht].queries[i];if(null===o.matches){const r=e.data,s=n.matches,a=[];for(let l=0;l<s.length;l+=2){const c=s[l];a.push(c<0?null:yN(t,r[c],s[l+1],n.metadata.read))}o.matches=a}return o.matches}function Nu(e,t,n,i){const o=e.queries.getByIndex(n),r=o.matches;if(null!==r){const s=Nv(e,t,o,n);for(let a=0;a<r.length;a+=2){const l=r[a];if(l>0)i.push(s[a/2]);else{const c=r[a+1],d=t[-l];for(let u=pe;u<d.length;u++){const f=d[u];f[Po]===f[me]&&Nu(f[x],f,c,i)}if(null!==d[Di]){const u=d[Di];for(let f=0;f<u.length;f++){const h=u[f];Nu(h[x],h,c,i)}}}}}return i}function Ye(e){const t=b(),n=$(),i=mm();hc(i+1);const o=kv(n,i);if(e.dirty&&function Gw(e){return 4==(4&e[M])}(t)===(2==(2&o.metadata.flags))){if(null===o.matches)e.reset([]);else{const r=o.crossesNgTemplate?Nu(n,t,i,[]):Nv(n,t,o,i);e.reset(r,FM),e.notifyOnChanges()}return!0}return!1}function Sn(e,t,n){const i=$();i.firstCreatePass&&(Ov(i,new Sv(e,t,n),-1),2==(2&t)&&(i.staticViewQueries=!0)),Fv(i,b(),t)}function Nn(e,t,n,i){const o=$();if(o.firstCreatePass){const r=Q();Ov(o,new Sv(t,n,i),r.index),function wN(e,t){const n=e.contentQueries||(e.contentQueries=[]);t!==(n.length?n[n.length-1]:-1)&&n.push(e.queries.length-1,t)}(o,e),2==(2&n)&&(o.staticContentQueries=!0)}Fv(o,b(),n)}function Ke(){return function DN(e,t){return e[Ht].queries[t].queryList}(b(),mm())}function Fv(e,t,n){const i=new Mu(4==(4&n));(function lI(e,t,n,i){const o=Tg(t);o.push(n),e.firstCreatePass&&Sg(e).push(i,o.length-1)})(e,t,i,i.destroy),null===t[Ht]&&(t[Ht]=new Au),t[Ht].queries.push(new Iu(i))}function Ov(e,t,n){null===e.queries&&(e.queries=new Tu),e.queries.track(new Su(t,n))}function kv(e,t){return e.queries.getByIndex(t)}function Rv(e,t){return Fa(e,t)}let Pu=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new Eh(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);const n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),0===this.pendingTasks.size&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();const ty=new w(""),Ra=new w("");let Bu,Vu=(()=>{class e{constructor(n,i,o){this._ngZone=n,this.registry=i,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this.taskTrackingZone=null,Bu||(function vF(e){Bu=e}(o),o.addToWindow(i)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._didWork=!0,this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{H.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;0!==this._callbacks.length;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb(this._didWork)}this._didWork=!1});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(i=>!i.updateCb||!i.updateCb(n)||(clearTimeout(i.timeoutId),!1)),this._didWork=!0}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,i,o){let r=-1;i&&i>0&&(r=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==r),n(this._didWork,this.getPendingTasks())},i)),this._callbacks.push({doneCb:n,timeoutId:r,updateCb:o})}whenStable(n,i,o){if(o&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,i,o),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,i,o){return[]}static#e=this.\u0275fac=function(i){return new(i||e)(C(H),C(ju),C(Ra))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac})}return e})(),ju=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,i){this._applications.set(n,i)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,i=!0){return Bu?.findTestabilityInTree(this,n,i)??null}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();const yF=new w("Application Initializer");let Hu=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,i)=>{this.resolve=n,this.reject=i}),this.appInits=N(yF,{optional:!0})??[]}runInitializers(){if(this.initialized)return;const n=[];for(const o of this.appInits){const r=o();if(Ia(r))n.push(r);else if(V_(r)){const s=new Promise((a,l)=>{r.subscribe({complete:a,error:l})});n.push(s)}}const i=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{i()}).catch(o=>{this.reject(o)}),0===n.length&&i(),this.initialized=!0}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();const ny=new w("appBootstrapListener");let ai=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=N(ug),this.afterRenderEffectManager=N(dr),this.componentTypes=[],this.components=[],this.isStable=N(Pu).hasPendingTasks.pipe(Bn(n=>!n)),this._injector=N(ln)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,i){const o=n instanceof Yp;if(!this._injector.get(Hu).done)throw!o&&function Hn(e){const t=B(e)||Me(e)||ke(e);return null!==t&&t.standalone}(n),new D(405,!1);let s;s=o?n:this._injector.get(Gi).resolveComponentFactory(n),this.componentTypes.push(s.componentType);const a=function DF(e){return e.isBoundToModule}(s)?void 0:this._injector.get(ri),c=s.create(bt.NULL,[],i||s.selector,a),d=c.location.nativeElement,u=c.injector.get(ty,null);return u?.registerApplication(d),c.onDestroy(()=>{this.detachView(c.hostView),Pa(this.components,c),u?.unregisterApplication(d)}),this._loadComponent(c),c}tick(){if(this._runningTick)throw new D(101,!1);try{this._runningTick=!0;for(let n of this._views)n.detectChanges()}catch(n){this.internalErrorHandler(n)}finally{try{this.afterRenderEffectManager.execute()}catch(n){this.internalErrorHandler(n)}this._runningTick=!1}}attachView(n){const i=n;this._views.push(i),i.attachToAppRef(this)}detachView(n){const i=n;Pa(this._views,i),i.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);const i=this._injector.get(ny,[]);[...this._bootstrapListeners,...i].forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Pa(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new D(406,!1);const n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Pa(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}let CF=(()=>{class e{constructor(){this.zone=N(H),this.applicationRef=N(ai)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ay(e){return[{provide:H,useFactory:e},{provide:Pi,multi:!0,useFactory:()=>{const t=N(CF,{optional:!0});return()=>t.initialize()}},{provide:Pi,multi:!0,useFactory:()=>{const t=N(IF);return()=>{t.initialize()}}},{provide:ug,useFactory:EF}]}function EF(){const e=N(H),t=N(dn);return n=>e.runOutsideAngular(()=>t.handleError(n))}function MF(e){return function Fc(e){return{\u0275providers:e}}([[],ay(()=>new H(function ly(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}(e)))])}let IF=(()=>{class e{constructor(){this.subscription=new ft,this.initialized=!1,this.zone=N(H),this.pendingTasks=N(Pu)}initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{H.assertNotInAngularZone(),queueMicrotask(()=>{null!==n&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{H.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();const en=new w("LocaleId",{providedIn:"root",factory:()=>N(en,U.Optional|U.SkipSelf)||function AF(){return typeof $localize<"u"&&$localize.locale||mo}()}),Uu=new w("PlatformDestroyListeners");let Fn=null;let VF=(()=>{class e{constructor(n){}static#e=this.\u0275fac=function(i){return new(i||e)(C(ai))};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({})}return e})();function XF(e){try{const{rootComponent:t,appProviders:n,platformProviders:i}=e,o=function OF(e=[]){if(Fn)return Fn;const t=function uy(e=[],t){return bt.create({name:t,providers:[{provide:Rc,useValue:"platform"},{provide:Uu,useValue:new Set([()=>Fn=null])},...e]})}(e);return Fn=t,function iy(){!function B0(e){mh=e}(()=>{throw new D(600,!1)})}(),function fy(e){e.get(ep,null)?.forEach(n=>n())}(t),t}(i),r=[MF(),...n||[]],a=new bv({providers:r,parent:o,debugName:"",runEnvironmentInitializers:!1}).injector,l=a.get(H);return l.run(()=>{a.resolveInjectorInitializers();const c=a.get(dn,null);let d;l.runOutsideAngular(()=>{d=l.onError.subscribe({next:h=>{c.handleError(h)}})});const u=()=>a.destroy(),f=o.get(Uu);return f.add(u),a.onDestroy(()=>{d.unsubscribe(),f.delete(u)}),function oy(e,t,n){try{const i=n();return Ia(i)?i.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):i}catch(i){throw t.runOutsideAngular(()=>e.handleError(i)),i}}(c,l,()=>{const h=a.get(Hu);return h.runInitializers(),h.donePromise.then(()=>{!function m_(e){tt(e,"Expected localeId to be defined"),"string"==typeof e&&(h_=e.toLowerCase().replace(/_/g,"-"))}(a.get(en,mo)||mo);const p=a.get(ai);return void 0!==t&&p.bootstrap(t),p})})})}catch(t){return Promise.reject(t)}}function Rr(e){return"boolean"==typeof e?e:null!=e&&"false"!==e}let Xu=null;function Pr(){return Xu}class hO{}const ut=new w("DocumentToken");let Zy=(()=>{class e{constructor(n){this._viewContainerRef=n,this._viewRef=null,this.ngTemplateOutletContext=null,this.ngTemplateOutlet=null,this.ngTemplateOutletInjector=null}ngOnChanges(n){if(this._shouldRecreateView(n)){const i=this._viewContainerRef;if(this._viewRef&&i.remove(i.indexOf(this._viewRef)),!this.ngTemplateOutlet)return void(this._viewRef=null);const o=this._createContextForwardProxy();this._viewRef=i.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,i,o)=>!!this.ngTemplateOutletContext&&Reflect.set(this.ngTemplateOutletContext,i,o),get:(n,i,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,i,o)}})}static#e=this.\u0275fac=function(i){return new(i||e)(_(Jt))};static#t=this.\u0275dir=I({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},standalone:!0,features:[$t]})}return e})(),Ky=(()=>{class e{static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({})}return e})();const Qy="browser";function Xy(e){return"server"===e}class aR extends hO{constructor(){super(...arguments),this.supportsDOMEvents=!0}}class gf extends aR{static makeCurrent(){!function fO(e){Xu||(Xu=e)}(new gf)}onAndCancel(t,n,i){return t.addEventListener(n,i),()=>{t.removeEventListener(n,i)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.parentNode&&t.parentNode.removeChild(t)}createElement(t,n){return(n=n||this.getDefaultDocument()).createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return"window"===n?window:"document"===n?t:"body"===n?t.body:null}getBaseHref(t){const n=function lR(){return Br=Br||document.querySelector("base"),Br?Br.getAttribute("href"):null}();return null==n?null:function cR(e){return new URL(e,document.baseURI).pathname}(n)}resetBaseElement(){Br=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return function JO(e,t){t=encodeURIComponent(t);for(const n of e.split(";")){const i=n.indexOf("="),[o,r]=-1==i?[n,""]:[n.slice(0,i),n.slice(i+1)];if(o.trim()===t)return decodeURIComponent(r)}return null}(document.cookie,t)}}let Br=null,uR=(()=>{class e{build(){return new XMLHttpRequest}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac})}return e})();const bf=new w("EventManagerPlugins");let nx=(()=>{class e{constructor(n,i){this._zone=i,this._eventNameToPlugin=new Map,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,i,o){return this._findPluginFor(i).addEventListener(n,i,o)}getZone(){return this._zone}_findPluginFor(n){let i=this._eventNameToPlugin.get(n);if(i)return i;if(i=this._plugins.find(r=>r.supports(n)),!i)throw new D(5101,!1);return this._eventNameToPlugin.set(n,i),i}static#e=this.\u0275fac=function(i){return new(i||e)(C(bf),C(H))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac})}return e})();class ix{constructor(t){this._doc=t}}const _f="ng-app-id";let ox=(()=>{class e{constructor(n,i,o,r={}){this.doc=n,this.appId=i,this.nonce=o,this.platformId=r,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=Xy(r),this.resetHostNodes()}addStyles(n){for(const i of n)1===this.changeUsageCount(i,1)&&this.onStyleAdded(i)}removeStyles(n){for(const i of n)this.changeUsageCount(i,-1)<=0&&this.onStyleRemoved(i)}ngOnDestroy(){const n=this.styleNodesInDOM;n&&(n.forEach(i=>i.remove()),n.clear());for(const i of this.getAllStyles())this.onStyleRemoved(i);this.resetHostNodes()}addHost(n){this.hostNodes.add(n);for(const i of this.getAllStyles())this.addStyleToHost(n,i)}removeHost(n){this.hostNodes.delete(n)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(n){for(const i of this.hostNodes)this.addStyleToHost(i,n)}onStyleRemoved(n){const i=this.styleRef;i.get(n)?.elements?.forEach(o=>o.remove()),i.delete(n)}collectServerRenderedStyles(){const n=this.doc.head?.querySelectorAll(`style[${_f}="${this.appId}"]`);if(n?.length){const i=new Map;return n.forEach(o=>{null!=o.textContent&&i.set(o.textContent,o)}),i}return null}changeUsageCount(n,i){const o=this.styleRef;if(o.has(n)){const r=o.get(n);return r.usage+=i,r.usage}return o.set(n,{usage:i,elements:[]}),i}getStyleElement(n,i){const o=this.styleNodesInDOM,r=o?.get(i);if(r?.parentNode===n)return o.delete(i),r.removeAttribute(_f),r;{const s=this.doc.createElement("style");return this.nonce&&s.setAttribute("nonce",this.nonce),s.textContent=i,this.platformIsServer&&s.setAttribute(_f,this.appId),n.appendChild(s),s}}addStyleToHost(n,i){const o=this.getStyleElement(n,i),r=this.styleRef,s=r.get(i)?.elements;s?s.push(o):r.set(i,{elements:[o],usage:1})}resetHostNodes(){const n=this.hostNodes;n.clear(),n.add(this.doc.head)}static#e=this.\u0275fac=function(i){return new(i||e)(C(ut),C(ks),C(Uc,8),C(Kn))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac})}return e})();const vf={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},yf=/%COMP%/g,pR=new w("RemoveStylesOnCompDestroy",{providedIn:"root",factory:()=>!0});function sx(e,t){return t.map(n=>n.replace(yf,e))}let ax=(()=>{class e{constructor(n,i,o,r,s,a,l,c=null){this.eventManager=n,this.sharedStylesHost=i,this.appId=o,this.removeStylesOnCompDestroy=r,this.doc=s,this.platformId=a,this.ngZone=l,this.nonce=c,this.rendererByCompId=new Map,this.platformIsServer=Xy(a),this.defaultRenderer=new xf(n,s,l,this.platformIsServer)}createRenderer(n,i){if(!n||!i)return this.defaultRenderer;this.platformIsServer&&i.encapsulation===It.ShadowDom&&(i={...i,encapsulation:It.Emulated});const o=this.getOrCreateRenderer(n,i);return o instanceof cx?o.applyToHost(n):o instanceof Df&&o.applyStyles(),o}getOrCreateRenderer(n,i){const o=this.rendererByCompId;let r=o.get(i.id);if(!r){const s=this.doc,a=this.ngZone,l=this.eventManager,c=this.sharedStylesHost,d=this.removeStylesOnCompDestroy,u=this.platformIsServer;switch(i.encapsulation){case It.Emulated:r=new cx(l,c,i,this.appId,d,s,a,u);break;case It.ShadowDom:return new vR(l,c,n,i,s,a,this.nonce,u);default:r=new Df(l,c,i,d,s,a,u)}o.set(i.id,r)}return r}ngOnDestroy(){this.rendererByCompId.clear()}static#e=this.\u0275fac=function(i){return new(i||e)(C(nx),C(ox),C(ks),C(pR),C(ut),C(Kn),C(H),C(Uc))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac})}return e})();class xf{constructor(t,n,i,o){this.eventManager=t,this.doc=n,this.ngZone=i,this.platformIsServer=o,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(t,n){return n?this.doc.createElementNS(vf[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(lx(t)?t.content:t).appendChild(n)}insertBefore(t,n,i){t&&(lx(t)?t.content:t).insertBefore(n,i)}removeChild(t,n){t&&t.removeChild(n)}selectRootElement(t,n){let i="string"==typeof t?this.doc.querySelector(t):t;if(!i)throw new D(-5104,!1);return n||(i.textContent=""),i}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,i,o){if(o){n=o+":"+n;const r=vf[o];r?t.setAttributeNS(r,n,i):t.setAttribute(n,i)}else t.setAttribute(n,i)}removeAttribute(t,n,i){if(i){const o=vf[i];o?t.removeAttributeNS(o,n):t.removeAttribute(`${i}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,i,o){o&(Mn.DashCase|Mn.Important)?t.style.setProperty(n,i,o&Mn.Important?"important":""):t.style[n]=i}removeStyle(t,n,i){i&Mn.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,i){null!=t&&(t[n]=i)}setValue(t,n){t.nodeValue=n}listen(t,n,i){if("string"==typeof t&&!(t=Pr().getGlobalEventTarget(this.doc,t)))throw new Error(`Unsupported event target ${t} for event ${n}`);return this.eventManager.addEventListener(t,n,this.decoratePreventDefault(i))}decoratePreventDefault(t){return n=>{if("__ngUnwrap__"===n)return t;!1===(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))&&n.preventDefault()}}}function lx(e){return"TEMPLATE"===e.tagName&&void 0!==e.content}class vR extends xf{constructor(t,n,i,o,r,s,a,l){super(t,r,s,l),this.sharedStylesHost=n,this.hostEl=i,this.shadowRoot=i.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);const c=sx(o.id,o.styles);for(const d of c){const u=document.createElement("style");a&&u.setAttribute("nonce",a),u.textContent=d,this.shadowRoot.appendChild(u)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,i){return super.insertBefore(this.nodeOrShadowRoot(t),n,i)}removeChild(t,n){return super.removeChild(this.nodeOrShadowRoot(t),n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}}class Df extends xf{constructor(t,n,i,o,r,s,a,l){super(t,r,s,a),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o,this.styles=l?sx(l,i.styles):i.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}}class cx extends Df{constructor(t,n,i,o,r,s,a,l){const c=o+"-"+i.id;super(t,n,i,r,s,a,l,c),this.contentAttr=function gR(e){return"_ngcontent-%COMP%".replace(yf,e)}(c),this.hostAttr=function bR(e){return"_nghost-%COMP%".replace(yf,e)}(c)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){const i=super.createElement(t,n);return super.setAttribute(i,this.contentAttr,""),i}}let yR=(()=>{class e extends ix{constructor(n){super(n)}supports(n){return!0}addEventListener(n,i,o){return n.addEventListener(i,o,!1),()=>this.removeEventListener(n,i,o)}removeEventListener(n,i,o){return n.removeEventListener(i,o)}static#e=this.\u0275fac=function(i){return new(i||e)(C(ut))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac})}return e})();const dx=["alt","control","meta","shift"],xR={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},DR={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey};let wR=(()=>{class e extends ix{constructor(n){super(n)}supports(n){return null!=e.parseEventName(n)}addEventListener(n,i,o){const r=e.parseEventName(i),s=e.eventCallback(r.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Pr().onAndCancel(n,r.domEventName,s))}static parseEventName(n){const i=n.toLowerCase().split("."),o=i.shift();if(0===i.length||"keydown"!==o&&"keyup"!==o)return null;const r=e._normalizeKey(i.pop());let s="",a=i.indexOf("code");if(a>-1&&(i.splice(a,1),s="code."),dx.forEach(c=>{const d=i.indexOf(c);d>-1&&(i.splice(d,1),s+=c+".")}),s+=r,0!=i.length||0===r.length)return null;const l={};return l.domEventName=o,l.fullKey=s,l}static matchEventFullKeyCode(n,i){let o=xR[n.key]||n.key,r="";return i.indexOf("code.")>-1&&(o=n.code,r="code."),!(null==o||!o)&&(o=o.toLowerCase()," "===o?o="space":"."===o&&(o="dot"),dx.forEach(s=>{s!==o&&(0,DR[s])(n)&&(r+=s+".")}),r+=o,r===i)}static eventCallback(n,i,o){return r=>{e.matchEventFullKeyCode(r,n)&&o.runGuarded(()=>i(r))}}static _normalizeKey(n){return"esc"===n?"escape":n}static#e=this.\u0275fac=function(i){return new(i||e)(C(ut))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac})}return e})();const AR=[{provide:Kn,useValue:Qy},{provide:ep,useValue:function ER(){gf.makeCurrent()},multi:!0},{provide:ut,useFactory:function IR(){return function dE(e){Hc=e}(document),document},deps:[]}],TR=new w(""),fx=[{provide:Ra,useClass:class dR{addToWindow(t){ee.getAngularTestability=(i,o=!0)=>{const r=t.findTestabilityInTree(i,o);if(null==r)throw new D(5103,!1);return r},ee.getAllAngularTestabilities=()=>t.getAllTestabilities(),ee.getAllAngularRootElements=()=>t.getAllRootElements(),ee.frameworkStabilizers||(ee.frameworkStabilizers=[]),ee.frameworkStabilizers.push(i=>{const o=ee.getAllAngularTestabilities();let r=o.length,s=!1;const a=function(l){s=s||l,r--,0==r&&i(s)};o.forEach(l=>{l.whenStable(a)})})}findTestabilityInTree(t,n,i){return null==n?null:t.getTestability(n)??(i?Pr().isShadowRoot(n)?this.findTestabilityInTree(t,n.host,!0):this.findTestabilityInTree(t,n.parentElement,!0):null)}},deps:[]},{provide:ty,useClass:Vu,deps:[H,ju,Ra]},{provide:Vu,useClass:Vu,deps:[H,ju,Ra]}],hx=[{provide:Rc,useValue:"root"},{provide:dn,useFactory:function MR(){return new dn},deps:[]},{provide:bf,useClass:yR,multi:!0,deps:[ut,H,Kn]},{provide:bf,useClass:wR,multi:!0,deps:[ut]},ax,ox,nx,{provide:Qp,useExisting:ax},{provide:class Vk{},useClass:uR,deps:[]},[]];let SR=(()=>{class e{constructor(n){}static withServerTransition(n){return{ngModule:e,providers:[{provide:ks,useValue:n.appId}]}}static#e=this.\u0275fac=function(i){return new(i||e)(C(TR,12))};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({providers:[...hx,...fx],imports:[Ky,VF]})}return e})();function kn(e){return this instanceof kn?(this.v=e,this):new kn(e)}function yx(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,t=e[Symbol.asyncIterator];return t?t.call(e):(e=function If(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],i=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),n={},i("next"),i("throw"),i("return"),n[Symbol.asyncIterator]=function(){return this},n);function i(r){n[r]=e[r]&&function(s){return new Promise(function(a,l){!function o(r,s,a,l){Promise.resolve(l).then(function(c){r({value:c,done:a})},s)}(a,l,(s=e[r](s)).done,s.value)})}}}typeof window<"u"&&window,"function"==typeof SuppressedError&&SuppressedError;const xx=e=>e&&"number"==typeof e.length&&"function"!=typeof e;function Dx(e){return ve(e?.then)}function wx(e){return ve(e[Ll])}function Cx(e){return Symbol.asyncIterator&&ve(e?.[Symbol.asyncIterator])}function Ex(e){return new TypeError(`You provided ${null!==e&&"object"==typeof e?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}const Mx=function oP(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}();function Ix(e){return ve(e?.[Mx])}function Ax(e){return function vx(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o,i=n.apply(e,t||[]),r=[];return o={},s("next"),s("throw"),s("return"),o[Symbol.asyncIterator]=function(){return this},o;function s(f){i[f]&&(o[f]=function(h){return new Promise(function(m,p){r.push([f,h,m,p])>1||a(f,h)})})}function a(f,h){try{!function l(f){f.value instanceof kn?Promise.resolve(f.value.v).then(c,d):u(r[0][2],f)}(i[f](h))}catch(m){u(r[0][3],m)}}function c(f){a("next",f)}function d(f){a("throw",f)}function u(f,h){f(h),r.shift(),r.length&&a(r[0][0],r[0][1])}}(this,arguments,function*(){const n=e.getReader();try{for(;;){const{value:i,done:o}=yield kn(n.read());if(o)return yield kn(void 0);yield yield kn(i)}}finally{n.releaseLock()}})}function Tx(e){return ve(e?.getReader)}function Vt(e){if(e instanceof Re)return e;if(null!=e){if(wx(e))return function rP(e){return new Re(t=>{const n=e[Ll]();if(ve(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}(e);if(xx(e))return function sP(e){return new Re(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}(e);if(Dx(e))return function aP(e){return new Re(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,xh)})}(e);if(Cx(e))return Sx(e);if(Ix(e))return function lP(e){return new Re(t=>{for(const n of e)if(t.next(n),t.closed)return;t.complete()})}(e);if(Tx(e))return function cP(e){return Sx(Ax(e))}(e)}throw Ex(e)}function Sx(e){return new Re(t=>{(function dP(e,t){var n,i,o,r;return function bx(e,t,n,i){return new(n||(n=Promise))(function(r,s){function a(d){try{c(i.next(d))}catch(u){s(u)}}function l(d){try{c(i.throw(d))}catch(u){s(u)}}function c(d){d.done?r(d.value):function o(r){return r instanceof n?r:new n(function(s){s(r)})}(d.value).then(a,l)}c((i=i.apply(e,t||[])).next())})}(this,void 0,void 0,function*(){try{for(n=yx(e);!(i=yield n.next()).done;)if(t.next(i.value),t.closed)return}catch(s){o={error:s}}finally{try{i&&!i.done&&(r=n.return)&&(yield r.call(n))}finally{if(o)throw o.error}}t.complete()})})(e,t).catch(n=>t.error(n))})}function bn(e,t,n,i=0,o=!1){const r=t.schedule(function(){n(),o?e.add(this.schedule(null,i)):this.unsubscribe()},i);if(e.add(r),!o)return r}function Nx(e,t=0){return Dt((n,i)=>{n.subscribe(ht(i,o=>bn(i,e,()=>i.next(o),t),()=>bn(i,e,()=>i.complete(),t),o=>bn(i,e,()=>i.error(o),t)))})}function Fx(e,t=0){return Dt((n,i)=>{i.add(e.schedule(()=>n.subscribe(i),t))})}function Ox(e,t){if(!e)throw new Error("Iterable cannot be null");return new Re(n=>{bn(n,t,()=>{const i=e[Symbol.asyncIterator]();bn(n,t,()=>{i.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function vo(e,t){return t?function gP(e,t){if(null!=e){if(wx(e))return function uP(e,t){return Vt(e).pipe(Fx(t),Nx(t))}(e,t);if(xx(e))return function hP(e,t){return new Re(n=>{let i=0;return t.schedule(function(){i===e.length?n.complete():(n.next(e[i++]),n.closed||this.schedule())})})}(e,t);if(Dx(e))return function fP(e,t){return Vt(e).pipe(Fx(t),Nx(t))}(e,t);if(Cx(e))return Ox(e,t);if(Ix(e))return function mP(e,t){return new Re(n=>{let i;return bn(n,t,()=>{i=e[Mx](),bn(n,t,()=>{let o,r;try{({value:o,done:r}=i.next())}catch(s){return void n.error(s)}r?n.complete():n.next(o)},0,!0)}),()=>ve(i?.return)&&i.return()})}(e,t);if(Tx(e))return function pP(e,t){return Ox(Ax(e),t)}(e,t)}throw Ex(e)}(e,t):Vt(e)}const{isArray:bP}=Array,{getPrototypeOf:_P,prototype:vP,keys:yP}=Object;function kx(e){if(1===e.length){const t=e[0];if(bP(t))return{args:t,keys:null};if(function xP(e){return e&&"object"==typeof e&&_P(e)===vP}(t)){const n=yP(t);return{args:n.map(i=>t[i]),keys:n}}}return{args:e,keys:null}}function Af(e){return e[e.length-1]}function Rx(e){return ve(Af(e))?e.pop():void 0}function Hr(e){return function DP(e){return e&&ve(e.schedule)}(Af(e))?e.pop():void 0}const{isArray:CP}=Array;function Px(e){return Bn(t=>function EP(e,t){return CP(t)?e(...t):e(t)}(e,t))}function Lx(e,t){return e.reduce((n,i,o)=>(n[i]=t[o],n),{})}let Vx=(()=>{class e{constructor(n,i){this._renderer=n,this._elementRef=i,this.onChange=o=>{},this.onTouched=()=>{}}setProperty(n,i){this._renderer.setProperty(this._elementRef.nativeElement,n,i)}registerOnTouched(n){this.onTouched=n}registerOnChange(n){this.onChange=n}setDisabledState(n){this.setProperty("disabled",n)}static#e=this.\u0275fac=function(i){return new(i||e)(_(Xn),_(ye))};static#t=this.\u0275dir=I({type:e})}return e})(),ci=(()=>{class e extends Vx{static#e=this.\u0275fac=(()=>{let n;return function(o){return(n||(n=function Ue(e){return rn(()=>{const t=e.prototype.constructor,n=t[on]||Dc(t),i=Object.prototype;let o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==i;){const r=o[on]||Dc(o);if(r&&r!==n)return r;o=Object.getPrototypeOf(o)}return r=>new r})}(e)))(o||e)}})();static#t=this.\u0275dir=I({type:e,features:[W]})}return e})();const tn=new w("NgValueAccessor"),AP={provide:tn,useExisting:J(()=>il),multi:!0},SP=new w("CompositionEventMode");let il=(()=>{class e extends Vx{constructor(n,i,o){super(n,i),this._compositionMode=o,this._composing=!1,null==this._compositionMode&&(this._compositionMode=!function TP(){const e=Pr()?Pr().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}())}writeValue(n){this.setProperty("value",n??"")}_handleInput(n){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(n)}_compositionStart(){this._composing=!0}_compositionEnd(n){this._composing=!1,this._compositionMode&&this.onChange(n)}static#e=this.\u0275fac=function(i){return new(i||e)(_(Xn),_(ye),_(SP,8))};static#t=this.\u0275dir=I({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(i,o){1&i&&ct("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},features:[oe([AP]),W]})}return e})();function Rn(e){return null==e||("string"==typeof e||Array.isArray(e))&&0===e.length}function Bx(e){return null!=e&&"number"==typeof e.length}const ze=new w("NgValidators"),Pn=new w("NgAsyncValidators"),NP=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;class Hx{static min(t){return function Ux(e){return t=>{if(Rn(t.value)||Rn(e))return null;const n=parseFloat(t.value);return!isNaN(n)&&n<e?{min:{min:e,actual:t.value}}:null}}(t)}static max(t){return function $x(e){return t=>{if(Rn(t.value)||Rn(e))return null;const n=parseFloat(t.value);return!isNaN(n)&&n>e?{max:{max:e,actual:t.value}}:null}}(t)}static required(t){return function zx(e){return Rn(e.value)?{required:!0}:null}(t)}static requiredTrue(t){return function Gx(e){return!0===e.value?null:{required:!0}}(t)}static email(t){return function Wx(e){return Rn(e.value)||NP.test(e.value)?null:{email:!0}}(t)}static minLength(t){return function qx(e){return t=>Rn(t.value)||!Bx(t.value)?null:t.value.length<e?{minlength:{requiredLength:e,actualLength:t.value.length}}:null}(t)}static maxLength(t){return function Zx(e){return t=>Bx(t.value)&&t.value.length>e?{maxlength:{requiredLength:e,actualLength:t.value.length}}:null}(t)}static pattern(t){return function Yx(e){if(!e)return ol;let t,n;return"string"==typeof e?(n="","^"!==e.charAt(0)&&(n+="^"),n+=e,"$"!==e.charAt(e.length-1)&&(n+="$"),t=new RegExp(n)):(n=e.toString(),t=e),i=>{if(Rn(i.value))return null;const o=i.value;return t.test(o)?null:{pattern:{requiredPattern:n,actualValue:o}}}}(t)}static nullValidator(t){return null}static compose(t){return tD(t)}static composeAsync(t){return nD(t)}}function ol(e){return null}function Kx(e){return null!=e}function Qx(e){return Ia(e)?vo(e):e}function Xx(e){let t={};return e.forEach(n=>{t=null!=n?{...t,...n}:t}),0===Object.keys(t).length?null:t}function Jx(e,t){return t.map(n=>n(e))}function eD(e){return e.map(t=>function FP(e){return!e.validate}(t)?t:n=>t.validate(n))}function tD(e){if(!e)return null;const t=e.filter(Kx);return 0==t.length?null:function(n){return Xx(Jx(n,t))}}function Tf(e){return null!=e?tD(eD(e)):null}function nD(e){if(!e)return null;const t=e.filter(Kx);return 0==t.length?null:function(n){return function MP(...e){const t=Rx(e),{args:n,keys:i}=kx(e),o=new Re(r=>{const{length:s}=n;if(!s)return void r.complete();const a=new Array(s);let l=s,c=s;for(let d=0;d<s;d++){let u=!1;Vt(n[d]).subscribe(ht(r,f=>{u||(u=!0,c--),a[d]=f},()=>l--,void 0,()=>{(!l||!u)&&(c||r.next(i?Lx(i,a):a),r.complete())}))}});return t?o.pipe(Px(t)):o}(Jx(n,t).map(Qx)).pipe(Bn(Xx))}}function Sf(e){return null!=e?nD(eD(e)):null}function iD(e,t){return null===e?[t]:Array.isArray(e)?[...e,t]:[e,t]}function oD(e){return e._rawValidators}function rD(e){return e._rawAsyncValidators}function Nf(e){return e?Array.isArray(e)?e:[e]:[]}function rl(e,t){return Array.isArray(e)?e.includes(t):e===t}function sD(e,t){const n=Nf(t);return Nf(e).forEach(o=>{rl(n,o)||n.push(o)}),n}function aD(e,t){return Nf(t).filter(n=>!rl(e,n))}class lD{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=Tf(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=Sf(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return!!this.control&&this.control.hasError(t,n)}getError(t,n){return this.control?this.control.getError(t,n):null}}class Je extends lD{get formDirective(){return null}get path(){return null}}class _n extends lD{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}}class cD{constructor(t){this._cd=t}get isTouched(){return!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return!!this._cd?.submitted}}let dD=(()=>{class e extends cD{constructor(n){super(n)}static#e=this.\u0275fac=function(i){return new(i||e)(_(_n,2))};static#t=this.\u0275dir=I({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(i,o){2&i&&st("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},features:[W]})}return e})();const Ur="VALID",al="INVALID",yo="PENDING",$r="DISABLED";function kf(e){return(ll(e)?e.validators:e)||null}function Rf(e,t){return(ll(t)?t.asyncValidators:e)||null}function ll(e){return null!=e&&!Array.isArray(e)&&"object"==typeof e}class mD{constructor(t,n){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=!1,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this.pristine=!0,this.touched=!1,this._onDisabledChange=[],this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get valid(){return this.status===Ur}get invalid(){return this.status===al}get pending(){return this.status==yo}get disabled(){return this.status===$r}get enabled(){return this.status!==$r}get dirty(){return!this.pristine}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(sD(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(sD(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(aD(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(aD(t,this._rawAsyncValidators))}hasValidator(t){return rl(this._rawValidators,t)}hasAsyncValidator(t){return rl(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){this.touched=!0,this._parent&&!t.onlySelf&&this._parent.markAsTouched(t)}markAllAsTouched(){this.markAsTouched({onlySelf:!0}),this._forEachChild(t=>t.markAllAsTouched())}markAsUntouched(t={}){this.touched=!1,this._pendingTouched=!1,this._forEachChild(n=>{n.markAsUntouched({onlySelf:!0})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t)}markAsDirty(t={}){this.pristine=!1,this._parent&&!t.onlySelf&&this._parent.markAsDirty(t)}markAsPristine(t={}){this.pristine=!0,this._pendingDirty=!1,this._forEachChild(n=>{n.markAsPristine({onlySelf:!0})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t)}markAsPending(t={}){this.status=yo,!1!==t.emitEvent&&this.statusChanges.emit(this.status),this._parent&&!t.onlySelf&&this._parent.markAsPending(t)}disable(t={}){const n=this._parentMarkedDirty(t.onlySelf);this.status=$r,this.errors=null,this._forEachChild(i=>{i.disable({...t,onlySelf:!0})}),this._updateValue(),!1!==t.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors({...t,skipPristineCheck:n}),this._onDisabledChange.forEach(i=>i(!0))}enable(t={}){const n=this._parentMarkedDirty(t.onlySelf);this.status=Ur,this._forEachChild(i=>{i.enable({...t,onlySelf:!0})}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors({...t,skipPristineCheck:n}),this._onDisabledChange.forEach(i=>i(!1))}_updateAncestors(t){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine(),this._parent._updateTouched())}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){this._setInitialStatus(),this._updateValue(),this.enabled&&(this._cancelExistingSubscription(),this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Ur||this.status===yo)&&this._runAsyncValidator(t.emitEvent)),!1!==t.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(t)}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?$r:Ur}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t){if(this.asyncValidator){this.status=yo,this._hasOwnPendingAsyncValidator=!0;const n=Qx(this.asyncValidator(this));this._asyncValidationSubscription=n.subscribe(i=>{this._hasOwnPendingAsyncValidator=!1,this.setErrors(i,{emitEvent:t})})}}_cancelExistingSubscription(){this._asyncValidationSubscription&&(this._asyncValidationSubscription.unsubscribe(),this._hasOwnPendingAsyncValidator=!1)}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(!1!==n.emitEvent)}get(t){let n=t;return null==n||(Array.isArray(n)||(n=n.split(".")),0===n.length)?null:n.reduce((i,o)=>i&&i._find(o),this)}getError(t,n){const i=n?this.get(n):this;return i&&i.errors?i.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),this._parent&&this._parent._updateControlsErrors(t)}_initObservables(){this.valueChanges=new Ne,this.statusChanges=new Ne}_calculateStatus(){return this._allControlsDisabled()?$r:this.errors?al:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(yo)?yo:this._anyControlsHaveStatus(al)?al:Ur}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t={}){this.pristine=!this._anyControlsDirty(),this._parent&&!t.onlySelf&&this._parent._updatePristine(t)}_updateTouched(t={}){this.touched=this._anyControlsTouched(),this._parent&&!t.onlySelf&&this._parent._updateTouched(t)}_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){ll(t)&&null!=t.updateOn&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){return!t&&!(!this._parent||!this._parent.dirty)&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=function LP(e){return Array.isArray(e)?Tf(e):e||null}(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=function VP(e){return Array.isArray(e)?Sf(e):e||null}(this._rawAsyncValidators)}}class Pf extends mD{constructor(t,n,i){super(kf(n),Rf(i,n)),this.controls=t,this._initObservables(),this._setUpdateStrategy(n),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(t,n){return this.controls[t]?this.controls[t]:(this.controls[t]=n,n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange),n)}addControl(t,n,i={}){this.registerControl(t,n),this.updateValueAndValidity({emitEvent:i.emitEvent}),this._onCollectionChange()}removeControl(t,n={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}setControl(t,n,i={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],n&&this.registerControl(t,n),this.updateValueAndValidity({emitEvent:i.emitEvent}),this._onCollectionChange()}contains(t){return this.controls.hasOwnProperty(t)&&this.controls[t].enabled}setValue(t,n={}){(function hD(e,t,n){e._forEachChild((i,o)=>{if(void 0===n[o])throw new D(1002,"")})})(this,0,t),Object.keys(t).forEach(i=>{(function fD(e,t,n){const i=e.controls;if(!(t?Object.keys(i):i).length)throw new D(1e3,"");if(!i[n])throw new D(1001,"")})(this,!0,i),this.controls[i].setValue(t[i],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n)}patchValue(t,n={}){null!=t&&(Object.keys(t).forEach(i=>{const o=this.controls[i];o&&o.patchValue(t[i],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n))}reset(t={},n={}){this._forEachChild((i,o)=>{i.reset(t?t[o]:null,{onlySelf:!0,emitEvent:n.emitEvent})}),this._updatePristine(n),this._updateTouched(n),this.updateValueAndValidity(n)}getRawValue(){return this._reduceChildren({},(t,n,i)=>(t[i]=n.getRawValue(),t))}_syncPendingControls(){let t=this._reduceChildren(!1,(n,i)=>!!i._syncPendingControls()||n);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){Object.keys(this.controls).forEach(n=>{const i=this.controls[n];i&&t(i,n)})}_setUpControls(){this._forEachChild(t=>{t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(t){for(const[n,i]of Object.entries(this.controls))if(this.contains(n)&&t(i))return!0;return!1}_reduceValue(){return this._reduceChildren({},(n,i,o)=>((i.enabled||this.disabled)&&(n[o]=i.value),n))}_reduceChildren(t,n){let i=t;return this._forEachChild((o,r)=>{i=n(i,o,r)}),i}_allControlsDisabled(){for(const t of Object.keys(this.controls))if(this.controls[t].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(t){return this.controls.hasOwnProperty(t)?this.controls[t]:null}}const di=new w("CallSetDisabledState",{providedIn:"root",factory:()=>zr}),zr="always";function Gr(e,t,n=zr){Lf(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||"always"===n)&&t.valueAccessor.setDisabledState?.(e.disabled),function HP(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,"change"===e.updateOn&&pD(e,t)})}(e,t),function $P(e,t){const n=(i,o)=>{t.valueAccessor.writeValue(i),o&&t.viewToModelUpdate(i)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}(e,t),function UP(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,"blur"===e.updateOn&&e._pendingChange&&pD(e,t),"submit"!==e.updateOn&&e.markAsTouched()})}(e,t),function BP(e,t){if(t.valueAccessor.setDisabledState){const n=i=>{t.valueAccessor.setDisabledState(i)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}(e,t)}function dl(e,t,n=!0){const i=()=>{};t.valueAccessor&&(t.valueAccessor.registerOnChange(i),t.valueAccessor.registerOnTouched(i)),fl(e,t),e&&(t._invokeOnDestroyCallbacks(),e._registerOnCollectionChange(()=>{}))}function ul(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function Lf(e,t){const n=oD(e);null!==t.validator?e.setValidators(iD(n,t.validator)):"function"==typeof n&&e.setValidators([n]);const i=rD(e);null!==t.asyncValidator?e.setAsyncValidators(iD(i,t.asyncValidator)):"function"==typeof i&&e.setAsyncValidators([i]);const o=()=>e.updateValueAndValidity();ul(t._rawValidators,o),ul(t._rawAsyncValidators,o)}function fl(e,t){let n=!1;if(null!==e){if(null!==t.validator){const o=oD(e);if(Array.isArray(o)&&o.length>0){const r=o.filter(s=>s!==t.validator);r.length!==o.length&&(n=!0,e.setValidators(r))}}if(null!==t.asyncValidator){const o=rD(e);if(Array.isArray(o)&&o.length>0){const r=o.filter(s=>s!==t.asyncValidator);r.length!==o.length&&(n=!0,e.setAsyncValidators(r))}}}const i=()=>{};return ul(t._rawValidators,i),ul(t._rawAsyncValidators,i),n}function pD(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function gD(e,t){Lf(e,t)}function bD(e,t){e._syncPendingControls(),t.forEach(n=>{const i=n.control;"submit"===i.updateOn&&i._pendingChange&&(n.viewToModelUpdate(i._pendingValue),i._pendingChange=!1)})}const ZP={provide:Je,useExisting:J(()=>hl)},Wr=(()=>Promise.resolve())();let hl=(()=>{class e extends Je{constructor(n,i,o){super(),this.callSetDisabledState=o,this.submitted=!1,this._directives=new Set,this.ngSubmit=new Ne,this.form=new Pf({},Tf(n),Sf(i))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(n){Wr.then(()=>{const i=this._findContainer(n.path);n.control=i.registerControl(n.name,n.control),Gr(n.control,n,this.callSetDisabledState),n.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(n)})}getControl(n){return this.form.get(n.path)}removeControl(n){Wr.then(()=>{const i=this._findContainer(n.path);i&&i.removeControl(n.name),this._directives.delete(n)})}addFormGroup(n){Wr.then(()=>{const i=this._findContainer(n.path),o=new Pf({});gD(o,n),i.registerControl(n.name,o),o.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(n){Wr.then(()=>{const i=this._findContainer(n.path);i&&i.removeControl(n.name)})}getFormGroup(n){return this.form.get(n.path)}updateModel(n,i){Wr.then(()=>{this.form.get(n.path).setValue(i)})}setValue(n){this.control.setValue(n)}onSubmit(n){return this.submitted=!0,bD(this.form,this._directives),this.ngSubmit.emit(n),"dialog"===n?.target?.method}onReset(){this.resetForm()}resetForm(n=void 0){this.form.reset(n),this.submitted=!1}_setUpdateStrategy(){this.options&&null!=this.options.updateOn&&(this.form._updateOn=this.options.updateOn)}_findContainer(n){return n.pop(),n.length?this.form.get(n):this.form}static#e=this.\u0275fac=function(i){return new(i||e)(_(ze,10),_(Pn,10),_(di,8))};static#t=this.\u0275dir=I({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(i,o){1&i&&ct("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{options:["ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[oe([ZP]),W]})}return e})();function _D(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}function vD(e){return"object"==typeof e&&null!==e&&2===Object.keys(e).length&&"value"in e&&"disabled"in e}const Hf=class extends mD{constructor(t=null,n,i){super(kf(n),Rf(i,n)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(t),this._setUpdateStrategy(n),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),ll(n)&&(n.nonNullable||n.initialValueIsDefault)&&(this.defaultValue=vD(t)?t.value:t)}setValue(t,n={}){this.value=this._pendingValue=t,this._onChange.length&&!1!==n.emitModelToViewChange&&this._onChange.forEach(i=>i(this.value,!1!==n.emitViewToModelChange)),this.updateValueAndValidity(n)}patchValue(t,n={}){this.setValue(t,n)}reset(t=this.defaultValue,n={}){this._applyFormState(t),this.markAsPristine(n),this.markAsUntouched(n),this.setValue(this.value,n),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){_D(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){_D(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return!("submit"!==this.updateOn||(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),!this._pendingChange)||(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),0))}_applyFormState(t){vD(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}};let ED=(()=>{class e{static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({})}return e})();const Uf=new w("NgModelWithFormControlWarning"),iL={provide:_n,useExisting:J(()=>$f)};let $f=(()=>{class e extends _n{set isDisabled(n){}static#e=this._ngModelWarningSentOnce=!1;constructor(n,i,o,r,s){super(),this._ngModelWarningConfig=r,this.callSetDisabledState=s,this.update=new Ne,this._ngModelWarningSent=!1,this._setValidators(n),this._setAsyncValidators(i),this.valueAccessor=function Bf(e,t){if(!t)return null;let n,i,o;return Array.isArray(t),t.forEach(r=>{r.constructor===il?n=r:function WP(e){return Object.getPrototypeOf(e.constructor)===ci}(r)?i=r:o=r}),o||i||n||null}(0,o)}ngOnChanges(n){if(this._isControlChanged(n)){const i=n.form.previousValue;i&&dl(i,this,!1),Gr(this.form,this,this.callSetDisabledState),this.form.updateValueAndValidity({emitEvent:!1})}(function jf(e,t){if(!e.hasOwnProperty("model"))return!1;const n=e.model;return!!n.isFirstChange()||!Object.is(t,n.currentValue)})(n,this.viewModel)&&(this.form.setValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.form&&dl(this.form,this,!1)}get path(){return[]}get control(){return this.form}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}_isControlChanged(n){return n.hasOwnProperty("form")}static#t=this.\u0275fac=function(i){return new(i||e)(_(ze,10),_(Pn,10),_(tn,10),_(Uf,8),_(di,8))};static#n=this.\u0275dir=I({type:e,selectors:[["","formControl",""]],inputs:{form:["formControl","form"],isDisabled:["disabled","isDisabled"],model:["ngModel","model"]},outputs:{update:"ngModelChange"},exportAs:["ngForm"],features:[oe([iL]),W,$t]})}return e})();const oL={provide:Je,useExisting:J(()=>ml)};let ml=(()=>{class e extends Je{constructor(n,i,o){super(),this.callSetDisabledState=o,this.submitted=!1,this._onCollectionChange=()=>this._updateDomValue(),this.directives=[],this.form=null,this.ngSubmit=new Ne,this._setValidators(n),this._setAsyncValidators(i)}ngOnChanges(n){this._checkFormPresent(),n.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(fl(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(n){const i=this.form.get(n.path);return Gr(i,n,this.callSetDisabledState),i.updateValueAndValidity({emitEvent:!1}),this.directives.push(n),i}getControl(n){return this.form.get(n.path)}removeControl(n){dl(n.control||null,n,!1),function qP(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}(this.directives,n)}addFormGroup(n){this._setUpFormContainer(n)}removeFormGroup(n){this._cleanUpFormContainer(n)}getFormGroup(n){return this.form.get(n.path)}addFormArray(n){this._setUpFormContainer(n)}removeFormArray(n){this._cleanUpFormContainer(n)}getFormArray(n){return this.form.get(n.path)}updateModel(n,i){this.form.get(n.path).setValue(i)}onSubmit(n){return this.submitted=!0,bD(this.form,this.directives),this.ngSubmit.emit(n),"dialog"===n?.target?.method}onReset(){this.resetForm()}resetForm(n=void 0){this.form.reset(n),this.submitted=!1}_updateDomValue(){this.directives.forEach(n=>{const i=n.control,o=this.form.get(n.path);i!==o&&(dl(i||null,n),(e=>e instanceof Hf)(o)&&(Gr(o,n,this.callSetDisabledState),n.control=o))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(n){const i=this.form.get(n.path);gD(i,n),i.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(n){if(this.form){const i=this.form.get(n.path);i&&function zP(e,t){return fl(e,t)}(i,n)&&i.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){Lf(this.form,this),this._oldForm&&fl(this._oldForm,this)}_checkFormPresent(){}static#e=this.\u0275fac=function(i){return new(i||e)(_(ze,10),_(Pn,10),_(di,8))};static#t=this.\u0275dir=I({type:e,selectors:[["","formGroup",""]],hostBindings:function(i,o){1&i&&ct("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{form:["formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[oe([oL]),W,$t]})}return e})(),HD=(()=>{class e{static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({imports:[ED]})}return e})(),wL=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:di,useValue:n.callSetDisabledState??zr}]}}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({imports:[HD]})}return e})(),CL=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:Uf,useValue:n.warnOnNgModelWithFormControl??"always"},{provide:di,useValue:n.callSetDisabledState??zr}]}}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({imports:[HD]})}return e})();const EL=new w("cdk-dir-doc",{providedIn:"root",factory:function ML(){return N(ut)}}),IL=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;let Yf,TL=(()=>{class e{constructor(n){this.value="ltr",this.change=new Ne,n&&(this.value=function AL(e){const t=e?.toLowerCase()||"";return"auto"===t&&typeof navigator<"u"&&navigator?.language?IL.test(navigator.language)?"rtl":"ltr":"rtl"===t?"rtl":"ltr"}((n.body?n.body.dir:null)||(n.documentElement?n.documentElement.dir:null)||"ltr"))}ngOnDestroy(){this.change.complete()}static#e=this.\u0275fac=function(i){return new(i||e)(C(EL,8))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),UD=(()=>{class e{static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({})}return e})();try{Yf=typeof Intl<"u"&&Intl.v8BreakIterator}catch{Yf=!1}let xo,nn=(()=>{class e{constructor(n){this._platformId=n,this.isBrowser=this._platformId?function Pk(e){return e===Qy}(this._platformId):"object"==typeof document&&!!document,this.EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent),this.TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent),this.BLINK=this.isBrowser&&!(!window.chrome&&!Yf)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT,this.WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT,this.IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window),this.FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent),this.ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT,this.SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT}static#e=this.\u0275fac=function(i){return new(i||e)(C(Kn))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();const $D=["color","button","checkbox","date","datetime-local","email","file","hidden","image","month","number","password","radio","range","reset","search","submit","tel","text","time","url","week"];function zD(){if(xo)return xo;if("object"!=typeof document||!document)return xo=new Set($D),xo;let e=document.createElement("input");return xo=new Set($D.filter(t=>(e.setAttribute("type",t),e.type===t))),xo}let qr,Kf;function Zr(e){return function SL(){if(null==qr&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>qr=!0}))}finally{qr=qr||!1}return qr}()?e:!!e.capture}function Do(e){return e.composedPath?e.composedPath()[0]:e.target}function GD(e,t,n=1/0){return ve(t)?GD((i,o)=>Bn((r,s)=>t(i,r,o,s))(Vt(e(i,o))),n):("number"==typeof t&&(n=t),Dt((i,o)=>function kL(e,t,n,i,o,r,s,a){const l=[];let c=0,d=0,u=!1;const f=()=>{u&&!l.length&&!c&&t.complete()},h=p=>c<i?m(p):l.push(p),m=p=>{r&&t.next(p),c++;let v=!1;Vt(n(p,d++)).subscribe(ht(t,y=>{o?.(y),r?h(y):t.next(y)},()=>{v=!0},void 0,()=>{if(v)try{for(c--;l.length&&c<i;){const y=l.shift();s?bn(t,s,()=>m(y)):m(y)}f()}catch(y){t.error(y)}}))};return e.subscribe(ht(t,h,()=>{u=!0,f()})),()=>{a?.()}}(i,o,e,n)))}function WD(e=1/0){return GD(To,e)}const gl=new Re(e=>e.complete());function qD(...e){const t=Hr(e),n=function wP(e,t){return"number"==typeof Af(e)?e.pop():t}(e,1/0),i=e;return i.length?1===i.length?Vt(i[0]):WD(n)(vo(i,t)):gl}function wo(e){return Dt((t,n)=>{Vt(e).subscribe(ht(n,()=>n.complete(),Fl)),!n.closed&&t.subscribe(n)})}function ZD(e,t){return Dt((n,i)=>{let o=0;n.subscribe(ht(i,r=>e.call(t,r,o++)&&i.next(r)))})}const Qf={now:()=>(Qf.delegate||Date).now(),delegate:void 0};class YD extends et{constructor(t=1/0,n=1/0,i=Qf){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=i,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){const{isStopped:n,_buffer:i,_infiniteTimeWindow:o,_timestampProvider:r,_windowTime:s}=this;n||(i.push(t),!o&&i.push(r.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();const n=this._innerSubscribe(t),{_infiniteTimeWindow:i,_buffer:o}=this,r=o.slice();for(let s=0;s<r.length&&!t.closed;s+=i?1:2)t.next(r[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){const{_bufferSize:t,_timestampProvider:n,_buffer:i,_infiniteTimeWindow:o}=this,r=(o?1:2)*t;if(t<1/0&&r<i.length&&i.splice(0,i.length-r),!o){const s=n.now();let a=0;for(let l=1;l<i.length&&i[l]<=s;l+=2)a=l;a&&i.splice(0,a+1)}}}function Xf(e,t,...n){if(!0===t)return void e();if(!1===t)return;const i=new Ao({next:()=>{i.unsubscribe(),e()}});return Vt(t(...n)).subscribe(i)}class VL{constructor(t){this._box=t,this._destroyed=new et,this._resizeSubject=new et,this._elementObservables=new Map,typeof ResizeObserver<"u"&&(this._resizeObserver=new ResizeObserver(n=>this._resizeSubject.next(n)))}observe(t){return this._elementObservables.has(t)||this._elementObservables.set(t,new Re(n=>{const i=this._resizeSubject.subscribe(n);return this._resizeObserver?.observe(t,{box:this._box}),()=>{this._resizeObserver?.unobserve(t),i.unsubscribe(),this._elementObservables.delete(t)}}).pipe(ZD(n=>n.some(i=>i.target===t)),function LL(e,t,n){let i,o=!1;return e&&"object"==typeof e?({bufferSize:i=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e):i=e??1/0,function PL(e={}){const{connector:t=(()=>new et),resetOnError:n=!0,resetOnComplete:i=!0,resetOnRefCountZero:o=!0}=e;return r=>{let s,a,l,c=0,d=!1,u=!1;const f=()=>{a?.unsubscribe(),a=void 0},h=()=>{f(),s=l=void 0,d=u=!1},m=()=>{const p=s;h(),p?.unsubscribe()};return Dt((p,v)=>{c++,!u&&!d&&f();const y=l=l??t();v.add(()=>{c--,0===c&&!u&&!d&&(a=Xf(m,o))}),y.subscribe(v),!s&&c>0&&(s=new Ao({next:g=>y.next(g),error:g=>{u=!0,f(),a=Xf(h,n,g),y.error(g)},complete:()=>{d=!0,f(),a=Xf(h,i),y.complete()}}),Vt(p).subscribe(s))})(r)}}({connector:()=>new YD(i,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}({bufferSize:1,refCount:!0}),wo(this._destroyed))),this._elementObservables.get(t)}destroy(){this._destroyed.next(),this._destroyed.complete(),this._resizeSubject.complete(),this._elementObservables.clear()}}let jL=(()=>{class e{constructor(){this._observers=new Map,this._ngZone=N(H)}ngOnDestroy(){for(const[,n]of this._observers)n.destroy();this._observers.clear()}observe(n,i){const o=i?.box||"content-box";return this._observers.has(o)||this._observers.set(o,new VL(o)),this._observers.get(o).observe(n)}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function bl(e){return null!=e&&"false"!=`${e}`}function KD(e){return Array.isArray(e)?e:[e]}function hi(e){return e instanceof ye?e.nativeElement:e}function HL(e,t){return{type:7,name:e,definitions:t,options:{}}}function UL(e,t=null){return{type:4,styles:t,timings:e}}function QD(e){return{type:6,styles:e,offset:null}}function $L(e,t,n){return{type:0,name:e,styles:t,options:n}}function zL(e,t,n=null){return{type:1,expr:e,animation:t,options:n}}let ZL=(()=>{class e{create(n){return typeof MutationObserver>"u"?null:new MutationObserver(n)}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),YL=(()=>{class e{static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({providers:[ZL]})}return e})();function XD(e){return ZD((t,n)=>e<=n)}function i2(e,t){return e===t}function JD(e,t,n){e?bn(n,e,t):t()}function Jf(...e){return function s2(){return WD(1)}()(vo(e,Hr(e)))}class l2 extends ft{constructor(t,n){super()}schedule(t,n=0){return this}}const _l={setInterval(e,t,...n){const{delegate:i}=_l;return i?.setInterval?i.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){const{delegate:t}=_l;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};class Yr{constructor(t,n=Yr.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,i){return new this.schedulerActionCtor(this,t).schedule(i,n)}}Yr.now=Qf.now;const u2=new class d2 extends Yr{constructor(t,n=Yr.now){super(t,n),this.actions=[],this._active=!1}flush(t){const{actions:n}=this;if(this._active)return void n.push(t);let i;this._active=!0;do{if(i=t.execute(t.state,t.delay))break}while(t=n.shift());if(this._active=!1,i){for(;t=n.shift();)t.unsubscribe();throw i}}}(class c2 extends l2{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var i;if(this.closed)return this;this.state=t;const o=this.id,r=this.scheduler;return null!=o&&(this.id=this.recycleAsyncId(r,o,n)),this.pending=!0,this.delay=n,this.id=null!==(i=this.id)&&void 0!==i?i:this.requestAsyncId(r,this.id,n),this}requestAsyncId(t,n,i=0){return _l.setInterval(t.flush.bind(t,this),i)}recycleAsyncId(t,n,i=0){if(null!=i&&this.delay===i&&!1===this.pending)return n;null!=n&&_l.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;const i=this._execute(t,n);if(i)return i;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let o,i=!1;try{this.work(t)}catch(r){i=!0,o=r||new Error("Scheduled action threw falsy error")}if(i)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){const{id:t,scheduler:n}=this,{actions:i}=n;this.work=this.state=this.scheduler=null,this.pending=!1,es(i,this),null!=t&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}});function h2(...e){const t=Hr(e);return Dt((n,i)=>{(t?Jf(e,n,t):Jf(e,n)).subscribe(i)})}const e0=new Set;let mi,m2=(()=>{class e{constructor(n,i){this._platform=n,this._nonce=i,this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):g2}matchMedia(n){return(this._platform.WEBKIT||this._platform.BLINK)&&function p2(e,t){if(!e0.has(e))try{mi||(mi=document.createElement("style"),t&&(mi.nonce=t),mi.setAttribute("type","text/css"),document.head.appendChild(mi)),mi.sheet&&(mi.sheet.insertRule(`@media ${e} {body{ }}`,0),e0.add(e))}catch(n){console.error(n)}}(n,this._nonce),this._matchMedia(n)}static#e=this.\u0275fac=function(i){return new(i||e)(C(nn),C(Uc,8))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function g2(e){return{matches:"all"===e||""===e,media:e,addListener:()=>{},removeListener:()=>{}}}let b2=(()=>{class e{constructor(n,i){this._mediaMatcher=n,this._zone=i,this._queries=new Map,this._destroySubject=new et}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(n){return t0(KD(n)).some(o=>this._registerQuery(o).mql.matches)}observe(n){let r=function o2(...e){const t=Hr(e),n=Rx(e),{args:i,keys:o}=kx(e);if(0===i.length)return vo([],t);const r=new Re(function r2(e,t,n=To){return i=>{JD(t,()=>{const{length:o}=e,r=new Array(o);let s=o,a=o;for(let l=0;l<o;l++)JD(t,()=>{const c=vo(e[l],t);let d=!1;c.subscribe(ht(i,u=>{r[l]=u,d||(d=!0,a--),a||i.next(n(r.slice()))},()=>{--s||i.complete()}))},i)},i)}}(i,t,o?s=>Lx(o,s):To));return n?r.pipe(Px(n)):r}(t0(KD(n)).map(s=>this._registerQuery(s).observable));return r=Jf(r.pipe(function a2(e){return e<=0?()=>gl:Dt((t,n)=>{let i=0;t.subscribe(ht(n,o=>{++i<=e&&(n.next(o),e<=i&&n.complete())}))})}(1)),r.pipe(XD(1),function f2(e,t=u2){return Dt((n,i)=>{let o=null,r=null,s=null;const a=()=>{if(o){o.unsubscribe(),o=null;const c=r;r=null,i.next(c)}};function l(){const c=s+e,d=t.now();if(d<c)return o=this.schedule(void 0,c-d),void i.add(o);a()}n.subscribe(ht(i,c=>{r=c,s=t.now(),o||(o=t.schedule(l,e),i.add(o))},()=>{a(),i.complete()},void 0,()=>{r=o=null}))})}(0))),r.pipe(Bn(s=>{const a={matches:!1,breakpoints:{}};return s.forEach(({matches:l,query:c})=>{a.matches=a.matches||l,a.breakpoints[c]=l}),a}))}_registerQuery(n){if(this._queries.has(n))return this._queries.get(n);const i=this._mediaMatcher.matchMedia(n),r={observable:new Re(s=>{const a=l=>this._zone.run(()=>s.next(l));return i.addListener(a),()=>{i.removeListener(a)}}).pipe(h2(i),Bn(({matches:s})=>({query:n,matches:s})),wo(this._destroySubject)),mql:i};return this._queries.set(n,r),r}static#e=this.\u0275fac=function(i){return new(i||e)(C(m2),C(H))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function t0(e){return e.map(t=>t.split(",")).reduce((t,n)=>t.concat(n)).map(t=>t.trim())}function s0(e){return 0===e.buttons||0===e.detail}function a0(e){const t=e.touches&&e.touches[0]||e.changedTouches&&e.changedTouches[0];return!(!t||-1!==t.identifier||null!=t.radiusX&&1!==t.radiusX||null!=t.radiusY&&1!==t.radiusY)}const E2=new w("cdk-input-modality-detector-options"),M2={ignoreKeys:[18,17,224,91,16]},Co=Zr({passive:!0,capture:!0});let I2=(()=>{class e{get mostRecentModality(){return this._modality.value}constructor(n,i,o,r){this._platform=n,this._mostRecentTarget=null,this._modality=new Eh(null),this._lastTouchMs=0,this._onKeydown=s=>{this._options?.ignoreKeys?.some(a=>a===s.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=Do(s))},this._onMousedown=s=>{Date.now()-this._lastTouchMs<650||(this._modality.next(s0(s)?"keyboard":"mouse"),this._mostRecentTarget=Do(s))},this._onTouchstart=s=>{a0(s)?this._modality.next("keyboard"):(this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=Do(s))},this._options={...M2,...r},this.modalityDetected=this._modality.pipe(XD(1)),this.modalityChanged=this.modalityDetected.pipe(function n2(e,t=To){return e=e??i2,Dt((n,i)=>{let o,r=!0;n.subscribe(ht(i,s=>{const a=t(s);(r||!e(o,a))&&(r=!1,o=a,i.next(s))}))})}()),n.isBrowser&&i.runOutsideAngular(()=>{o.addEventListener("keydown",this._onKeydown,Co),o.addEventListener("mousedown",this._onMousedown,Co),o.addEventListener("touchstart",this._onTouchstart,Co)})}ngOnDestroy(){this._modality.complete(),this._platform.isBrowser&&(document.removeEventListener("keydown",this._onKeydown,Co),document.removeEventListener("mousedown",this._onMousedown,Co),document.removeEventListener("touchstart",this._onTouchstart,Co))}static#e=this.\u0275fac=function(i){return new(i||e)(C(nn),C(H),C(ut),C(E2,8))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();const T2=new w("cdk-focus-monitor-default-options"),vl=Zr({passive:!0,capture:!0});let S2=(()=>{class e{constructor(n,i,o,r,s){this._ngZone=n,this._platform=i,this._inputModalityDetector=o,this._origin=null,this._windowFocused=!1,this._originFromTouchInteraction=!1,this._elementInfo=new Map,this._monitoredElementCount=0,this._rootNodeFocusListenerCount=new Map,this._windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=window.setTimeout(()=>this._windowFocused=!1)},this._stopInputModalityDetector=new et,this._rootNodeFocusAndBlurListener=a=>{for(let c=Do(a);c;c=c.parentElement)"focus"===a.type?this._onFocus(a,c):this._onBlur(a,c)},this._document=r,this._detectionMode=s?.detectionMode||0}monitor(n,i=!1){const o=hi(n);if(!this._platform.isBrowser||1!==o.nodeType)return function KL(...e){return vo(e,Hr(e))}();const r=function FL(e){if(function NL(){if(null==Kf){const e=typeof document<"u"?document.head:null;Kf=!(!e||!e.createShadowRoot&&!e.attachShadow)}return Kf}()){const t=e.getRootNode?e.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&t instanceof ShadowRoot)return t}return null}(o)||this._getDocument(),s=this._elementInfo.get(o);if(s)return i&&(s.checkChildren=!0),s.subject;const a={checkChildren:i,subject:new et,rootNode:r};return this._elementInfo.set(o,a),this._registerGlobalListeners(a),a.subject}stopMonitoring(n){const i=hi(n),o=this._elementInfo.get(i);o&&(o.subject.complete(),this._setClasses(i),this._elementInfo.delete(i),this._removeGlobalListeners(o))}focusVia(n,i,o){const r=hi(n);r===this._getDocument().activeElement?this._getClosestElementsInfo(r).forEach(([a,l])=>this._originChanged(a,i,l)):(this._setOrigin(i),"function"==typeof r.focus&&r.focus(o))}ngOnDestroy(){this._elementInfo.forEach((n,i)=>this.stopMonitoring(i))}_getDocument(){return this._document||document}_getWindow(){return this._getDocument().defaultView||window}_getFocusOrigin(n){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(n)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:n&&this._isLastInteractionFromInputLabel(n)?"mouse":"program"}_shouldBeAttributedToTouch(n){return 1===this._detectionMode||!!n?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(n,i){n.classList.toggle("cdk-focused",!!i),n.classList.toggle("cdk-touch-focused","touch"===i),n.classList.toggle("cdk-keyboard-focused","keyboard"===i),n.classList.toggle("cdk-mouse-focused","mouse"===i),n.classList.toggle("cdk-program-focused","program"===i)}_setOrigin(n,i=!1){this._ngZone.runOutsideAngular(()=>{this._origin=n,this._originFromTouchInteraction="touch"===n&&i,0===this._detectionMode&&(clearTimeout(this._originTimeoutId),this._originTimeoutId=setTimeout(()=>this._origin=null,this._originFromTouchInteraction?650:1))})}_onFocus(n,i){const o=this._elementInfo.get(i),r=Do(n);!o||!o.checkChildren&&i!==r||this._originChanged(i,this._getFocusOrigin(r),o)}_onBlur(n,i){const o=this._elementInfo.get(i);!o||o.checkChildren&&n.relatedTarget instanceof Node&&i.contains(n.relatedTarget)||(this._setClasses(i),this._emitOrigin(o,null))}_emitOrigin(n,i){n.subject.observers.length&&this._ngZone.run(()=>n.subject.next(i))}_registerGlobalListeners(n){if(!this._platform.isBrowser)return;const i=n.rootNode,o=this._rootNodeFocusListenerCount.get(i)||0;o||this._ngZone.runOutsideAngular(()=>{i.addEventListener("focus",this._rootNodeFocusAndBlurListener,vl),i.addEventListener("blur",this._rootNodeFocusAndBlurListener,vl)}),this._rootNodeFocusListenerCount.set(i,o+1),1==++this._monitoredElementCount&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe(wo(this._stopInputModalityDetector)).subscribe(r=>{this._setOrigin(r,!0)}))}_removeGlobalListeners(n){const i=n.rootNode;if(this._rootNodeFocusListenerCount.has(i)){const o=this._rootNodeFocusListenerCount.get(i);o>1?this._rootNodeFocusListenerCount.set(i,o-1):(i.removeEventListener("focus",this._rootNodeFocusAndBlurListener,vl),i.removeEventListener("blur",this._rootNodeFocusAndBlurListener,vl),this._rootNodeFocusListenerCount.delete(i))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(n,i,o){this._setClasses(n,i),this._emitOrigin(o,i),this._lastFocusOrigin=i}_getClosestElementsInfo(n){const i=[];return this._elementInfo.forEach((o,r)=>{(r===n||o.checkChildren&&r.contains(n))&&i.push([r,o])}),i}_isLastInteractionFromInputLabel(n){const{_mostRecentTarget:i,mostRecentModality:o}=this._inputModalityDetector;if("mouse"!==o||!i||i===n||"INPUT"!==n.nodeName&&"TEXTAREA"!==n.nodeName||n.disabled)return!1;const r=n.labels;if(r)for(let s=0;s<r.length;s++)if(r[s].contains(i))return!0;return!1}static#e=this.\u0275fac=function(i){return new(i||e)(C(H),C(nn),C(I2),C(ut,8),C(T2,8))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();const c0="cdk-high-contrast-black-on-white",d0="cdk-high-contrast-white-on-black",eh="cdk-high-contrast-active";let N2=(()=>{class e{constructor(n,i){this._platform=n,this._document=i,this._breakpointSubscription=N(b2).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return 0;const n=this._document.createElement("div");n.style.backgroundColor="rgb(1,2,3)",n.style.position="absolute",this._document.body.appendChild(n);const i=this._document.defaultView||window,o=i&&i.getComputedStyle?i.getComputedStyle(n):null,r=(o&&o.backgroundColor||"").replace(/ /g,"");switch(n.remove(),r){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return 2;case"rgb(255,255,255)":case"rgb(255,250,239)":return 1}return 0}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){const n=this._document.body.classList;n.remove(eh,c0,d0),this._hasCheckedHighContrastMode=!0;const i=this.getHighContrastMode();1===i?n.add(eh,c0):2===i&&n.add(eh,d0)}}static#e=this.\u0275fac=function(i){return new(i||e)(C(nn),C(ut))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();const O2=new w("mat-sanity-checks",{providedIn:"root",factory:function F2(){return!0}});let Ln=(()=>{class e{constructor(n,i,o){this._sanityChecks=i,this._document=o,this._hasDoneGlobalChecks=!1,n._applyBodyHighContrastModeCssClasses(),this._hasDoneGlobalChecks||(this._hasDoneGlobalChecks=!0)}_checkIsEnabled(n){return!function OL(){return typeof __karma__<"u"&&!!__karma__||typeof jasmine<"u"&&!!jasmine||typeof jest<"u"&&!!jest||typeof Mocha<"u"&&!!Mocha}()&&("boolean"==typeof this._sanityChecks?this._sanityChecks:!!this._sanityChecks[n])}static#e=this.\u0275fac=function(i){return new(i||e)(C(N2),C(O2,8),C(ut))};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({imports:[UD,UD]})}return e})();function k2(e){return class extends e{updateErrorState(){const t=this.errorState,r=(this.errorStateMatcher||this._defaultErrorStateMatcher).isErrorState(this.ngControl?this.ngControl.control:null,this._parentFormGroup||this._parentForm);r!==t&&(this.errorState=r,this.stateChanges.next())}constructor(...t){super(...t),this.errorState=!1}}}let P2=(()=>{class e{isErrorState(n,i){return!!(n&&n.invalid&&(n.touched||i&&i.submitted))}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();class L2{constructor(t,n,i,o=!1){this._renderer=t,this.element=n,this.config=i,this._animationForciblyDisabledThroughCss=o,this.state=3}fadeOut(){this._renderer.fadeOutRipple(this)}}const h0=Zr({passive:!0,capture:!0});class V2{constructor(){this._events=new Map,this._delegateEventHandler=t=>{const n=Do(t);n&&this._events.get(t.type)?.forEach((i,o)=>{(o===n||o.contains(n))&&i.forEach(r=>r.handleEvent(t))})}}addHandler(t,n,i,o){const r=this._events.get(n);if(r){const s=r.get(i);s?s.add(o):r.set(i,new Set([o]))}else this._events.set(n,new Map([[i,new Set([o])]])),t.runOutsideAngular(()=>{document.addEventListener(n,this._delegateEventHandler,h0)})}removeHandler(t,n,i){const o=this._events.get(t);if(!o)return;const r=o.get(n);r&&(r.delete(i),0===r.size&&o.delete(n),0===o.size&&(this._events.delete(t),document.removeEventListener(t,this._delegateEventHandler,h0)))}}const m0={enterDuration:225,exitDuration:150},p0=Zr({passive:!0,capture:!0}),g0=["mousedown","touchstart"],b0=["mouseup","mouseleave","touchend","touchcancel"];class yl{static#e=this._eventManager=new V2;constructor(t,n,i,o){this._target=t,this._ngZone=n,this._platform=o,this._isPointerDown=!1,this._activeRipples=new Map,this._pointerUpEventsRegistered=!1,o.isBrowser&&(this._containerElement=hi(i))}fadeInRipple(t,n,i={}){const o=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),r={...m0,...i.animation};i.centered&&(t=o.left+o.width/2,n=o.top+o.height/2);const s=i.radius||function B2(e,t,n){const i=Math.max(Math.abs(e-n.left),Math.abs(e-n.right)),o=Math.max(Math.abs(t-n.top),Math.abs(t-n.bottom));return Math.sqrt(i*i+o*o)}(t,n,o),a=t-o.left,l=n-o.top,c=r.enterDuration,d=document.createElement("div");d.classList.add("mat-ripple-element"),d.style.left=a-s+"px",d.style.top=l-s+"px",d.style.height=2*s+"px",d.style.width=2*s+"px",null!=i.color&&(d.style.backgroundColor=i.color),d.style.transitionDuration=`${c}ms`,this._containerElement.appendChild(d);const u=window.getComputedStyle(d),h=u.transitionDuration,m="none"===u.transitionProperty||"0s"===h||"0s, 0s"===h||0===o.width&&0===o.height,p=new L2(this,d,i,m);d.style.transform="scale3d(1, 1, 1)",p.state=0,i.persistent||(this._mostRecentTransientRipple=p);let v=null;return!m&&(c||r.exitDuration)&&this._ngZone.runOutsideAngular(()=>{const y=()=>this._finishRippleTransition(p),g=()=>this._destroyRipple(p);d.addEventListener("transitionend",y),d.addEventListener("transitioncancel",g),v={onTransitionEnd:y,onTransitionCancel:g}}),this._activeRipples.set(p,v),(m||!c)&&this._finishRippleTransition(p),p}fadeOutRipple(t){if(2===t.state||3===t.state)return;const n=t.element,i={...m0,...t.config.animation};n.style.transitionDuration=`${i.exitDuration}ms`,n.style.opacity="0",t.state=2,(t._animationForciblyDisabledThroughCss||!i.exitDuration)&&this._finishRippleTransition(t)}fadeOutAll(){this._getActiveRipples().forEach(t=>t.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(t=>{t.config.persistent||t.fadeOut()})}setupTriggerEvents(t){const n=hi(t);!this._platform.isBrowser||!n||n===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=n,g0.forEach(i=>{yl._eventManager.addHandler(this._ngZone,i,n,this)}))}handleEvent(t){"mousedown"===t.type?this._onMousedown(t):"touchstart"===t.type?this._onTouchStart(t):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{b0.forEach(n=>{this._triggerElement.addEventListener(n,this,p0)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(t){0===t.state?this._startFadeOutTransition(t):2===t.state&&this._destroyRipple(t)}_startFadeOutTransition(t){const n=t===this._mostRecentTransientRipple,{persistent:i}=t.config;t.state=1,!i&&(!n||!this._isPointerDown)&&t.fadeOut()}_destroyRipple(t){const n=this._activeRipples.get(t)??null;this._activeRipples.delete(t),this._activeRipples.size||(this._containerRect=null),t===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),t.state=3,null!==n&&(t.element.removeEventListener("transitionend",n.onTransitionEnd),t.element.removeEventListener("transitioncancel",n.onTransitionCancel)),t.element.remove()}_onMousedown(t){const n=s0(t),i=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+800;!this._target.rippleDisabled&&!n&&!i&&(this._isPointerDown=!0,this.fadeInRipple(t.clientX,t.clientY,this._target.rippleConfig))}_onTouchStart(t){if(!this._target.rippleDisabled&&!a0(t)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;const n=t.changedTouches;if(n)for(let i=0;i<n.length;i++)this.fadeInRipple(n[i].clientX,n[i].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(t=>{!t.config.persistent&&(1===t.state||t.config.terminateOnPointerUp&&0===t.state)&&t.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){const t=this._triggerElement;t&&(g0.forEach(n=>yl._eventManager.removeHandler(n,t,this)),this._pointerUpEventsRegistered&&b0.forEach(n=>t.removeEventListener(n,this,p0)))}}const _0=new w("mat-ripple-global-options");let H2=(()=>{class e{get disabled(){return this._disabled}set disabled(n){n&&this.fadeOutAllNonPersistent(),this._disabled=n,this._setupTriggerEventsIfEnabled()}get trigger(){return this._trigger||this._elementRef.nativeElement}set trigger(n){this._trigger=n,this._setupTriggerEventsIfEnabled()}constructor(n,i,o,r,s){this._elementRef=n,this._animationMode=s,this.radius=0,this._disabled=!1,this._isInitialized=!1,this._globalOptions=r||{},this._rippleRenderer=new yl(this,i,n,o)}ngOnInit(){this._isInitialized=!0,this._setupTriggerEventsIfEnabled()}ngOnDestroy(){this._rippleRenderer._removeTriggerEvents()}fadeOutAll(){this._rippleRenderer.fadeOutAll()}fadeOutAllNonPersistent(){this._rippleRenderer.fadeOutAllNonPersistent()}get rippleConfig(){return{centered:this.centered,radius:this.radius,color:this.color,animation:{...this._globalOptions.animation,..."NoopAnimations"===this._animationMode?{enterDuration:0,exitDuration:0}:{},...this.animation},terminateOnPointerUp:this._globalOptions.terminateOnPointerUp}}get rippleDisabled(){return this.disabled||!!this._globalOptions.disabled}_setupTriggerEventsIfEnabled(){!this.disabled&&this._isInitialized&&this._rippleRenderer.setupTriggerEvents(this.trigger)}launch(n,i=0,o){return"number"==typeof n?this._rippleRenderer.fadeInRipple(n,i,{...this.rippleConfig,...o}):this._rippleRenderer.fadeInRipple(0,0,{...this.rippleConfig,...n})}static#e=this.\u0275fac=function(i){return new(i||e)(_(ye),_(H),_(nn),_(_0,8),_(Rs,8))};static#t=this.\u0275dir=I({type:e,selectors:[["","mat-ripple",""],["","matRipple",""]],hostAttrs:[1,"mat-ripple"],hostVars:2,hostBindings:function(i,o){2&i&&st("mat-ripple-unbounded",o.unbounded)},inputs:{color:["matRippleColor","color"],unbounded:["matRippleUnbounded","unbounded"],centered:["matRippleCentered","centered"],radius:["matRippleRadius","radius"],animation:["matRippleAnimation","animation"],disabled:["matRippleDisabled","disabled"],trigger:["matRippleTrigger","trigger"]},exportAs:["matRipple"]})}return e})(),U2=(()=>{class e{static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({imports:[Ln,Ln]})}return e})();const v0={capture:!0},y0=["focus","click","mouseenter","touchstart"],th="mat-ripple-loader-uninitialized",nh="mat-ripple-loader-class-name",x0="mat-ripple-loader-centered",xl="mat-ripple-loader-disabled";let $2=(()=>{class e{constructor(){this._document=N(ut,{optional:!0}),this._animationMode=N(Rs,{optional:!0}),this._globalRippleOptions=N(_0,{optional:!0}),this._platform=N(nn),this._ngZone=N(H),this._hosts=new Map,this._onInteraction=n=>{if(!(n.target instanceof HTMLElement))return;const o=n.target.closest(`[${th}]`);o&&this._createRipple(o)},this._ngZone.runOutsideAngular(()=>{for(const n of y0)this._document?.addEventListener(n,this._onInteraction,v0)})}ngOnDestroy(){const n=this._hosts.keys();for(const i of n)this.destroyRipple(i);for(const i of y0)this._document?.removeEventListener(i,this._onInteraction,v0)}configureRipple(n,i){n.setAttribute(th,""),(i.className||!n.hasAttribute(nh))&&n.setAttribute(nh,i.className||""),i.centered&&n.setAttribute(x0,""),i.disabled&&n.setAttribute(xl,"")}getRipple(n){return this._hosts.get(n)||this._createRipple(n)}setDisabled(n,i){const o=this._hosts.get(n);o?o.disabled=i:i?n.setAttribute(xl,""):n.removeAttribute(xl)}_createRipple(n){if(!this._document)return;const i=this._hosts.get(n);if(i)return i;n.querySelector(".mat-ripple")?.remove();const o=this._document.createElement("span");o.classList.add("mat-ripple",n.getAttribute(nh)),n.append(o);const r=new H2(new ye(o),this._ngZone,this._platform,this._globalRippleOptions?this._globalRippleOptions:void 0,this._animationMode?this._animationMode:void 0);return r._isInitialized=!0,r.trigger=n,r.centered=n.hasAttribute(x0),r.disabled=n.hasAttribute(xl),this.attachRipple(n,r),r}attachRipple(n,i){n.removeAttribute(th),this._hosts.set(n,i)}destroyRipple(n){const i=this._hosts.get(n);i&&(i.ngOnDestroy(),this._hosts.delete(n))}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();const z2=["notch"],G2=["matFormFieldNotchedOutline",""],W2=["*"],q2=["textField"],Z2=["iconPrefixContainer"],Y2=["textPrefixContainer"];function K2(e,t){1&e&&Rt(0,"span",16)}function Q2(e,t){if(1&e&&(ae(0,"label",14),dt(1,1),Fe(2,K2,1,0,"span",15),le()),2&e){const n=fn(2);Ft("floating",n._shouldLabelFloat())("monitorResize",n._hasOutline())("id",n._labelId),yt("for",n._control.id),ge(2),at(2,!n.hideRequiredMarker&&n._control.required?2:-1)}}function X2(e,t){1&e&&Fe(0,Q2,3,5,"label",14),2&e&&at(0,fn()._hasFloatingLabel()?0:-1)}function J2(e,t){1&e&&Rt(0,"div",17)}function eV(e,t){}function tV(e,t){1&e&&Fe(0,eV,0,0,"ng-template",9),2&e&&(fn(2),Ft("ngTemplateOutlet",bu(1)))}function nV(e,t){if(1&e&&(ae(0,"div",5),Fe(1,tV,1,1,null,9),le()),2&e){const n=fn();Ft("matFormFieldNotchedOutlineOpen",n._shouldLabelFloat()),ge(),at(1,n._forceDisplayInfixLabel()?-1:1)}}function iV(e,t){1&e&&(ae(0,"div",18,19),dt(2,2),le())}function oV(e,t){1&e&&(ae(0,"div",20,21),dt(2,3),le())}function rV(e,t){}function sV(e,t){1&e&&Fe(0,rV,0,0,"ng-template",9),2&e&&(fn(),Ft("ngTemplateOutlet",bu(1)))}function aV(e,t){1&e&&(ae(0,"div",22),dt(1,4),le())}function lV(e,t){1&e&&(ae(0,"div",23),dt(1,5),le())}function cV(e,t){1&e&&Rt(0,"div",12)}function dV(e,t){1&e&&(ae(0,"div",24),dt(1,6),le()),2&e&&Ft("@transitionMessages",fn()._subscriptAnimationState)}function uV(e,t){if(1&e&&(ae(0,"mat-hint",26),hn(1),le()),2&e){const n=fn(2);Ft("id",n._hintLabelId),ge(),Ta(n.hintLabel)}}function fV(e,t){if(1&e&&(ae(0,"div",25),Fe(1,uV,2,2,"mat-hint",26),dt(2,7),Rt(3,"div",27),dt(4,8),le()),2&e){const n=fn();Ft("@transitionMessages",n._subscriptAnimationState),ge(),at(1,n.hintLabel?1:-1)}}const hV=["*",[["mat-label"]],[["","matPrefix",""],["","matIconPrefix",""]],[["","matTextPrefix",""]],[["","matTextSuffix",""]],[["","matSuffix",""],["","matIconSuffix",""]],[["mat-error"],["","matError",""]],[["mat-hint",3,"align","end"]],[["mat-hint","align","end"]]],mV=["*","mat-label","[matPrefix], [matIconPrefix]","[matTextPrefix]","[matTextSuffix]","[matSuffix], [matIconSuffix]","mat-error, [matError]","mat-hint:not([align='end'])","mat-hint[align='end']"];let ih=(()=>{class e{static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275dir=I({type:e,selectors:[["mat-label"]]})}return e})();const pV=new w("MatError");let gV=0,D0=(()=>{class e{constructor(){this.align="start",this.id="mat-mdc-hint-"+gV++}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275dir=I({type:e,selectors:[["mat-hint"]],hostAttrs:[1,"mat-mdc-form-field-hint","mat-mdc-form-field-bottom-align"],hostVars:4,hostBindings:function(i,o){2&i&&(Da("id",o.id),yt("align",null),st("mat-mdc-form-field-hint-end","end"===o.align))},inputs:{align:"align",id:"id"}})}return e})();const bV=new w("MatPrefix"),_V=new w("MatSuffix"),w0=new w("FloatingLabelParent");let C0=(()=>{class e{get floating(){return this._floating}set floating(n){this._floating=n,this.monitorResize&&this._handleResize()}get monitorResize(){return this._monitorResize}set monitorResize(n){this._monitorResize=n,this._monitorResize?this._subscribeToResize():this._resizeSubscription.unsubscribe()}constructor(n){this._elementRef=n,this._floating=!1,this._monitorResize=!1,this._resizeObserver=N(jL),this._ngZone=N(H),this._parent=N(w0),this._resizeSubscription=new ft}ngOnDestroy(){this._resizeSubscription.unsubscribe()}getWidth(){return function vV(e){if(null!==e.offsetParent)return e.scrollWidth;const n=e.cloneNode(!0);n.style.setProperty("position","absolute"),n.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(n);const i=n.scrollWidth;return n.remove(),i}(this._elementRef.nativeElement)}get element(){return this._elementRef.nativeElement}_handleResize(){setTimeout(()=>this._parent._handleLabelResized())}_subscribeToResize(){this._resizeSubscription.unsubscribe(),this._ngZone.runOutsideAngular(()=>{this._resizeSubscription=this._resizeObserver.observe(this._elementRef.nativeElement,{box:"border-box"}).subscribe(()=>this._handleResize())})}static#e=this.\u0275fac=function(i){return new(i||e)(_(ye))};static#t=this.\u0275dir=I({type:e,selectors:[["label","matFormFieldFloatingLabel",""]],hostAttrs:[1,"mdc-floating-label","mat-mdc-floating-label"],hostVars:2,hostBindings:function(i,o){2&i&&st("mdc-floating-label--float-above",o.floating)},inputs:{floating:"floating",monitorResize:"monitorResize"}})}return e})();const E0="mdc-line-ripple--active",Dl="mdc-line-ripple--deactivating";let M0=(()=>{class e{constructor(n,i){this._elementRef=n,this._handleTransitionEnd=o=>{const r=this._elementRef.nativeElement.classList,s=r.contains(Dl);"opacity"===o.propertyName&&s&&r.remove(E0,Dl)},i.runOutsideAngular(()=>{n.nativeElement.addEventListener("transitionend",this._handleTransitionEnd)})}activate(){const n=this._elementRef.nativeElement.classList;n.remove(Dl),n.add(E0)}deactivate(){this._elementRef.nativeElement.classList.add(Dl)}ngOnDestroy(){this._elementRef.nativeElement.removeEventListener("transitionend",this._handleTransitionEnd)}static#e=this.\u0275fac=function(i){return new(i||e)(_(ye),_(H))};static#t=this.\u0275dir=I({type:e,selectors:[["div","matFormFieldLineRipple",""]],hostAttrs:[1,"mdc-line-ripple"]})}return e})(),I0=(()=>{class e{constructor(n,i){this._elementRef=n,this._ngZone=i,this.open=!1}ngAfterViewInit(){const n=this._elementRef.nativeElement.querySelector(".mdc-floating-label");n?(this._elementRef.nativeElement.classList.add("mdc-notched-outline--upgraded"),"function"==typeof requestAnimationFrame&&(n.style.transitionDuration="0s",this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>n.style.transitionDuration="")}))):this._elementRef.nativeElement.classList.add("mdc-notched-outline--no-label")}_setNotchWidth(n){this._notch.nativeElement.style.width=this.open&&n?`calc(${n}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + 9px)`:""}static#e=this.\u0275fac=function(i){return new(i||e)(_(ye),_(H))};static#t=this.\u0275cmp=_i({type:e,selectors:[["div","matFormFieldNotchedOutline",""]],viewQuery:function(i,o){if(1&i&&Sn(z2,5),2&i){let r;Ye(r=Ke())&&(o._notch=r.first)}},hostAttrs:[1,"mdc-notched-outline"],hostVars:2,hostBindings:function(i,o){2&i&&st("mdc-notched-outline--notched",o.open)},inputs:{open:["matFormFieldNotchedOutlineOpen","open"]},attrs:G2,ngContentSelectors:W2,decls:5,vars:0,consts:[[1,"mdc-notched-outline__leading"],[1,"mdc-notched-outline__notch"],["notch",""],[1,"mdc-notched-outline__trailing"]],template:function(i,o){1&i&&(Aa(),Rt(0,"div",0),ae(1,"div",1,2),dt(3),le(),Rt(4,"div",3))},encapsulation:2,changeDetection:0})}return e})();const yV={transitionMessages:HL("transitionMessages",[$L("enter",QD({opacity:1,transform:"translateY(0%)"})),zL("void => enter",[QD({opacity:0,transform:"translateY(-5px)"}),UL("300ms cubic-bezier(0.55, 0, 0.55, 0.2)")])])};let A0=(()=>{class e{static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275dir=I({type:e})}return e})();const T0=new w("MatFormField"),xV=new w("MAT_FORM_FIELD_DEFAULT_OPTIONS");let S0=0,CV=(()=>{class e{get hideRequiredMarker(){return this._hideRequiredMarker}set hideRequiredMarker(n){this._hideRequiredMarker=bl(n)}get floatLabel(){return this._floatLabel||this._defaults?.floatLabel||"auto"}set floatLabel(n){n!==this._floatLabel&&(this._floatLabel=n,this._changeDetectorRef.markForCheck())}get appearance(){return this._appearance}set appearance(n){const i=this._appearance;this._appearance=n||this._defaults?.appearance||"fill","outline"===this._appearance&&this._appearance!==i&&(this._needsOutlineLabelOffsetUpdateOnStable=!0)}get subscriptSizing(){return this._subscriptSizing||this._defaults?.subscriptSizing||"fixed"}set subscriptSizing(n){this._subscriptSizing=n||this._defaults?.subscriptSizing||"fixed"}get hintLabel(){return this._hintLabel}set hintLabel(n){this._hintLabel=n,this._processHints()}get _control(){return this._explicitFormFieldControl||this._formFieldControl}set _control(n){this._explicitFormFieldControl=n}constructor(n,i,o,r,s,a,l,c){this._elementRef=n,this._changeDetectorRef=i,this._ngZone=o,this._dir=r,this._platform=s,this._defaults=a,this._animationMode=l,this._hideRequiredMarker=!1,this.color="primary",this._appearance="fill",this._subscriptSizing=null,this._hintLabel="",this._hasIconPrefix=!1,this._hasTextPrefix=!1,this._hasIconSuffix=!1,this._hasTextSuffix=!1,this._labelId="mat-mdc-form-field-label-"+S0++,this._hintLabelId="mat-mdc-hint-"+S0++,this._subscriptAnimationState="",this._destroyed=new et,this._isFocused=null,this._needsOutlineLabelOffsetUpdateOnStable=!1,a&&(a.appearance&&(this.appearance=a.appearance),this._hideRequiredMarker=!!a?.hideRequiredMarker,a.color&&(this.color=a.color))}ngAfterViewInit(){this._updateFocusState(),this._subscriptAnimationState="enter",this._changeDetectorRef.detectChanges()}ngAfterContentInit(){this._assertFormFieldControl(),this._initializeControl(),this._initializeSubscript(),this._initializePrefixAndSuffix(),this._initializeOutlineLabelOffsetSubscriptions()}ngAfterContentChecked(){this._assertFormFieldControl()}ngOnDestroy(){this._destroyed.next(),this._destroyed.complete()}getLabelId(){return this._hasFloatingLabel()?this._labelId:null}getConnectedOverlayOrigin(){return this._textField||this._elementRef}_animateAndLockLabel(){this._hasFloatingLabel()&&(this.floatLabel="always")}_initializeControl(){const n=this._control;n.controlType&&this._elementRef.nativeElement.classList.add(`mat-mdc-form-field-type-${n.controlType}`),n.stateChanges.subscribe(()=>{this._updateFocusState(),this._syncDescribedByIds(),this._changeDetectorRef.markForCheck()}),n.ngControl&&n.ngControl.valueChanges&&n.ngControl.valueChanges.pipe(wo(this._destroyed)).subscribe(()=>this._changeDetectorRef.markForCheck())}_checkPrefixAndSuffixTypes(){this._hasIconPrefix=!!this._prefixChildren.find(n=>!n._isText),this._hasTextPrefix=!!this._prefixChildren.find(n=>n._isText),this._hasIconSuffix=!!this._suffixChildren.find(n=>!n._isText),this._hasTextSuffix=!!this._suffixChildren.find(n=>n._isText)}_initializePrefixAndSuffix(){this._checkPrefixAndSuffixTypes(),qD(this._prefixChildren.changes,this._suffixChildren.changes).subscribe(()=>{this._checkPrefixAndSuffixTypes(),this._changeDetectorRef.markForCheck()})}_initializeSubscript(){this._hintChildren.changes.subscribe(()=>{this._processHints(),this._changeDetectorRef.markForCheck()}),this._errorChildren.changes.subscribe(()=>{this._syncDescribedByIds(),this._changeDetectorRef.markForCheck()}),this._validateHints(),this._syncDescribedByIds()}_assertFormFieldControl(){}_updateFocusState(){this._control.focused&&!this._isFocused?(this._isFocused=!0,this._lineRipple?.activate()):!this._control.focused&&(this._isFocused||null===this._isFocused)&&(this._isFocused=!1,this._lineRipple?.deactivate()),this._textField?.nativeElement.classList.toggle("mdc-text-field--focused",this._control.focused)}_initializeOutlineLabelOffsetSubscriptions(){this._prefixChildren.changes.subscribe(()=>this._needsOutlineLabelOffsetUpdateOnStable=!0),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.pipe(wo(this._destroyed)).subscribe(()=>{this._needsOutlineLabelOffsetUpdateOnStable&&(this._needsOutlineLabelOffsetUpdateOnStable=!1,this._updateOutlineLabelOffset())})}),this._dir.change.pipe(wo(this._destroyed)).subscribe(()=>this._needsOutlineLabelOffsetUpdateOnStable=!0)}_shouldAlwaysFloat(){return"always"===this.floatLabel}_hasOutline(){return"outline"===this.appearance}_forceDisplayInfixLabel(){return!this._platform.isBrowser&&this._prefixChildren.length&&!this._shouldLabelFloat()}_hasFloatingLabel(){return!!this._labelChildNonStatic||!!this._labelChildStatic}_shouldLabelFloat(){return this._control.shouldLabelFloat||this._shouldAlwaysFloat()}_shouldForward(n){const i=this._control?this._control.ngControl:null;return i&&i[n]}_getDisplayedMessages(){return this._errorChildren&&this._errorChildren.length>0&&this._control.errorState?"error":"hint"}_handleLabelResized(){this._refreshOutlineNotchWidth()}_refreshOutlineNotchWidth(){this._hasOutline()&&this._floatingLabel&&this._shouldLabelFloat()?this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth()):this._notchedOutline?._setNotchWidth(0)}_processHints(){this._validateHints(),this._syncDescribedByIds()}_validateHints(){}_syncDescribedByIds(){if(this._control){let n=[];if(this._control.userAriaDescribedBy&&"string"==typeof this._control.userAriaDescribedBy&&n.push(...this._control.userAriaDescribedBy.split(" ")),"hint"===this._getDisplayedMessages()){const i=this._hintChildren?this._hintChildren.find(r=>"start"===r.align):null,o=this._hintChildren?this._hintChildren.find(r=>"end"===r.align):null;i?n.push(i.id):this._hintLabel&&n.push(this._hintLabelId),o&&n.push(o.id)}else this._errorChildren&&n.push(...this._errorChildren.map(i=>i.id));this._control.setDescribedByIds(n)}}_updateOutlineLabelOffset(){if(!this._platform.isBrowser||!this._hasOutline()||!this._floatingLabel)return;const n=this._floatingLabel.element;if(!this._iconPrefixContainer&&!this._textPrefixContainer)return void(n.style.transform="");if(!this._isAttachedToDom())return void(this._needsOutlineLabelOffsetUpdateOnStable=!0);const i=this._iconPrefixContainer?.nativeElement,o=this._textPrefixContainer?.nativeElement,r=i?.getBoundingClientRect().width??0,s=o?.getBoundingClientRect().width??0;n.style.transform=`var(\n        --mat-mdc-form-field-label-transform,\n        translateY(-50%) translateX(calc(${"rtl"===this._dir.value?"-1":"1"} * (${r+s}px + var(--mat-mdc-form-field-label-offset-x, 0px))))\n    )`}_isAttachedToDom(){const n=this._elementRef.nativeElement;if(n.getRootNode){const i=n.getRootNode();return i&&i!==n}return document.documentElement.contains(n)}static#e=this.\u0275fac=function(i){return new(i||e)(_(ye),_(cr),_(H),_(TL),_(nn),_(xV,8),_(Rs,8),_(ut))};static#t=this.\u0275cmp=_i({type:e,selectors:[["mat-form-field"]],contentQueries:function(i,o,r){if(1&i&&(Nn(r,ih,5),Nn(r,ih,7),Nn(r,A0,5),Nn(r,bV,5),Nn(r,_V,5),Nn(r,pV,5),Nn(r,D0,5)),2&i){let s;Ye(s=Ke())&&(o._labelChildNonStatic=s.first),Ye(s=Ke())&&(o._labelChildStatic=s.first),Ye(s=Ke())&&(o._formFieldControl=s.first),Ye(s=Ke())&&(o._prefixChildren=s),Ye(s=Ke())&&(o._suffixChildren=s),Ye(s=Ke())&&(o._errorChildren=s),Ye(s=Ke())&&(o._hintChildren=s)}},viewQuery:function(i,o){if(1&i&&(Sn(q2,5),Sn(Z2,5),Sn(Y2,5),Sn(C0,5),Sn(I0,5),Sn(M0,5)),2&i){let r;Ye(r=Ke())&&(o._textField=r.first),Ye(r=Ke())&&(o._iconPrefixContainer=r.first),Ye(r=Ke())&&(o._textPrefixContainer=r.first),Ye(r=Ke())&&(o._floatingLabel=r.first),Ye(r=Ke())&&(o._notchedOutline=r.first),Ye(r=Ke())&&(o._lineRipple=r.first)}},hostAttrs:[1,"mat-mdc-form-field"],hostVars:42,hostBindings:function(i,o){2&i&&st("mat-mdc-form-field-label-always-float",o._shouldAlwaysFloat())("mat-mdc-form-field-has-icon-prefix",o._hasIconPrefix)("mat-mdc-form-field-has-icon-suffix",o._hasIconSuffix)("mat-form-field-invalid",o._control.errorState)("mat-form-field-disabled",o._control.disabled)("mat-form-field-autofilled",o._control.autofilled)("mat-form-field-no-animations","NoopAnimations"===o._animationMode)("mat-form-field-appearance-fill","fill"==o.appearance)("mat-form-field-appearance-outline","outline"==o.appearance)("mat-form-field-hide-placeholder",o._hasFloatingLabel()&&!o._shouldLabelFloat())("mat-focused",o._control.focused)("mat-primary","accent"!==o.color&&"warn"!==o.color)("mat-accent","accent"===o.color)("mat-warn","warn"===o.color)("ng-untouched",o._shouldForward("untouched"))("ng-touched",o._shouldForward("touched"))("ng-pristine",o._shouldForward("pristine"))("ng-dirty",o._shouldForward("dirty"))("ng-valid",o._shouldForward("valid"))("ng-invalid",o._shouldForward("invalid"))("ng-pending",o._shouldForward("pending"))},inputs:{hideRequiredMarker:"hideRequiredMarker",color:"color",floatLabel:"floatLabel",appearance:"appearance",subscriptSizing:"subscriptSizing",hintLabel:"hintLabel"},exportAs:["matFormField"],features:[oe([{provide:T0,useExisting:e},{provide:w0,useExisting:e}])],ngContentSelectors:mV,decls:18,vars:21,consts:[["labelTemplate",""],[1,"mat-mdc-text-field-wrapper","mdc-text-field",3,"click"],["textField",""],["class","mat-mdc-form-field-focus-overlay"],[1,"mat-mdc-form-field-flex"],["matFormFieldNotchedOutline","",3,"matFormFieldNotchedOutlineOpen"],["class","mat-mdc-form-field-icon-prefix"],["class","mat-mdc-form-field-text-prefix"],[1,"mat-mdc-form-field-infix"],[3,"ngTemplateOutlet"],["class","mat-mdc-form-field-text-suffix"],["class","mat-mdc-form-field-icon-suffix"],["matFormFieldLineRipple",""],[1,"mat-mdc-form-field-subscript-wrapper","mat-mdc-form-field-bottom-align"],["matFormFieldFloatingLabel","",3,"floating","monitorResize","id"],["aria-hidden","true","class","mat-mdc-form-field-required-marker mdc-floating-label--required"],["aria-hidden","true",1,"mat-mdc-form-field-required-marker","mdc-floating-label--required"],[1,"mat-mdc-form-field-focus-overlay"],[1,"mat-mdc-form-field-icon-prefix"],["iconPrefixContainer",""],[1,"mat-mdc-form-field-text-prefix"],["textPrefixContainer",""],[1,"mat-mdc-form-field-text-suffix"],[1,"mat-mdc-form-field-icon-suffix"],[1,"mat-mdc-form-field-error-wrapper"],[1,"mat-mdc-form-field-hint-wrapper"],[3,"id"],[1,"mat-mdc-form-field-hint-spacer"]],template:function(i,o){if(1&i&&(Aa(hV),Fe(0,X2,1,1,"ng-template",null,0,Rv),ae(2,"div",1,2),ct("click",function(s){return o._control.onContainerClick(s)}),Fe(4,J2,1,0,"div",3),ae(5,"div",4),Fe(6,nV,2,2,"div",5)(7,iV,3,0,"div",6)(8,oV,3,0,"div",7),ae(9,"div",8),Fe(10,sV,1,1,null,9),dt(11),le(),Fe(12,aV,2,0,"div",10)(13,lV,2,0,"div",11),le(),Fe(14,cV,1,0,"div",12),le(),ae(15,"div",13),Fe(16,dV,2,1)(17,fV,5,2),le()),2&i){let r;ge(2),st("mdc-text-field--filled",!o._hasOutline())("mdc-text-field--outlined",o._hasOutline())("mdc-text-field--no-label",!o._hasFloatingLabel())("mdc-text-field--disabled",o._control.disabled)("mdc-text-field--invalid",o._control.errorState),ge(2),at(4,o._hasOutline()||o._control.disabled?-1:4),ge(2),at(6,o._hasOutline()?6:-1),ge(),at(7,o._hasIconPrefix?7:-1),ge(),at(8,o._hasTextPrefix?8:-1),ge(2),at(10,!o._hasOutline()||o._forceDisplayInfixLabel()?10:-1),ge(2),at(12,o._hasTextSuffix?12:-1),ge(),at(13,o._hasIconSuffix?13:-1),ge(),at(14,o._hasOutline()?-1:14),ge(),st("mat-mdc-form-field-subscript-dynamic-size","dynamic"===o.subscriptSizing),ge(),at(16,"error"===(r=o._getDisplayedMessages())?16:"hint"===r?17:-1)}},dependencies:[Zy,D0,C0,I0,M0],styles:['.mdc-text-field{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:0;border-bottom-left-radius:0;display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-text-field__input{height:28px;width:100%;min-width:0;border:none;border-radius:0;background:none;appearance:none;padding:0}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}@media all{.mdc-text-field__input::placeholder{opacity:0}}@media all{.mdc-text-field__input:-ms-input-placeholder{opacity:0}}@media all{.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}}@media all{.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}}.mdc-text-field__affix{height:28px;opacity:0;white-space:nowrap}.mdc-text-field--label-floating .mdc-text-field__affix,.mdc-text-field--no-label .mdc-text-field__affix{opacity:1}@supports(-webkit-hyphens: none){.mdc-text-field--outlined .mdc-text-field__affix{align-items:center;align-self:center;display:inline-flex;height:100%}}.mdc-text-field__affix--prefix{padding-left:0;padding-right:2px}[dir=rtl] .mdc-text-field__affix--prefix,.mdc-text-field__affix--prefix[dir=rtl]{padding-left:2px;padding-right:0}.mdc-text-field--end-aligned .mdc-text-field__affix--prefix{padding-left:0;padding-right:12px}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__affix--prefix,.mdc-text-field--end-aligned .mdc-text-field__affix--prefix[dir=rtl]{padding-left:12px;padding-right:0}.mdc-text-field__affix--suffix{padding-left:12px;padding-right:0}[dir=rtl] .mdc-text-field__affix--suffix,.mdc-text-field__affix--suffix[dir=rtl]{padding-left:0;padding-right:12px}.mdc-text-field--end-aligned .mdc-text-field__affix--suffix{padding-left:2px;padding-right:0}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__affix--suffix,.mdc-text-field--end-aligned .mdc-text-field__affix--suffix[dir=rtl]{padding-left:0;padding-right:2px}.mdc-text-field--filled{height:56px}.mdc-text-field--filled::before{display:inline-block;width:0;height:40px;content:"";vertical-align:0}.mdc-text-field--filled .mdc-floating-label{left:16px;right:initial}[dir=rtl] .mdc-text-field--filled .mdc-floating-label,.mdc-text-field--filled .mdc-floating-label[dir=rtl]{left:initial;right:16px}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled.mdc-text-field--no-label::before{display:none}@supports(-webkit-hyphens: none){.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__affix{align-items:center;align-self:center;display:inline-flex;height:100%}}.mdc-text-field--outlined{height:56px;overflow:visible}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1)}.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--outlined .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-shape-small, 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px,var(--mdc-shape-small, 4px))*2)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}@supports(top: max(0%)){.mdc-text-field--outlined{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined{padding-right:max(16px,var(--mdc-shape-small, 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-right:max(16px,var(--mdc-shape-small, 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-left:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-right:max(16px,var(--mdc-shape-small, 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-right:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:max(16px,var(--mdc-shape-small, 4px))}}.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-right:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-right:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:initial}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label,.mdc-text-field--outlined .mdc-floating-label[dir=rtl]{left:initial;right:4px}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mdc-text-field--textarea{flex-direction:column;align-items:center;width:auto;height:auto;padding:0}.mdc-text-field--textarea .mdc-floating-label{top:19px}.mdc-text-field--textarea .mdc-floating-label:not(.mdc-floating-label--float-above){transform:none}.mdc-text-field--textarea .mdc-text-field__input{flex-grow:1;height:auto;min-height:1.5rem;overflow-x:hidden;overflow-y:auto;box-sizing:border-box;resize:none;padding:0 16px}.mdc-text-field--textarea.mdc-text-field--filled::before{display:none}.mdc-text-field--textarea.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-10.25px) scale(0.75)}.mdc-text-field--textarea.mdc-text-field--filled .mdc-text-field__input{margin-top:23px;margin-bottom:9px}.mdc-text-field--textarea.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{margin-top:16px;margin-bottom:16px}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:0}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-27.25px) scale(1)}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-24.75px) scale(0.75)}.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-text-field__input{margin-top:16px;margin-bottom:16px}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label{top:18px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field__input{margin-bottom:2px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter{align-self:flex-end;padding:0 16px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter::after{display:inline-block;width:0;height:16px;content:"";vertical-align:-16px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter::before{display:none}.mdc-text-field__resizer{align-self:stretch;display:inline-flex;flex-direction:column;flex-grow:1;max-height:100%;max-width:100%;min-height:56px;min-width:fit-content;min-width:-moz-available;min-width:-webkit-fill-available;overflow:hidden;resize:both}.mdc-text-field--filled .mdc-text-field__resizer{transform:translateY(-1px)}.mdc-text-field--filled .mdc-text-field__resizer .mdc-text-field__input,.mdc-text-field--filled .mdc-text-field__resizer .mdc-text-field-character-counter{transform:translateY(1px)}.mdc-text-field--outlined .mdc-text-field__resizer{transform:translateX(-1px) translateY(-1px)}[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer,.mdc-text-field--outlined .mdc-text-field__resizer[dir=rtl]{transform:translateX(1px) translateY(-1px)}.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input,.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter{transform:translateX(1px) translateY(1px)}[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input,[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter,.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input[dir=rtl],.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter[dir=rtl]{transform:translateX(-1px) translateY(1px)}.mdc-text-field--with-leading-icon{padding-left:0;padding-right:16px}[dir=rtl] .mdc-text-field--with-leading-icon,.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:16px;padding-right:0}.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 48px);left:48px;right:initial}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label,.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label[dir=rtl]{left:initial;right:48px}.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 64px/0.75)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label{left:36px;right:initial}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label[dir=rtl]{left:initial;right:36px}.mdc-text-field--with-leading-icon.mdc-text-field--outlined :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) translateX(-32px) scale(1)}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-37.25px) translateX(32px) scale(1)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) translateX(-32px) scale(0.75)}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl],.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-34.75px) translateX(32px) scale(0.75)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--with-trailing-icon{padding-left:16px;padding-right:0}[dir=rtl] .mdc-text-field--with-trailing-icon,.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0;padding-right:16px}.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 64px)}.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 64px/0.75)}.mdc-text-field--with-trailing-icon.mdc-text-field--outlined :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 96px)}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 96px/0.75)}.mdc-text-field-helper-line{display:flex;justify-content:space-between;box-sizing:border-box}.mdc-text-field+.mdc-text-field-helper-line{padding-right:16px;padding-left:16px}.mdc-form-field>.mdc-text-field+label{align-self:flex-start}.mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--focused .mdc-notched-outline__trailing{border-width:2px}.mdc-text-field--focused+.mdc-text-field-helper-line .mdc-text-field-helper-text:not(.mdc-text-field-helper-text--validation-msg){opacity:1}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-text-field--focused.mdc-text-field--outlined.mdc-text-field--textarea .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:0}.mdc-text-field--invalid+.mdc-text-field-helper-line .mdc-text-field-helper-text--validation-msg{opacity:1}.mdc-text-field--disabled{pointer-events:none}@media screen and (forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--disabled .mdc-floating-label{cursor:default}.mdc-text-field--disabled.mdc-text-field--filled .mdc-text-field__ripple{display:none}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--end-aligned .mdc-text-field__input{text-align:right}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__input,.mdc-text-field--end-aligned .mdc-text-field__input[dir=rtl]{text-align:left}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__input,[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__input,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix{direction:ltr}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--prefix{padding-left:0;padding-right:2px}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--suffix{padding-left:12px;padding-right:0}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__icon--leading,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__icon--leading{order:1}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--suffix{order:2}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__input,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__input{order:3}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--prefix{order:4}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__icon--trailing,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__icon--trailing{order:5}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__input,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__input{text-align:right}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__affix--prefix{padding-right:12px}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__affix--suffix{padding-left:2px}.mdc-floating-label{position:absolute;left:0;-webkit-transform-origin:left top;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label,.mdc-floating-label[dir=rtl]{right:0;left:auto;-webkit-transform-origin:right top;transform-origin:right top;text-align:right}.mdc-floating-label--float-above{cursor:auto}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0px;content:"*"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after,.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)[dir=rtl]::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline,.mdc-notched-outline[dir=rtl]{text-align:right}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{box-sizing:border-box;height:100%;pointer-events:none}.mdc-notched-outline__trailing{flex-grow:1}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch,.mdc-notched-outline--notched .mdc-notched-outline__notch[dir=rtl]{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:""}.mdc-line-ripple::before{z-index:1}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{border-top:1px solid;border-bottom:1px solid}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;width:12px}[dir=rtl] .mdc-notched-outline__leading,.mdc-notched-outline__leading[dir=rtl]{border-left:none;border-right:1px solid}.mdc-notched-outline__trailing{border-left:none;border-right:1px solid}[dir=rtl] .mdc-notched-outline__trailing,.mdc-notched-outline__trailing[dir=rtl]{border-left:1px solid;border-right:none}.mdc-notched-outline__notch{max-width:calc(100% - 12px*2)}.mdc-line-ripple::before{border-bottom-width:1px}.mdc-line-ripple::after{border-bottom-width:2px}.mdc-text-field--filled{border-top-left-radius:var(--mdc-filled-text-field-container-shape);border-top-right-radius:var(--mdc-filled-text-field-container-shape);border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-caret-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-error-caret-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-filled-text-field-input-text-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-filled-text-field-disabled-input-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-label-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-focus-label-text-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-disabled-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-focus-label-text-color)}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mdc-filled-text-field-label-text-font);font-size:var(--mdc-filled-text-field-label-text-size);font-weight:var(--mdc-filled-text-field-label-text-weight);letter-spacing:var(--mdc-filled-text-field-label-text-tracking)}@media all{.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}}@media all{.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mdc-filled-text-field-container-color)}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mdc-filled-text-field-disabled-container-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-active-indicator-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-hover-active-indicator-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-focus-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-disabled-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-hover-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-error-focus-active-indicator-color)}.mdc-text-field--filled .mdc-line-ripple::before{border-bottom-width:var(--mdc-filled-text-field-active-indicator-height)}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mdc-filled-text-field-focus-active-indicator-height)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-caret-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-error-caret-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-outlined-text-field-input-text-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-outlined-text-field-disabled-input-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-label-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-focus-label-text-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-disabled-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-focus-label-text-color)}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mdc-outlined-text-field-label-text-font);font-size:var(--mdc-outlined-text-field-label-text-size);font-weight:var(--mdc-outlined-text-field-label-text-weight);letter-spacing:var(--mdc-outlined-text-field-label-text-tracking)}@media all{.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color)}}@media all{.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:var(--mdc-outlined-text-field-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape)}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-left-radius:0}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-outlined-text-field-container-shape))}}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px,var(--mdc-outlined-text-field-container-shape))*2)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-left-radius:0}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:var(--mdc-outlined-text-field-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape)}@supports(top: max(0%)){.mdc-text-field--outlined{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-left:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-right:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:max(16px,var(--mdc-outlined-text-field-container-shape))}}.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-right:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-right:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-hover-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-focus-outline-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-disabled-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-hover-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-focus-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__trailing{border-width:var(--mdc-outlined-text-field-outline-width)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-width:var(--mdc-outlined-text-field-focus-outline-width)}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-text-field-wrapper::before{content:none}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height);padding-top:var(--mat-form-field-filled-with-label-container-padding-top);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding);padding-bottom:var(--mat-form-field-container-vertical-padding)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:"";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color)}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font);line-height:var(--mat-form-field-subscript-text-line-height);font-size:var(--mat-form-field-subscript-text-size);letter-spacing:var(--mat-form-field-subscript-text-tracking);font-weight:var(--mat-form-field-subscript-text-weight)}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color)}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity)}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color)}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color)}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:"";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color)}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color)}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color)}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}.cdk-high-contrast-active .mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font);line-height:var(--mat-form-field-container-text-line-height);font-size:var(--mat-form-field-container-text-size);letter-spacing:var(--mat-form-field-container-text-tracking);font-weight:var(--mat-form-field-container-text-weight)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:12px;box-sizing:content-box}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input{transition:opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}@media all{.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder{transition:opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}}@media all{.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}}@media all{.mdc-text-field--no-label .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder,.mdc-text-field--focused .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}}@media all{.mdc-text-field--no-label .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__affix{transition:opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--filled.mdc-ripple-upgraded--background-focused .mdc-text-field__ripple::before,.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea{transition:none}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea.mdc-text-field--filled .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-textarea-filled 250ms 1}@keyframes mdc-floating-label-shake-float-above-textarea-filled{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-textarea-outlined 250ms 1}@keyframes mdc-floating-label-shake-float-above-textarea-outlined{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon{0%{transform:translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}}[dir=rtl] .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake,.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined[dir=rtl] .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl{0%{transform:translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-standard 250ms 1}@keyframes mdc-floating-label-shake-float-above-standard{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}'],encapsulation:2,data:{animation:[yV.transitionMessages]},changeDetection:0})}return e})(),oh=(()=>{class e{static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({imports:[Ln,Ky,YL,Ln]})}return e})();const O0=Zr({passive:!0});let EV=(()=>{class e{constructor(n,i){this._platform=n,this._ngZone=i,this._monitoredElements=new Map}monitor(n){if(!this._platform.isBrowser)return gl;const i=hi(n),o=this._monitoredElements.get(i);if(o)return o.subject;const r=new et,s="cdk-text-field-autofilled",a=l=>{"cdk-text-field-autofill-start"!==l.animationName||i.classList.contains(s)?"cdk-text-field-autofill-end"===l.animationName&&i.classList.contains(s)&&(i.classList.remove(s),this._ngZone.run(()=>r.next({target:l.target,isAutofilled:!1}))):(i.classList.add(s),this._ngZone.run(()=>r.next({target:l.target,isAutofilled:!0})))};return this._ngZone.runOutsideAngular(()=>{i.addEventListener("animationstart",a,O0),i.classList.add("cdk-text-field-autofill-monitored")}),this._monitoredElements.set(i,{subject:r,unlisten:()=>{i.removeEventListener("animationstart",a,O0)}}),r}stopMonitoring(n){const i=hi(n),o=this._monitoredElements.get(i);o&&(o.unlisten(),o.subject.complete(),i.classList.remove("cdk-text-field-autofill-monitored"),i.classList.remove("cdk-text-field-autofilled"),this._monitoredElements.delete(i))}ngOnDestroy(){this._monitoredElements.forEach((n,i)=>this.stopMonitoring(i))}static#e=this.\u0275fac=function(i){return new(i||e)(C(nn),C(H))};static#t=this.\u0275prov=O({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),MV=(()=>{class e{static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({})}return e})();const IV=new w("MAT_INPUT_VALUE_ACCESSOR"),AV=["button","checkbox","file","hidden","image","radio","range","reset","submit"];let TV=0;const SV=k2(class{constructor(e,t,n,i){this._defaultErrorStateMatcher=e,this._parentForm=t,this._parentFormGroup=n,this.ngControl=i,this.stateChanges=new et}});let NV=(()=>{class e extends SV{get disabled(){return this._disabled}set disabled(n){this._disabled=bl(n),this.focused&&(this.focused=!1,this.stateChanges.next())}get id(){return this._id}set id(n){this._id=n||this._uid}get required(){return this._required??this.ngControl?.control?.hasValidator(Hx.required)??!1}set required(n){this._required=bl(n)}get type(){return this._type}set type(n){this._type=n||"text",this._validateType(),!this._isTextarea&&zD().has(this._type)&&(this._elementRef.nativeElement.type=this._type)}get value(){return this._inputValueAccessor.value}set value(n){n!==this.value&&(this._inputValueAccessor.value=n,this.stateChanges.next())}get readonly(){return this._readonly}set readonly(n){this._readonly=bl(n)}constructor(n,i,o,r,s,a,l,c,d,u){super(a,r,s,o),this._elementRef=n,this._platform=i,this._autofillMonitor=c,this._formField=u,this._uid="mat-input-"+TV++,this.focused=!1,this.stateChanges=new et,this.controlType="mat-input",this.autofilled=!1,this._disabled=!1,this._type="text",this._readonly=!1,this._neverEmptyInputTypes=["date","datetime","datetime-local","month","time","week"].filter(m=>zD().has(m)),this._iOSKeyupListener=m=>{const p=m.target;!p.value&&0===p.selectionStart&&0===p.selectionEnd&&(p.setSelectionRange(1,1),p.setSelectionRange(0,0))};const f=this._elementRef.nativeElement,h=f.nodeName.toLowerCase();this._inputValueAccessor=l||f,this._previousNativeValue=this.value,this.id=this.id,i.IOS&&d.runOutsideAngular(()=>{n.nativeElement.addEventListener("keyup",this._iOSKeyupListener)}),this._isServer=!this._platform.isBrowser,this._isNativeSelect="select"===h,this._isTextarea="textarea"===h,this._isInFormField=!!u,this._isNativeSelect&&(this.controlType=f.multiple?"mat-native-select-multiple":"mat-native-select")}ngAfterViewInit(){this._platform.isBrowser&&this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(n=>{this.autofilled=n.isAutofilled,this.stateChanges.next()})}ngOnChanges(){this.stateChanges.next()}ngOnDestroy(){this.stateChanges.complete(),this._platform.isBrowser&&this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement),this._platform.IOS&&this._elementRef.nativeElement.removeEventListener("keyup",this._iOSKeyupListener)}ngDoCheck(){this.ngControl&&(this.updateErrorState(),null!==this.ngControl.disabled&&this.ngControl.disabled!==this.disabled&&(this.disabled=this.ngControl.disabled,this.stateChanges.next())),this._dirtyCheckNativeValue(),this._dirtyCheckPlaceholder()}focus(n){this._elementRef.nativeElement.focus(n)}_focusChanged(n){n!==this.focused&&(this.focused=n,this.stateChanges.next())}_onInput(){}_dirtyCheckNativeValue(){const n=this._elementRef.nativeElement.value;this._previousNativeValue!==n&&(this._previousNativeValue=n,this.stateChanges.next())}_dirtyCheckPlaceholder(){const n=this._getPlaceholder();if(n!==this._previousPlaceholder){const i=this._elementRef.nativeElement;this._previousPlaceholder=n,n?i.setAttribute("placeholder",n):i.removeAttribute("placeholder")}}_getPlaceholder(){return this.placeholder||null}_validateType(){AV.indexOf(this._type)}_isNeverEmpty(){return this._neverEmptyInputTypes.indexOf(this._type)>-1}_isBadInput(){let n=this._elementRef.nativeElement.validity;return n&&n.badInput}get empty(){return!(this._isNeverEmpty()||this._elementRef.nativeElement.value||this._isBadInput()||this.autofilled)}get shouldLabelFloat(){if(this._isNativeSelect){const n=this._elementRef.nativeElement,i=n.options[0];return this.focused||n.multiple||!this.empty||!!(n.selectedIndex>-1&&i&&i.label)}return this.focused||!this.empty}setDescribedByIds(n){n.length?this._elementRef.nativeElement.setAttribute("aria-describedby",n.join(" ")):this._elementRef.nativeElement.removeAttribute("aria-describedby")}onContainerClick(){this.focused||this.focus()}_isInlineSelect(){const n=this._elementRef.nativeElement;return this._isNativeSelect&&(n.multiple||n.size>1)}static#e=this.\u0275fac=function(i){return new(i||e)(_(ye),_(nn),_(_n,10),_(hl,8),_(ml,8),_(P2),_(IV,10),_(EV),_(H),_(T0,8))};static#t=this.\u0275dir=I({type:e,selectors:[["input","matInput",""],["textarea","matInput",""],["select","matNativeControl",""],["input","matNativeControl",""],["textarea","matNativeControl",""]],hostAttrs:[1,"mat-mdc-input-element"],hostVars:18,hostBindings:function(i,o){1&i&&ct("focus",function(){return o._focusChanged(!0)})("blur",function(){return o._focusChanged(!1)})("input",function(){return o._onInput()}),2&i&&(Da("id",o.id)("disabled",o.disabled)("required",o.required),yt("name",o.name||null)("readonly",o.readonly&&!o._isNativeSelect||null)("aria-invalid",o.empty&&o.required?null:o.errorState)("aria-required",o.required)("id",o.id),st("mat-input-server",o._isServer)("mat-mdc-form-field-textarea-control",o._isInFormField&&o._isTextarea)("mat-mdc-form-field-input-control",o._isInFormField)("mdc-text-field__input",o._isInFormField)("mat-mdc-native-select-inline",o._isInlineSelect()))},inputs:{disabled:"disabled",id:"id",placeholder:"placeholder",name:"name",required:"required",type:"type",errorStateMatcher:"errorStateMatcher",userAriaDescribedBy:["aria-describedby","userAriaDescribedBy"],value:"value",readonly:"readonly"},exportAs:["matInput"],features:[oe([{provide:A0,useExisting:e}]),W,$t]})}return e})(),FV=(()=>{class e{static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({imports:[Ln,oh,oh,MV,Ln]})}return e})();const OV=["mat-button",""],kV=[[["",8,"material-icons",3,"iconPositionEnd",""],["mat-icon",3,"iconPositionEnd",""],["","matButtonIcon","",3,"iconPositionEnd",""]],"*",[["","iconPositionEnd","",8,"material-icons"],["mat-icon","iconPositionEnd",""],["","matButtonIcon","","iconPositionEnd",""]]],RV=[".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])","*",".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]"],PV=[{attribute:"mat-button",mdcClasses:["mdc-button","mat-mdc-button"]},{attribute:"mat-flat-button",mdcClasses:["mdc-button","mdc-button--unelevated","mat-mdc-unelevated-button"]},{attribute:"mat-raised-button",mdcClasses:["mdc-button","mdc-button--raised","mat-mdc-raised-button"]},{attribute:"mat-stroked-button",mdcClasses:["mdc-button","mdc-button--outlined","mat-mdc-outlined-button"]},{attribute:"mat-fab",mdcClasses:["mdc-fab","mat-mdc-fab"]},{attribute:"mat-mini-fab",mdcClasses:["mdc-fab","mdc-fab--mini","mat-mdc-mini-fab"]},{attribute:"mat-icon-button",mdcClasses:["mdc-icon-button","mat-mdc-icon-button"]}];let LV=(()=>{class e{get ripple(){return this._rippleLoader?.getRipple(this._elementRef.nativeElement)}set ripple(n){this._rippleLoader?.attachRipple(this._elementRef.nativeElement,n)}get disableRipple(){return this._disableRipple}set disableRipple(n){this._disableRipple=n,this._updateRippleDisabled()}get disabled(){return this._disabled}set disabled(n){this._disabled=n,this._updateRippleDisabled()}constructor(n,i,o,r){this._elementRef=n,this._platform=i,this._ngZone=o,this._animationMode=r,this._focusMonitor=N(S2),this._rippleLoader=N($2),this._isFab=!1,this._disableRipple=!1,this._disabled=!1,this._rippleLoader?.configureRipple(this._elementRef.nativeElement,{className:"mat-mdc-button-ripple"});const s=this._elementRef.nativeElement,a=s.classList;for(const{attribute:l,mdcClasses:c}of PV)s.hasAttribute(l)&&a.add(...c)}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0)}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._rippleLoader?.destroyRipple(this._elementRef.nativeElement)}focus(n="program",i){n?this._focusMonitor.focusVia(this._elementRef.nativeElement,n,i):this._elementRef.nativeElement.focus(i)}_updateRippleDisabled(){this._rippleLoader?.setDisabled(this._elementRef.nativeElement,this.disableRipple||this.disabled)}static#e=this.\u0275fac=function(i){!function _g(){throw new Error("invalid")}()};static#t=this.\u0275dir=I({type:e,inputs:{color:"color",disableRipple:["disableRipple","disableRipple",Rr],disabled:["disabled","disabled",Rr]},features:[Kg]})}return e})(),VV=(()=>{class e extends LV{constructor(n,i,o,r){super(n,i,o,r)}static#e=this.\u0275fac=function(i){return new(i||e)(_(ye),_(nn),_(H),_(Rs,8))};static#t=this.\u0275cmp=_i({type:e,selectors:[["button","mat-button",""],["button","mat-raised-button",""],["button","mat-flat-button",""],["button","mat-stroked-button",""]],hostVars:9,hostBindings:function(i,o){2&i&&(yt("disabled",o.disabled||null),mb(o.color?"mat-"+o.color:""),st("_mat-animation-noopable","NoopAnimations"===o._animationMode)("mat-unthemed",!o.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],features:[W],attrs:OV,ngContentSelectors:RV,decls:7,vars:4,consts:[[1,"mat-mdc-button-persistent-ripple"],[1,"mdc-button__label"],[1,"mat-mdc-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(i,o){1&i&&(Aa(kV),Rt(0,"span",0),dt(1),ae(2,"span",1),dt(3,1),le(),dt(4,2),Rt(5,"span",2)(6,"span",3)),2&i&&st("mdc-button__ripple",!o._isFab)("mdc-fab__ripple",o._isFab)},styles:['.mdc-touch-target-wrapper{display:inline}.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-button{position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;user-select:none;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0)}.mdc-button .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__icon{margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top}[dir=rtl] .mdc-button .mdc-button__icon,.mdc-button .mdc-button__icon[dir=rtl]{margin-left:8px;margin-right:0}.mdc-button .mdc-button__progress-indicator{font-size:0;position:absolute;transform:translate(-50%, -50%);top:50%;left:50%;line-height:initial}.mdc-button .mdc-button__label{position:relative}.mdc-button .mdc-button__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px);display:none}@media screen and (forced-colors: active){.mdc-button .mdc-button__focus-ring{border-color:CanvasText}}.mdc-button .mdc-button__focus-ring::after{content:"";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-button .mdc-button__focus-ring::after{border-color:CanvasText}}@media screen and (forced-colors: active){.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring{display:block}}.mdc-button .mdc-button__touch{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}.mdc-button__label+.mdc-button__icon{margin-left:8px;margin-right:0}[dir=rtl] .mdc-button__label+.mdc-button__icon,.mdc-button__label+.mdc-button__icon[dir=rtl]{margin-left:0;margin-right:8px}svg.mdc-button__icon{fill:currentColor}.mdc-button--touch{margin-top:6px;margin-bottom:6px}.mdc-button{padding:0 8px 0 8px}.mdc-button--unelevated{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 16px 0 16px}.mdc-button--unelevated.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--unelevated.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--raised{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 16px 0 16px}.mdc-button--raised.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--raised.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--outlined{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-button--outlined .mdc-button__ripple{border-style:solid;border-color:rgba(0,0,0,0)}.mat-mdc-button{font-family:var(--mdc-text-button-label-text-font);font-size:var(--mdc-text-button-label-text-size);letter-spacing:var(--mdc-text-button-label-text-tracking);font-weight:var(--mdc-text-button-label-text-weight);text-transform:var(--mdc-text-button-label-text-transform);height:var(--mdc-text-button-container-height);border-radius:var(--mdc-text-button-container-shape);--mdc-text-button-container-shape:4px;--mdc-text-button-container-height:36px;--mdc-text-button-keep-touch-target:false}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color)}.mat-mdc-button:disabled{color:var(--mdc-text-button-disabled-label-text-color)}.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-text-button-ripple-color)}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-state-layer-color)}.mat-mdc-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-hover-state-layer-opacity)}.mat-mdc-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-focus-state-layer-opacity)}.mat-mdc-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-pressed-state-layer-opacity)}.mat-mdc-button[disabled]{cursor:default;pointer-events:none;color:var(--mdc-text-button-disabled-label-text-color)}.mat-mdc-unelevated-button{font-family:var(--mdc-filled-button-label-text-font);font-size:var(--mdc-filled-button-label-text-size);letter-spacing:var(--mdc-filled-button-label-text-tracking);font-weight:var(--mdc-filled-button-label-text-weight);text-transform:var(--mdc-filled-button-label-text-transform);height:var(--mdc-filled-button-container-height);border-radius:var(--mdc-filled-button-container-shape);--mdc-filled-button-container-shape:4px;--mdc-filled-button-container-elevation:0;--mdc-filled-button-disabled-container-elevation:0;--mdc-filled-button-focus-container-elevation:0;--mdc-filled-button-hover-container-elevation:0;--mdc-filled-button-keep-touch-target:false;--mdc-filled-button-pressed-container-elevation:0}.mat-mdc-unelevated-button:not(:disabled){background-color:var(--mdc-filled-button-container-color)}.mat-mdc-unelevated-button:disabled{background-color:var(--mdc-filled-button-disabled-container-color)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color)}.mat-mdc-unelevated-button:disabled{color:var(--mdc-filled-button-disabled-label-text-color)}.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-filled-button-ripple-color)}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-state-layer-color)}.mat-mdc-unelevated-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-hover-state-layer-opacity)}.mat-mdc-unelevated-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-focus-state-layer-opacity)}.mat-mdc-unelevated-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-pressed-state-layer-opacity)}.mat-mdc-unelevated-button[disabled]{cursor:default;pointer-events:none;color:var(--mdc-filled-button-disabled-label-text-color);background-color:var(--mdc-filled-button-disabled-container-color)}.mat-mdc-raised-button{font-family:var(--mdc-protected-button-label-text-font);font-size:var(--mdc-protected-button-label-text-size);letter-spacing:var(--mdc-protected-button-label-text-tracking);font-weight:var(--mdc-protected-button-label-text-weight);text-transform:var(--mdc-protected-button-label-text-transform);height:var(--mdc-protected-button-container-height);border-radius:var(--mdc-protected-button-container-shape);--mdc-protected-button-container-shape:4px;--mdc-protected-button-keep-touch-target:false}.mat-mdc-raised-button:not(:disabled){background-color:var(--mdc-protected-button-container-color)}.mat-mdc-raised-button:disabled{background-color:var(--mdc-protected-button-disabled-container-color)}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color)}.mat-mdc-raised-button:disabled{color:var(--mdc-protected-button-disabled-label-text-color)}.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-protected-button-ripple-color)}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-state-layer-color)}.mat-mdc-raised-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-hover-state-layer-opacity)}.mat-mdc-raised-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-focus-state-layer-opacity)}.mat-mdc-raised-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-pressed-state-layer-opacity)}.mat-mdc-raised-button[disabled]{cursor:default;pointer-events:none;color:var(--mdc-protected-button-disabled-label-text-color);background-color:var(--mdc-protected-button-disabled-container-color)}.mat-mdc-raised-button[disabled][disabled]{box-shadow:none}.mat-mdc-outlined-button{font-family:var(--mdc-outlined-button-label-text-font);font-size:var(--mdc-outlined-button-label-text-size);letter-spacing:var(--mdc-outlined-button-label-text-tracking);font-weight:var(--mdc-outlined-button-label-text-weight);text-transform:var(--mdc-outlined-button-label-text-transform);height:var(--mdc-outlined-button-container-height);border-radius:var(--mdc-outlined-button-container-shape);padding:0 15px 0 15px;border-width:var(--mdc-outlined-button-outline-width);--mdc-outlined-button-keep-touch-target:false;--mdc-outlined-button-outline-width:1px;--mdc-outlined-button-container-shape:4px}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color)}.mat-mdc-outlined-button:disabled{color:var(--mdc-outlined-button-disabled-label-text-color)}.mat-mdc-outlined-button .mdc-button__ripple{border-radius:var(--mdc-outlined-button-container-shape)}.mat-mdc-outlined-button:not(:disabled){border-color:var(--mdc-outlined-button-outline-color)}.mat-mdc-outlined-button:disabled{border-color:var(--mdc-outlined-button-disabled-outline-color)}.mat-mdc-outlined-button.mdc-button--icon-trailing{padding:0 11px 0 15px}.mat-mdc-outlined-button.mdc-button--icon-leading{padding:0 15px 0 11px}.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px;border-width:var(--mdc-outlined-button-outline-width)}.mat-mdc-outlined-button .mdc-button__touch{left:calc(-1 * var(--mdc-outlined-button-outline-width));width:calc(100% + 2 * var(--mdc-outlined-button-outline-width))}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-outlined-button-ripple-color)}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-state-layer-color)}.mat-mdc-outlined-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-hover-state-layer-opacity)}.mat-mdc-outlined-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-focus-state-layer-opacity)}.mat-mdc-outlined-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-pressed-state-layer-opacity)}.mat-mdc-outlined-button[disabled]{cursor:default;pointer-events:none;color:var(--mdc-outlined-button-disabled-label-text-color);border-color:var(--mdc-outlined-button-disabled-outline-color)}.mat-mdc-button-base{text-decoration:none}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-outlined-button .mdc-button__label{z-index:1}.mat-mdc-button .mat-mdc-focus-indicator,.mat-mdc-unelevated-button .mat-mdc-focus-indicator,.mat-mdc-raised-button .mat-mdc-focus-indicator,.mat-mdc-outlined-button .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-unelevated-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-raised-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-outlined-button:focus .mat-mdc-focus-indicator::before{content:""}.mat-mdc-button .mat-mdc-button-touch-target,.mat-mdc-unelevated-button .mat-mdc-button-touch-target,.mat-mdc-raised-button .mat-mdc-button-touch-target,.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon{margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}[dir=rtl] .mat-mdc-button>.mat-icon,.mat-mdc-button>.mat-icon[dir=rtl]{margin-left:8px;margin-right:0}.mat-mdc-button .mdc-button__label+.mat-icon{margin-left:8px;margin-right:0}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon,.mat-mdc-button .mdc-button__label+.mat-icon[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem;margin-left:-4px;margin-right:8px}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon,[dir=rtl] .mat-mdc-raised-button>.mat-icon,[dir=rtl] .mat-mdc-outlined-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon[dir=rtl],.mat-mdc-raised-button>.mat-icon[dir=rtl],.mat-mdc-outlined-button>.mat-icon[dir=rtl]{margin-left:8px;margin-right:0}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon,[dir=rtl] .mat-mdc-raised-button>.mat-icon,[dir=rtl] .mat-mdc-outlined-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon[dir=rtl],.mat-mdc-raised-button>.mat-icon[dir=rtl],.mat-mdc-outlined-button>.mat-icon[dir=rtl]{margin-left:8px;margin-right:-4px}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon,.mat-mdc-raised-button .mdc-button__label+.mat-icon,.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-left:8px;margin-right:-4px}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon,.mat-mdc-unelevated-button .mdc-button__label+.mat-icon[dir=rtl],.mat-mdc-raised-button .mdc-button__label+.mat-icon[dir=rtl],.mat-mdc-outlined-button .mdc-button__label+.mat-icon[dir=rtl]{margin-left:-4px;margin-right:8px}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px;border-width:-1px}.mat-mdc-unelevated-button .mat-mdc-focus-indicator::before,.mat-mdc-raised-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 3px)*-1)}',".cdk-high-contrast-active .mat-mdc-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-unelevated-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-raised-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-outlined-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-icon-button{outline:solid 1px}"],encapsulation:2,changeDetection:0})}return e})(),BV=(()=>{class e{static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275mod=Pe({type:e});static#n=this.\u0275inj=Oe({imports:[Ln,U2,Ln]})}return e})();const HV=[{name:"iata-email-confirmation",component:(()=>{class e{constructor(){this.sendEmail=new Ne,this.emailControl=new Hf("",[Hx.required])}onSend(){this.emailControl.invalid||this.sendEmail.emit(this.emailControl.value)}static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275cmp=_i({type:e,selectors:[["iata-cmp-email-confirmation"]],inputs:{userName:"userName"},outputs:{sendEmail:"sendEmail"},standalone:!0,features:[Cu],decls:12,vars:3,consts:[[1,"iata-email-confirmation"],[1,"iata-email-confirmation__greeting"],[1,"iata-email-confirmation__question"],[1,"iata-email-confirmation__email-form"],["appearance","outline","subscriptSizing","dynamic",1,"iata-email-confirmation__email-form__field"],["type","email","matInput","",3,"formControl"],["mat-flat-button","","color","primary",1,"iata-email-confirmation__email-form__send-btn",3,"disabled","click"]],template:function(i,o){1&i&&(ae(0,"div",0)(1,"h3",1),hn(2),le(),ae(3,"div",2),hn(4," Please confirm your email "),le(),ae(5,"div",3)(6,"mat-form-field",4)(7,"mat-label"),hn(8,"Email"),le(),Rt(9,"input",5),le(),ae(10,"button",6),ct("click",function(){return o.onSend()}),hn(11," Send "),le()()()),2&i&&(ge(2),Ar("Hello, ",o.userName,"!"),ge(7),Ft("formControl",o.emailControl),ge(),Ft("disabled",o.emailControl.invalid))},dependencies:[wL,il,dD,oh,CV,ih,FV,NV,CL,$f,BV,VV],styles:[".iata-email-confirmation[_ngcontent-%COMP%]{padding:20px;background-color:#fef8e2}.iata-email-confirmation__greeting[_ngcontent-%COMP%]{font-size:14px;font-weight:500;margin-bottom:20px}.iata-email-confirmation__email-form[_ngcontent-%COMP%]{margin-top:15px;display:flex;align-items:center}.iata-email-confirmation__email-form__field[_ngcontent-%COMP%]{width:300px}.iata-email-confirmation__email-form__send-btn[_ngcontent-%COMP%]{margin-left:20px}"],changeDetection:0})}return e})()},{name:"iata-email-result",component:(()=>{class e{static#e=this.\u0275fac=function(i){return new(i||e)};static#t=this.\u0275cmp=_i({type:e,selectors:[["iata-cmp-email-result"]],inputs:{userName:"userName",email:"email"},standalone:!0,features:[Cu],decls:7,vars:2,consts:[[1,"iata-email-result"],[1,"iata-email-result__title"],[1,"iata-email-result__result"]],template:function(i,o){1&i&&(ae(0,"div",0)(1,"h3",1),hn(2),le(),ae(3,"div",2),hn(4," There is your email: "),ae(5,"strong"),hn(6),le()()()),2&i&&(ge(2),Ar(" Thank you, ",o.userName,"! Your email has been verified. "),ge(4),Ta(o.email))},styles:[".iata-email-result[_ngcontent-%COMP%]{padding:20px;background-color:#e8f6e8}.iata-email-result__title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;margin-bottom:20px}"],changeDetection:0})}return e})()}],wl={schedule(e,t){const n=setTimeout(e,t);return()=>clearTimeout(n)},scheduleBeforeRender(e){if(typeof window>"u")return wl.schedule(e,0);if(typeof window.requestAnimationFrame>"u")return wl.schedule(e,16);const t=window.requestAnimationFrame(e);return()=>window.cancelAnimationFrame(t)}};let rh;function QV(e,t,n){let i=n;return function zV(e){return!!e&&e.nodeType===Node.ELEMENT_NODE}(e)&&t.some((o,r)=>!("*"===o||!function WV(e,t){if(!rh){const n=Element.prototype;rh=n.matches||n.matchesSelector||n.mozMatchesSelector||n.msMatchesSelector||n.oMatchesSelector||n.webkitMatchesSelector}return e.nodeType===Node.ELEMENT_NODE&&rh.call(e,t)}(e,o)||(i=r,0))),i}class JV{constructor(t,n){this.componentFactory=n.get(Gi).resolveComponentFactory(t)}create(t){return new ej(this.componentFactory,t)}}class ej{constructor(t,n){this.componentFactory=t,this.injector=n,this.eventEmitters=new YD(1),this.events=this.eventEmitters.pipe(function UV(e,t){return Dt((n,i)=>{let o=null,r=0,s=!1;const a=()=>s&&!o&&i.complete();n.subscribe(ht(i,l=>{o?.unsubscribe();let c=0;const d=r++;Vt(e(l,d)).subscribe(o=ht(i,u=>i.next(t?t(l,u,d,c++):u),()=>{o=null,a()}))},()=>{s=!0,a()}))})}(i=>qD(...i))),this.componentRef=null,this.viewChangeDetectorRef=null,this.inputChanges=null,this.hasInputChanges=!1,this.implementsOnChanges=!1,this.scheduledChangeDetectionFn=null,this.scheduledDestroyFn=null,this.initialInputValues=new Map,this.unchangedInputs=new Set(this.componentFactory.inputs.map(({propName:i})=>i)),this.ngZone=this.injector.get(H),this.elementZone=typeof Zone>"u"?null:this.ngZone.run(()=>Zone.current)}connect(t){this.runInZone(()=>{if(null!==this.scheduledDestroyFn)return this.scheduledDestroyFn(),void(this.scheduledDestroyFn=null);null===this.componentRef&&this.initializeComponent(t)})}disconnect(){this.runInZone(()=>{null===this.componentRef||null!==this.scheduledDestroyFn||(this.scheduledDestroyFn=wl.schedule(()=>{null!==this.componentRef&&(this.componentRef.destroy(),this.componentRef=null,this.viewChangeDetectorRef=null)},10))})}getInputValue(t){return this.runInZone(()=>null===this.componentRef?this.initialInputValues.get(t):this.componentRef.instance[t])}setInputValue(t,n,i){this.runInZone(()=>{i&&(n=i.call(this.componentRef?.instance,n)),null!==this.componentRef?function qV(e,t){return e===t||e!=e&&t!=t}(n,this.getInputValue(t))&&(void 0!==n||!this.unchangedInputs.has(t))||(this.recordInputChange(t,n),this.unchangedInputs.delete(t),this.hasInputChanges=!0,this.componentRef.instance[t]=n,this.scheduleDetectChanges()):this.initialInputValues.set(t,n)})}initializeComponent(t){const n=bt.create({providers:[],parent:this.injector}),i=function KV(e,t){const n=e.childNodes,i=t.map(()=>[]);let o=-1;t.some((r,s)=>"*"===r&&(o=s,!0));for(let r=0,s=n.length;r<s;++r){const a=n[r],l=QV(a,t,o);-1!==l&&i[l].push(a)}return i}(t,this.componentFactory.ngContentSelectors);this.componentRef=this.componentFactory.create(n,i,t),this.viewChangeDetectorRef=this.componentRef.injector.get(cr),this.implementsOnChanges=function GV(e){return"function"==typeof e}(this.componentRef.instance.ngOnChanges),this.initializeInputs(),this.initializeOutputs(this.componentRef),this.detectChanges(),this.injector.get(ai).attachView(this.componentRef.hostView)}initializeInputs(){this.componentFactory.inputs.forEach(({propName:t,transform:n})=>{this.initialInputValues.has(t)&&this.setInputValue(t,this.initialInputValues.get(t),n)}),this.initialInputValues.clear()}initializeOutputs(t){const n=this.componentFactory.outputs.map(({propName:i,templateName:o})=>t.instance[i].pipe(Bn(s=>({name:o,value:s}))));this.eventEmitters.next(n)}callNgOnChanges(t){if(!this.implementsOnChanges||null===this.inputChanges)return;const n=this.inputChanges;this.inputChanges=null,t.instance.ngOnChanges(n)}markViewForCheck(t){this.hasInputChanges&&(this.hasInputChanges=!1,t.markForCheck())}scheduleDetectChanges(){this.scheduledChangeDetectionFn||(this.scheduledChangeDetectionFn=wl.scheduleBeforeRender(()=>{this.scheduledChangeDetectionFn=null,this.detectChanges()}))}recordInputChange(t,n){if(!this.implementsOnChanges)return;null===this.inputChanges&&(this.inputChanges={});const i=this.inputChanges[t];if(i)return void(i.currentValue=n);const o=this.unchangedInputs.has(t),r=o?void 0:this.getInputValue(t);this.inputChanges[t]=new Xh(r,n,o)}detectChanges(){null!==this.componentRef&&(this.callNgOnChanges(this.componentRef),this.markViewForCheck(this.viewChangeDetectorRef),this.componentRef.changeDetectorRef.detectChanges())}runInZone(t){return this.elementZone&&Zone.current!==this.elementZone?this.ngZone.run(t):t()}}class tj extends HTMLElement{constructor(){super(...arguments),this.ngElementEventsSubscription=null}}!function El(e){return function(){var t=this,n=arguments;return new Promise(function(i,o){var r=e.apply(t,n);function s(l){Eo(r,i,o,s,a,"next",l)}function a(l){Eo(r,i,o,s,a,"throw",l)}s(void 0)})}}(function*(){const e=yield function CR(e){return XF(function ux(e){return{appProviders:[...hx,...e?.providers??[]],platformProviders:AR}}(e))}({providers:[Um(SR)]});!function ij(e,t){e.forEach(n=>{const i=function nj(e,t){const n=function YV(e,t){return t.get(Gi).resolveComponentFactory(e).inputs}(e,t.injector),i=t.strategyFactory||new JV(e,t.injector),o=function ZV(e){const t={};return e.forEach(({propName:n,templateName:i,transform:o})=>{t[function $V(e){return e.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}(i)]=[n,o]}),t}(n);class r extends tj{static#e=this.observedAttributes=Object.keys(o);get ngElementStrategy(){if(!this._ngElementStrategy){const a=this._ngElementStrategy=i.create(this.injector||t.injector);n.forEach(({propName:l,transform:c})=>{if(!this.hasOwnProperty(l))return;const d=this[l];delete this[l],a.setInputValue(l,d,c)})}return this._ngElementStrategy}constructor(a){super(),this.injector=a}attributeChangedCallback(a,l,c,d){const[u,f]=o[a];this.ngElementStrategy.setInputValue(u,c,f)}connectedCallback(){let a=!1;this.ngElementStrategy.events&&(this.subscribeToEvents(),a=!0),this.ngElementStrategy.connect(this),a||this.subscribeToEvents()}disconnectedCallback(){this._ngElementStrategy&&this._ngElementStrategy.disconnect(),this.ngElementEventsSubscription&&(this.ngElementEventsSubscription.unsubscribe(),this.ngElementEventsSubscription=null)}subscribeToEvents(){this.ngElementEventsSubscription=this.ngElementStrategy.events.subscribe(a=>{const l=new CustomEvent(a.name,{detail:a.value});this.dispatchEvent(l)})}}return n.forEach(({propName:s,transform:a})=>{Object.defineProperty(r.prototype,s,{get(){return this.ngElementStrategy.getInputValue(s)},set(l){this.ngElementStrategy.setInputValue(s,l,a)},configurable:!0,enumerable:!0})}),r}(n.component,{injector:t});customElements.define(n.name,i)})}(HV,e.injector)})()}},Eo=>{Eo(Eo.s=31)}]);