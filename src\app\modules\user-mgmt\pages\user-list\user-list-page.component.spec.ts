import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { UserMgmtRequestService } from '../../services/user-mgmt-request.service';
import { of, throwError } from 'rxjs';
import { PageEvent } from '@angular/material/paginator';
import { PaginationResponse } from '@shared/models/pagination-response.model';
// eslint-disable-next-line @typescript-eslint/naming-convention
import UserListPageComponent from './user-list-page.component';
import { TranslateModule } from '@ngx-translate/core';
import { Sort } from '@angular/material/sort';

describe('UserListPageComponent', () => {
	let component: UserListPageComponent;
	let fixture: ComponentFixture<UserListPageComponent>;
	let userMgmtRequestServiceSpy: jasmine.SpyObj<UserMgmtRequestService>;
	let cdrSpy: jasmine.SpyObj<ChangeDetectorRef>;
	let mockSliRecords: any[];
	let mockPaginationResponse: PaginationResponse<any>;

	beforeEach(() => {
		// Create spies for all required services
		userMgmtRequestServiceSpy = jasmine.createSpyObj('UserMgmtRequestService', ['getUserList', 'getSharedSliList']);
		cdrSpy = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

		// Setup mock data
		mockSliRecords = [{ id: 1 }, { id: 2 }, { id: 3 }];
		mockPaginationResponse = {
			rows: mockSliRecords,
			total: mockSliRecords.length,
			pageNum: 1,
		} as PaginationResponse<any>;

		// Configure default spy behavior
		userMgmtRequestServiceSpy.getUserList.and.returnValue(of(mockPaginationResponse));
	});

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [UserListPageComponent, TranslateModule.forRoot()],
			providers: [
				{
					provide: UserMgmtRequestService,
					useValue: userMgmtRequestServiceSpy,
				},
				{
					provide: ChangeDetectorRef,
					useValue: cdrSpy,
				},
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA],
		}).compileComponents();

		fixture = TestBed.createComponent(UserListPageComponent);
		component = fixture.componentInstance;
		component['keyword'] = '';
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should retrieve the user list page with default pagination parameters', () => {
			spyOn<UserListPageComponent, any>(component, 'getUserListPage');

			component.ngOnInit();

			expect(component['getUserListPage']).toHaveBeenCalledWith({ pageNum: 1, pageSize: 10 });
		});
	});

	describe('onPageChange', () => {
		it('should update pagination parameters and retrieve the requested page', () => {
			spyOn<UserListPageComponent, any>(component, 'getUserListPage');
			const pageEvent: PageEvent = { pageIndex: 1, previousPageIndex: 0, pageSize: 10, length: 30 };

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(2);
			expect(component.pageParams.pageSize).toBe(10);
			expect(component['getUserListPage']).toHaveBeenCalledWith({ pageNum: 2, pageSize: 10 });
		});

		it('should update pagination and sort parameters when sort direction is provided', () => {
			spyOn<UserListPageComponent, any>(component, 'getUserListPage');
			const pageEvent: PageEvent & { sortField?: string; sortDirection?: string } = {
				pageIndex: 0,
				previousPageIndex: 0,
				pageSize: 20,
				length: 30,
				sortField: 'updatedAt',
				sortDirection: 'desc',
			};

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(1);
			expect(component.pageParams.pageSize).toBe(20);
			expect(component.pageParams.orderByColumn).toBe('updatedAt');
			expect(component.pageParams.isAsc).toBe('desc');
			expect(component['getUserListPage']).toHaveBeenCalledWith({
				pageNum: 1,
				pageSize: 20,
				orderByColumn: 'updatedAt',
				isAsc: 'desc',
			});
		});
	});

	describe('onSearch', () => {
		it('should update search payload and retrieve user list with current pagination', () => {
			spyOn<UserListPageComponent, any>(component, 'getUserListPage');
			const mockKeyword = 'test';

			component.onSearch(mockKeyword);

			expect(component.keyword).toBe(mockKeyword);
			expect(component['getUserListPage']).toHaveBeenCalledWith(component.pageParams);
		});
	});

	describe('onSortChange', () => {
		it('should update sort parameters when sort direction is provided', () => {
			const sortEvent: Sort = { active: 'createdAt', direction: 'asc' };

			component.onSortChange(sortEvent);

			expect(component.pageParams.orderByColumn).toBe('createdAt');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should clear sort parameters when sort direction is empty', () => {
			// First set some values
			component.pageParams.orderByColumn = 'createdAt';
			component.pageParams.isAsc = 'asc';

			const sortEvent: Sort = { active: 'createdAt', direction: '' };

			component.onSortChange(sortEvent);

			expect(component.pageParams.orderByColumn).toBe('');
			expect(component.pageParams.isAsc).toBe('');
		});

		it('should update sort parameters when sort direction is desc', () => {
			spyOn<UserListPageComponent, any>(component, 'getUserListPage');
			const sortEvent: Sort = { active: 'updatedAt', direction: 'desc' };

			component.onSortChange(sortEvent);

			expect(component.pageParams.orderByColumn).toBe('updatedAt');
			expect(component.pageParams.isAsc).toBe('desc');
			// Verify getUserListPage is not called directly by onSortChange
			expect(component['getUserListPage']).not.toHaveBeenCalled();
		});

		it('should not call getUserListPage after updating sort parameters', () => {
			// Setup
			spyOn<UserListPageComponent, any>(component, 'getUserListPage');
			const sortEvent: Sort = { active: 'createdAt', direction: 'asc' };

			// Execute
			component.onSortChange(sortEvent);

			// Verify
			expect(component['getUserListPage']).not.toHaveBeenCalled();
			// Note: This test documents the current behavior. If the component is updated to call
			// getUserListPage after updating sort parameters, this test should be updated.
		});
	});

	describe('getUserListPage', () => {
		it('should set dataLoading to true and clear userList before making the request', () => {
			// Setup
			component.dataLoading = false;
			component.userList = [{} as any];

			// We need to modify the spy to capture the state before the observable completes
			userMgmtRequestServiceSpy.getUserList.and.callFake(() => {
				// Check state right after the method call but before observable completes
				expect(component.dataLoading).toBeTrue();
				expect(component.userList.length).toBe(0);
				return of(mockPaginationResponse);
			});

			// Execute
			component['getUserListPage']({ pageNum: 1, pageSize: 10 });

			// Verify
			expect(userMgmtRequestServiceSpy.getUserList).toHaveBeenCalled();
		});

		it('should retrieve a page of user list and update component properties on success', () => {
			// Execute
			component['getUserListPage']({ pageNum: 1, pageSize: 10 });

			// Verify
			expect(userMgmtRequestServiceSpy.getUserList).toHaveBeenCalledWith({ pageNum: 1, pageSize: 10 }, '');
			expect(component.userList).toEqual(mockSliRecords);
			expect(component.userListTotalRecords).toEqual(mockSliRecords.length);
			expect(component.dataLoading).toBeFalse();
		});

		it('should handle errors and display a snackbar notification', () => {
			const errorResponse = { message: 'something went wrong' };
			userMgmtRequestServiceSpy.getUserList.and.returnValue(throwError(() => errorResponse));

			component['getUserListPage']({ pageNum: 1, pageSize: 10 });

			expect(component.dataLoading).toBeFalse();
		});
	});
});
