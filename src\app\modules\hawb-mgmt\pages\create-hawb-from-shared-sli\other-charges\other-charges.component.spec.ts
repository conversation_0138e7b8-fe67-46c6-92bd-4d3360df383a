import { ComponentFixture, TestBed } from '@angular/core/testing';
import { OtherChargesComponent } from './other-charges.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';
import { Validators } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { CurrencyInputComponent } from '@shared/components/currency-input/currency-input.component';
import { MatDialog } from '@angular/material/dialog';
import { of } from 'rxjs';

describe('OtherChargesComponent', () => {
	let component: OtherChargesComponent;
	let fixture: ComponentFixture<OtherChargesComponent>;
	const mockCurrencies = ['USD', 'EUR', 'GBP'];
	let mockDialog: jasmine.SpyObj<MatDialog>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [OtherChargesComponent],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting(), provideTranslateService()],
		}).compileComponents();

		fixture = TestBed.createComponent(OtherChargesComponent);
		component = fixture.componentInstance;

		component.currencies = mockCurrencies;
		fixture.detectChanges();

		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
		mockDialog.open.and.returnValue({
			afterClosed: () => of(true),
		} as any);
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize form with required validators', () => {
		const form = component.otherChargesForm;
		expect(form.get('chargePaymentType')?.hasValidator(Validators.required)).toBeTruthy();
		expect(form.get('entitlement')?.hasValidator(Validators.required)).toBeTruthy();
		expect(form.get('otherChargeCode')?.hasValidator(Validators.required)).toBeTruthy();
		expect(form.get('otherChargeAmount')?.hasValidator(Validators.required)).toBeTruthy();
	});

	it('should mark form as touched when adding invalid entry', () => {
		const markAllAsTouchedSpy = spyOn(component.otherChargesForm, 'markAllAsTouched');

		component.addOtherCharges();

		expect(markAllAsTouchedSpy).toHaveBeenCalled();
		expect(mockDialog.open).not.toHaveBeenCalled();
		expect(component.otherChargesList.length).toBe(0);
	});

	it('should add valid entry to the list', () => {
		// Fill valid form data
		component.otherChargesForm.patchValue({
			chargePaymentType: 'PREPAID' as any,
			entitlement: 'CARRIER',
			otherChargeCode: 'ABC123',
			otherChargeAmount: {
				currencyUnit: 'USD',
				numericalValue: 100,
			},
		});

		component.addOtherCharges();

		expect(component.otherChargesList.length).toBe(1);
		expect(component.otherChargesList[0]).toEqual({
			chargePaymentType: 'PREPAID',
			entitlement: 'CARRIER',
			otherChargeCode: 'ABC123',
			otherChargeAmount: {
				currencyUnit: 'USD',
				numericalValue: 100,
			},
		});
	});

	it('should delete items from the list', () => {
		// Add sample items
		component.otherChargesList = [
			{
				chargePaymentType: 'TYPE1',
				entitlement: 'ENT1',
				otherChargeCode: 'CODE1',
				otherChargeAmount: { currencyUnit: 'USD', numericalValue: 100 },
			},
			{
				chargePaymentType: 'TYPE2',
				entitlement: 'ENT2',
				otherChargeCode: 'CODE2',
				otherChargeAmount: { currencyUnit: 'EUR', numericalValue: 200 },
			},
		];

		component.deleteOtherCharge(0);

		expect(component.otherChargesList.length).toBe(1);
		expect(component.otherChargesList[0].otherChargeCode).toBe('CODE2');
	});

	it('should handle currency input correctly', () => {
		const currencyInput = fixture.debugElement.query(By.directive(CurrencyInputComponent));
		const currencyComponent = currencyInput.componentInstance as CurrencyInputComponent;

		// Test currency input initialization
		expect(currencyComponent.currencies).toEqual(mockCurrencies);

		// Simulate currency selection
		currencyComponent.currencyForm.get('currencyUnit')?.setValue('EUR');
		fixture.detectChanges();

		expect(component.otherChargesForm.get('otherChargeAmount.currencyUnit')?.value).toBe('EUR');
	});

	it('should require all fields before adding', () => {
		// Test partial form completion
		const testCases = [
			{ field: 'chargePaymentType', value: 'PREPAID' },
			{ field: 'entitlement', value: 'CARRIER' },
			{ field: 'otherChargeCode', value: 'ABC123' },
			{ field: 'otherChargeAmount', value: { currencyUnit: 'USD', numericalValue: 100 } },
		];

		for (const testCase of testCases) {
			component.otherChargesForm.reset();
			component.otherChargesForm.patchValue({ [testCase.field]: testCase.value });

			component.addOtherCharges();

			expect(mockDialog.open).not.toHaveBeenCalled();
			expect(component.otherChargesList.length).toBe(0);
		}
	});

	it('should validate numericalValue in otherChargeAmount', () => {
		// Set valid form except numericalValue
		component.otherChargesForm.patchValue({
			chargePaymentType: 'PREPAID' as any,
			entitlement: 'CARRIER',
			otherChargeCode: 'ABC123',
			otherChargeAmount: {
				currencyUnit: 'USD',
				numericalValue: null, // Invalid
			},
		});

		component.addOtherCharges();

		expect(component.otherChargesForm.get('otherChargeAmount')?.invalid).toBeTruthy();
		expect(mockDialog.open).not.toHaveBeenCalled();
	});

	it('should add multiple entries correctly', () => {
		const entries = [
			{
				chargePaymentType: 'TYPE1',
				entitlement: 'ENT1',
				otherChargeCode: 'CODE1',
				otherChargeAmount: { currencyUnit: 'USD', numericalValue: 100 },
			},
			{
				chargePaymentType: 'TYPE2',
				entitlement: 'ENT2',
				otherChargeCode: 'CODE2',
				otherChargeAmount: { currencyUnit: 'EUR', numericalValue: 200 },
			},
		];

		for (const entry of entries) {
			component.otherChargesForm.patchValue(entry as any);
			component.addOtherCharges();
		}

		expect(component.otherChargesList.length).toBe(2);
		expect(component.otherChargesList).toEqual(entries);
	});

	it('should validate currencyUnit in otherChargeAmount', () => {
		// Set valid form except currencyUnit
		component.otherChargesForm.patchValue({
			chargePaymentType: 'PREPAID' as any,
			entitlement: 'CARRIER',
			otherChargeCode: 'ABC123',
			otherChargeAmount: {
				currencyUnit: '',
				numericalValue: null,
			},
		});

		component.addOtherCharges();

		expect(component.otherChargesForm.get('otherChargeAmount')?.invalid).toBeTrue();

		component.otherChargesForm.patchValue({
			otherChargeAmount: {
				currencyUnit: '',
				numericalValue: 100,
			},
		});

		expect(component.otherChargesForm.get('otherChargeAmount')?.invalid).toBeFalse();

		expect(mockDialog.open).not.toHaveBeenCalled();
	});
});
