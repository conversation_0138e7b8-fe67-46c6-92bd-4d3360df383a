import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MawbSearchComponent } from './mawb-search.component';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { provideTranslateService } from '@ngx-translate/core';
import { AutocompleteComponent } from '@shared/components/autocomplete/autocomplete.component';
import { QueryList } from '@angular/core';
import { SearchType } from '@shared/models/search-type.model';
import { MawbSearchRequestService } from '../../services/mawb-search-request.service';
import { DatePipe } from '@angular/common';

describe('MawbSearchComponent', () => {
	let component: MawbSearchComponent;
	let fixture: ComponentFixture<MawbSearchComponent>;
	let datePipe: DatePipe;

	const mockSearchService = {
		getOptions: jasmine.createSpy('getOptions'),
	};

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [MawbSearchComponent],
			providers: [
				{ provide: MawbSearchRequestService, useValue: mockSearchService },
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				provideTranslateService(),
				DatePipe,
			],
		}).compileComponents();

		fixture = TestBed.createComponent(MawbSearchComponent);
		component = fixture.componentInstance;
		datePipe = TestBed.inject(DatePipe);
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize form with empty values', () => {
		expect(component.mawbSearchForm.value).toEqual({
			goodsDescription: '',
			startDate: null,
			endDate: null,
		});
	});

	it('should handle different search types in selectedItems()', () => {
		const testItem = { code: 'TST', name: 'Test' };

		component.selectedItems([testItem], SearchType.AIRLINE_CODE);
		expect(component.selectedAirlines).toEqual([testItem]);

		component.selectedItems([testItem], SearchType.LATEST_STATUS);
		expect(component.selectedLatestStatus).toEqual([testItem]);

		component.selectedItems([testItem], SearchType.ORIGIN);
		expect(component.selectedDepartureLocations).toEqual([testItem]);

		component.selectedItems([testItem], SearchType.DESTINATION);
		expect(component.selectedArrivalLocations).toEqual([testItem]);

		component.selectedItems([testItem], SearchType.MAWB);
		expect(component.selectedMawbNumbers).toEqual([testItem]);
	});

	it('should emit complete search payload with transformed dates', () => {
		const emitSpy = spyOn(component.searchMawb, 'emit');
		const testDate = new Date('2023-01-15');

		component.mawbSearchForm.patchValue({
			goodsDescription: 'Electronics',
			startDate: testDate,
			endDate: testDate,
		});

		component.selectedAirlines = [{ code: 'AA', name: 'American Airlines' }];
		component.selectedDepartureLocations = [{ code: 'JFK', name: 'New York' }];

		component.onSearch();

		expect(emitSpy).toHaveBeenCalledWith({
			goodsDescription: 'Electronics',
			createDateStart: '2023-01-15',
			createDateEnd: '2023-01-15',
			airlineCodeList: ['American Airlines'],
			latestStatusList: [],
			departureLocationList: ['JFK'],
			arrivalLocationList: [],
			mawbNumberList: [],
		});
	});

	it('should reset form and clear all selections', () => {
		const event = new MouseEvent('click');
		spyOn(event, 'preventDefault');
		spyOn(event, 'stopPropagation');

		// Set initial values
		component.mawbSearchForm.patchValue({
			goodsDescription: 'Test',
			startDate: new Date(),
			endDate: new Date(),
		});
		component.selectedAirlines = [{ code: 'DL', name: 'Delta' }];

		// Mock autocomplete reset
		component.autocompleteList = {
			forEach: jasmine.createSpy('forEach'),
		} as unknown as QueryList<AutocompleteComponent<any>>;

		component.onReset(event);

		expect(event.preventDefault).toHaveBeenCalled();
		expect(event.stopPropagation).toHaveBeenCalled();
		expect(component.mawbSearchForm.value).toEqual({
			goodsDescription: null,
			startDate: null,
			endDate: null,
		});
		expect(component.selectedAirlines).toEqual([]);
		expect(component.autocompleteList.forEach).toHaveBeenCalled();
	});

	it('should handle null dates in search payload', () => {
		const emitSpy = spyOn(component.searchMawb, 'emit');

		component.onSearch();

		expect(emitSpy).toHaveBeenCalledWith(
			jasmine.objectContaining({
				createDateStart: null,
				createDateEnd: null,
			})
		);
	});

	it('should transform dates to correct format', () => {
		const testDate = new Date('2023-12-31');
		const formatted = datePipe.transform(testDate, 'yyyy-MM-dd');
		expect(formatted).toBe('2023-12-31');
	});
});
