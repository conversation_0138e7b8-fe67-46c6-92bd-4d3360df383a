import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterLink } from '@angular/router';
import { filter, distinctUntilChanged } from 'rxjs/operators';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

export interface Breadcrumb {
	label: string;
	url: string;
	icon?: string;
}

@Component({
	selector: 'iata-breadcrumb',
	templateUrl: './breadcrumb.component.html',
	styleUrls: ['./breadcrumb.component.scss'],
	imports: [CommonModule, RouterLink, TranslateModule, MatIconModule],
})
export class BreadcrumbComponent extends DestroyRefComponent implements OnInit {
	breadcrumbs: Breadcrumb[] = [];

	constructor(
		private readonly router: Router,
		private readonly activatedRoute: ActivatedRoute
	) {
		super();
	}

	ngOnInit(): void {
		this.router.events
			.pipe(
				filter((event) => event instanceof NavigationEnd),
				distinctUntilChanged()
			)
			.subscribe(() => {
				this.breadcrumbs = this.createBreadcrumbs(this.activatedRoute.root);
			});

		// Initialize breadcrumbs
		this.breadcrumbs = this.createBreadcrumbs(this.activatedRoute.root);
	}

	private createBreadcrumbs(route: ActivatedRoute, url = '', breadcrumbs: Breadcrumb[] = []): Breadcrumb[] {
		// Get the route children
		const children: ActivatedRoute[] = route.children;

		// Return if no children
		if (children.length === 0) {
			return breadcrumbs;
		}

		// Iterate over each child
		for (const child of children) {
			// Verify primary route
			if (child.outlet !== 'primary') {
				continue;
			}

			// Get the route's URL segment
			const routeURL: string = child.snapshot.url.map((segment) => segment.path).join('/');

			// Append route URL to URL
			const currentUrl = url.length > 0 ? `${url}/${routeURL}` : routeURL;

			// Add breadcrumb
			if (routeURL.length > 0) {
				const breadcrumbData = child.snapshot.data['breadcrumb'];

				if (breadcrumbData) {
					breadcrumbs.push({
						label: breadcrumbData.label ?? routeURL,
						url: currentUrl,
						icon: breadcrumbData.icon,
					});
				}
			}

			// Recursive
			return this.createBreadcrumbs(child, currentUrl, breadcrumbs);
		}

		return breadcrumbs;
	}
}
