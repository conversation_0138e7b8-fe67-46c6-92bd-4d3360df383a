import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormGroup, FormControl } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Router } from '@angular/router';
import { of } from 'rxjs';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';
import { DatePipe } from '@angular/common';
// eslint-disable-next-line @typescript-eslint/naming-convention
import CreateHawbFromSharedSliComponent from './create-hawb-from-shared-sli.component';
import { UserProfileService } from '@shared/services/user-profile.service';
import { SliCreateRequestService } from '../../../sli-mgmt/services/sli-create-request.service';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { NotificationService } from '@shared/services/notification.service';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { EmitCharges } from '../../models/other-charges.model';

describe('CreateHawbFromSharedSliComponent', () => {
	let component: CreateHawbFromSharedSliComponent;
	let fixture: ComponentFixture<CreateHawbFromSharedSliComponent>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;
	let sliCreateRequestServiceSpy: jasmine.SpyObj<SliCreateRequestService>;
	let hawbSearchRequestServiceSpy: jasmine.SpyObj<HawbSearchRequestService>;
	let orgMgmtRequestServiceSpy: jasmine.SpyObj<OrgMgmtRequestService>;
	let notificationServiceSpy: jasmine.SpyObj<NotificationService>;
	let routerSpy: jasmine.SpyObj<Router>;
	let datePipeSpy: jasmine.SpyObj<DatePipe>;

	// Global getCurrentUser spy to prevent undefined subscription errors
	let getCurrentUserSpy: jasmine.Spy;

	beforeEach(async () => {
		// Create spies
		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		sliCreateRequestServiceSpy = jasmine.createSpyObj('SliCreateRequestService', [
			'getCurrencies',
			'getSliDetail',
			'getAirports',
			'getCountries',
			'getCurrencies',
		]);
		hawbSearchRequestServiceSpy = jasmine.createSpyObj('HawbSearchRequestService', ['getHawbDetail', 'createHawb', 'updateHawb']);
		orgMgmtRequestServiceSpy = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgInfo']);
		notificationServiceSpy = jasmine.createSpyObj('NotificationService', ['showSuccess', 'showError']);
		routerSpy = jasmine.createSpyObj('Router', ['navigate']);

		datePipeSpy = jasmine.createSpyObj('DatePipe', ['transform']);

		// Setup default spy returns
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));
		sliCreateRequestServiceSpy.getCurrencies.and.returnValue(of(['USD', 'EUR', 'GBP']));
		sliCreateRequestServiceSpy.getAirports.and.returnValue(
			of([
				{ code: 'LAX', name: 'Los Angeles' },
				{ code: 'JFK', name: 'New York' },
				{ code: 'LHR', name: 'London' },
			] as any)
		);
		sliCreateRequestServiceSpy.getCountries.and.returnValue(
			of([
				{ code: 'US', name: 'United States' },
				{ code: 'UK', name: 'United Kingdom' },
				{ code: 'CA', name: 'Canada' },
			] as any)
		);
		sliCreateRequestServiceSpy.getSliDetail.and.returnValue(
			of({
				shipmentParty: [],
				departureLocation: 'LAX',
				arrivalLocation: 'JFK',
				textualHandlingInstructions: 'Handle with care',
				pieces: [],
				totalGrossWeight: 100,
				rateCharge: {
					currencyUnit: 'USD',
					numericalValue: 50,
				},
				waybillPrefix: 'TEST',
				waybillNumber: '123456',
				accountingInformation: 'Test accounting',
				rateClassCode: 'N',
				totalVolumetricWeight: 80,
				goodsDescriptionForRate: 'Test goods',
				carrierDeclarationDate: new Date().toISOString(),
				carrierDeclarationPlace: 'Test place',
				consignorDeclarationSignature: 'Test signature',
				carrierDeclarationSignature: 'Carrier signature',
			} as any)
		);
		hawbSearchRequestServiceSpy.getHawbDetail.and.returnValue(
			of({
				shipmentParty: [],
				sliPartyList: [],
				partyList: [],
				departureLocation: 'LAX',
				arrivalLocation: 'JFK',
				waybillPrefix: 'AWB',
				waybillNumber: '12345',
				sliId: 'test-sli-id',
				orgId: 'test-org-id',
			} as any)
		);
		hawbSearchRequestServiceSpy.createHawb.and.returnValue(of('success'));
		hawbSearchRequestServiceSpy.updateHawb.and.returnValue(of('success'));
		orgMgmtRequestServiceSpy.getOrgInfo.and.returnValue(
			of({
				id: 'test-org',
				companyName: 'Test Organization',
				partyRole: 'AIRLINE',
				countryCode: 'US',
				locationName: '123 Test St',
				regionCode: 'CA',
				textualPostCode: '90210',
				cityCode: 'LA',
				persons: [{ phoneNumber: '************', emailAddress: '<EMAIL>' }],
				iataCargoAgentCode: 'TEST123',
			} as any)
		);
		datePipeSpy.transform.and.returnValue('2023-01-01 10:00:00');

		await TestBed.configureTestingModule({
			imports: [
				CreateHawbFromSharedSliComponent,
				ReactiveFormsModule,
				MatDialogModule,
				MatFormFieldModule,
				MatInputModule,
				MatSelectModule,
				MatButtonModule,
				MatDatepickerModule,
				MatNativeDateModule,
				BrowserAnimationsModule,
				TranslateModule.forRoot(),
			],
			providers: [
				provideHttpClient(),
				provideHttpClientTesting(),
				{ provide: UserProfileService, useValue: userProfileServiceSpy },
				{ provide: SliCreateRequestService, useValue: sliCreateRequestServiceSpy },
				{ provide: HawbSearchRequestService, useValue: hawbSearchRequestServiceSpy },
				{ provide: OrgMgmtRequestService, useValue: orgMgmtRequestServiceSpy },
				{ provide: NotificationService, useValue: notificationServiceSpy },
				{ provide: Router, useValue: routerSpy },
				{ provide: DatePipe, useValue: datePipeSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(CreateHawbFromSharedSliComponent);
		component = fixture.componentInstance;

		// Set up global getCurrentUser spy to prevent subscription errors
		getCurrentUserSpy = spyOn(component, 'getCurrentUser').and.returnValue(of(null));

		// Mock child components with proper FormGroup instances to prevent runtime errors
		const mockCarrierAgentForm = new FormGroup({
			carrierCode: new FormControl(''),
			carrierName: new FormControl(''),
			company: new FormControl(''),
			agentIataCode: new FormControl(''),
			country: new FormControl(''),
		});
		spyOn(mockCarrierAgentForm, 'patchValue').and.callThrough();

		const mockAirportInfoForm = new FormGroup({
			origin: new FormControl(''),
			destination: new FormControl(''),
			departureAndRequestedRouting: new FormControl(''),
			airportOfDestination: new FormControl(''),
			amountOfInsurance: new FormControl(''),
			wtOrVal: new FormControl(''),
			declaredValueForCarriage: new FormGroup({
				currencyUnit: new FormControl('USD'),
				numericalValue: new FormControl('200'),
			}),
			declaredValueForCustoms: new FormGroup({
				currencyUnit: new FormControl('USD'),
				numericalValue: new FormControl('100'),
			}),
		});
		spyOn(mockAirportInfoForm, 'patchValue').and.callThrough();

		const mockOtherChargesForm = new FormGroup({
			charges: new FormControl(''),
		});
		spyOn(mockOtherChargesForm, 'patchValue').and.callThrough();

		const mockPrepaidForm = new FormGroup({
			prepaid: new FormControl(''),
			weightChargePrepaid: new FormControl(''),
			weightChargeCollect: new FormControl(''),
			valuationChargePrepaid: new FormControl(''),
			valuationChargeCollect: new FormControl(''),
			taxPrepaid: new FormControl(''),
			taxCollect: new FormControl(''),
		});
		spyOn(mockPrepaidForm, 'patchValue').and.callThrough();

		const mockCollectForm = new FormGroup({
			collect: new FormControl(''),
		});
		spyOn(mockCollectForm, 'patchValue').and.callThrough();

		component.carrierAgentComponent = {
			carrierAgentForm: mockCarrierAgentForm,
		} as any;
		component.airPortInfoComponent = {
			airportInfoForm: mockAirportInfoForm,
		} as any;
		component.otherChargesComponent = {
			otherChargesForm: mockOtherChargesForm,
			otherChargesList: [], // Add empty array to prevent map error
		} as any;
		component.prepaidCollectComponent = {
			prepaidForm: mockPrepaidForm,
			collectForm: mockCollectForm,
		} as any;
	});

	afterEach(() => {
		// Prevent Observable subscription errors by ensuring proper cleanup
		// Reset all service spies to return safe values before destroying fixture
		if (sliCreateRequestServiceSpy?.getSliDetail) {
			sliCreateRequestServiceSpy.getSliDetail.and.returnValue(
				of({
					shipmentParty: [],
					departureLocation: 'LAX',
					arrivalLocation: 'JFK',
					textualHandlingInstructions: 'Handle with care',
					pieces: [],
					totalGrossWeight: 100,
					rateCharge: {
						currencyUnit: 'USD',
						numericalValue: 50,
					},
					waybillPrefix: 'TEST',
					waybillNumber: '123456',
					accountingInformation: 'Test accounting',
					rateClassCode: 'N',
					totalVolumetricWeight: 80,
					goodsDescriptionForRate: 'Test goods',
					carrierDeclarationDate: new Date().toISOString(),
					carrierDeclarationPlace: 'Test place',
					consignorDeclarationSignature: 'Test signature',
					carrierDeclarationSignature: 'Carrier signature',
				} as any)
			);
		}
		if (hawbSearchRequestServiceSpy?.getHawbDetail) {
			hawbSearchRequestServiceSpy.getHawbDetail.and.returnValue(
				of({
					shipmentParty: [],
					sliPartyList: [],
					partyList: [],
					departureLocation: 'LAX',
					arrivalLocation: 'JFK',
					waybillPrefix: 'AWB',
					waybillNumber: '12345',
					sliId: 'test-sli-id',
					orgId: 'test-org-id',
					rateCharge: {
						currencyUnit: 'USD',
						numericalValue: 50,
					},
					totalGrossWeight: 100,
					rateClassCode: 'N',
					totalVolumetricWeight: 80,
					goodsDescriptionForRate: 'Test goods',
					carrierDeclarationDate: new Date().toISOString(),
					carrierDeclarationPlace: 'Test place',
					consignorDeclarationSignature: 'Test signature',
					carrierDeclarationSignature: 'Carrier signature',
				} as any)
			);
		}
		fixture.destroy();
	});

	afterAll(() => {
		// Final cleanup to prevent afterAll Observable errors
		// Reset service spies to return safe default values instead of EMPTY to prevent subscription errors
		if (sliCreateRequestServiceSpy?.getSliDetail) {
			sliCreateRequestServiceSpy.getSliDetail.and.returnValue(
				of({
					shipmentParty: [],
					departureLocation: 'LAX',
					arrivalLocation: 'JFK',
					textualHandlingInstructions: 'Handle with care',
					pieces: [],
					totalGrossWeight: 100,
					rateCharge: {
						currencyUnit: 'USD',
						numericalValue: 50,
					},
					waybillPrefix: 'TEST',
					waybillNumber: '123456',
					accountingInformation: 'Test accounting',
					rateClassCode: 'N',
					totalVolumetricWeight: 80,
					goodsDescriptionForRate: 'Test goods',
					carrierDeclarationDate: new Date().toISOString(),
					carrierDeclarationPlace: 'Test place',
					consignorDeclarationSignature: 'Test signature',
					carrierDeclarationSignature: 'Carrier signature',
				} as any)
			);
		}
		if (sliCreateRequestServiceSpy?.getCurrencies) {
			sliCreateRequestServiceSpy.getCurrencies.and.returnValue(of(['USD', 'EUR', 'GBP']));
		}
		if (hawbSearchRequestServiceSpy?.getHawbDetail) {
			hawbSearchRequestServiceSpy.getHawbDetail.and.returnValue(
				of({
					shipmentParty: [],
					sliPartyList: [],
					partyList: [],
					departureLocation: 'LAX',
					arrivalLocation: 'JFK',
					waybillPrefix: 'AWB',
					waybillNumber: '12345',
					sliId: 'test-sli-id',
					orgId: 'test-org-id',
					rateCharge: {
						currencyUnit: 'USD',
						numericalValue: 50,
					},
					totalGrossWeight: 100,
					rateClassCode: 'N',
					totalVolumetricWeight: 80,
					goodsDescriptionForRate: 'Test goods',
					carrierDeclarationDate: new Date().toISOString(),
					carrierDeclarationPlace: 'Test place',
					consignorDeclarationSignature: 'Test signature',
					carrierDeclarationSignature: 'Carrier signature',
				} as any)
			);
		}
		if (orgMgmtRequestServiceSpy?.getOrgInfo) {
			orgMgmtRequestServiceSpy.getOrgInfo.and.returnValue(
				of({
					id: 'test-org',
					companyName: 'Test Organization',
					partyRole: 'AIRLINE',
					countryCode: 'US',
					locationName: '123 Test St',
					regionCode: 'CA',
					textualPostCode: '90210',
					cityCode: 'LA',
					persons: [{ phoneNumber: '************', emailAddress: '<EMAIL>' }],
					iataCargoAgentCode: 'TEST123',
				} as any)
			);
		}
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize form on ngOnInit', () => {
		expect(component.hawbForm).toBeDefined();
		expect(component.hawbForm.get('hawbPrefix')).toBeTruthy();
		expect(component.hawbForm.get('hawbNumber')).toBeTruthy();
		expect(component.hawbForm.get('chargeableWeight')).toBeTruthy();
		expect(component.hawbForm.get('rateCharge')).toBeTruthy();
	});

	it('should call getCurrencies on ngOnInit', () => {
		component.ngOnInit();
		expect(sliCreateRequestServiceSpy.getCurrencies).toHaveBeenCalled();
	});

	it('should handle sliNumber input', () => {
		component.sliNumber = 'SLI-123';
		component.ngOnInit();
		expect(sliCreateRequestServiceSpy.getSliDetail).toHaveBeenCalledWith('SLI-123');
	});

	it('should handle hawbId input', () => {
		component.hawbId = 'HAWB-456';
		component.ngOnInit();
		expect(hawbSearchRequestServiceSpy.getHawbDetail).toHaveBeenCalledWith('HAWB-456');
	});

	it('should call getOrgInfo when getCurrentUser returns valid orgId', () => {
		// Set sliNumber so that getCurrentUser is called
		component.sliNumber = 'SLI-123';

		// Override the global spy for this specific test
		getCurrentUserSpy.and.returnValue(
			of({
				primaryOrgId: 'test-org-id',
				userId: 'user123',
				email: '<EMAIL>',
				firstName: 'Test',
				lastName: 'User',
			} as any)
		);

		component.ngOnInit();

		expect(orgMgmtRequestServiceSpy.getOrgInfo).toHaveBeenCalledWith('test-org-id');
	});

	it('should handle form validation correctly', () => {
		component.hawbForm.patchValue({
			hawbPrefix: '',
			hawbNumber: '',
			grossWeight: null,
			chargeableWeight: null,
			rateCharge: { currencyUnit: '', numericalValue: null },
			natureAndQuantityOfGoods: '',
			date: null,
			atPlace: '',
			signatureOfShipperOrHisAgent: '',
			signatureOfCarrierOrItsAgent: '',
		});

		expect(component.hawbForm.invalid).toBeTruthy();
		expect(component.hawbForm.get('hawbPrefix')?.invalid).toBeTruthy();
		expect(component.hawbForm.get('hawbNumber')?.invalid).toBeTruthy();
		expect(component.hawbForm.get('grossWeight')?.invalid).toBeTruthy();
		expect(component.hawbForm.get('chargeableWeight')?.invalid).toBeTruthy();
		expect(component.hawbForm.get('rateCharge')?.invalid).toBeTruthy();
	});

	it('should validate grossWeight pattern correctly', () => {
		const grossWeightControl = component.hawbForm.get('grossWeight');

		// Valid values
		grossWeightControl?.setValue(100);
		expect(grossWeightControl?.valid).toBeTruthy();

		grossWeightControl?.setValue(100.5);
		expect(grossWeightControl?.valid).toBeTruthy();

		// Invalid values
		grossWeightControl?.setValue(100.25);
		expect(grossWeightControl?.valid).toBeFalsy();
	});

	it('should validate chargeableWeight pattern correctly', () => {
		const chargeableWeightControl = component.hawbForm.get('chargeableWeight');

		// Valid values
		chargeableWeightControl?.setValue(100);
		expect(chargeableWeightControl?.valid).toBeTruthy();

		chargeableWeightControl?.setValue(100.5);
		expect(chargeableWeightControl?.valid).toBeTruthy();

		// Invalid values
		chargeableWeightControl?.setValue(100.25);
		expect(chargeableWeightControl?.valid).toBeFalsy();
	});

	it('should validate rateCharge numericalValue pattern correctly', () => {
		const rateChargeControl = component.hawbForm.get('rateCharge')?.get('numericalValue');

		// Valid values
		rateChargeControl?.setValue(100);
		expect(rateChargeControl?.valid).toBeTruthy();

		rateChargeControl?.setValue(100.5);
		expect(rateChargeControl?.valid).toBeTruthy();

		rateChargeControl?.setValue(100.5);
		expect(rateChargeControl?.valid).toBeTruthy();
	});

	describe('onWtOrValChange', () => {
		it('should update prepaid form when wtOrVal is PREPAID', () => {
			const mockEvent = { value: DropDownType.PREPAID } as any;
			component.hawbForm.patchValue({ total: 150 });
			component.airPortInfoComponent.airportInfoForm.get('declaredValueForCarriage')?.patchValue({
				currencyUnit: 'USD',
				numericalValue: '200',
			});

			component.onWtOrValChange(mockEvent);

			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				weightChargePrepaid: 150,
				weightChargeCollect: null,
				valuationChargePrepaid: 200,
				valuationChargeCollect: null,
			});
		});

		it('should update collect form when wtOrVal is COLLECT', () => {
			const mockEvent = { value: DropDownType.COLLECT } as any;
			component.hawbForm.patchValue({ total: 150 });
			component.airPortInfoComponent.airportInfoForm.get('declaredValueForCarriage')?.patchValue({
				currencyUnit: 'USD',
				numericalValue: '200',
			});

			component.onWtOrValChange(mockEvent);

			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				weightChargePrepaid: null,
				weightChargeCollect: 150,
				valuationChargePrepaid: null,
				valuationChargeCollect: 200,
			});
		});
	});

	describe('onOtherChargesChange', () => {
		it('should handle prepaid charges correctly', () => {
			const mockCharges: EmitCharges[] = [
				{
					chargePaymentType: DropDownType.PREPAID,
					taxCharges: 10,
					agentCharges: 20,
					carrierCharges: 30,
				},
			];

			component.onOtherChargesChange(mockCharges);

			// The method makes multiple calls: first clears collect fields (since no collect charges), then sets prepaid fields
			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				taxCollect: null,
				totalOtherChargesDueAgentCollect: null,
				totalOtherChargesDueCarrierCollect: null,
			});
			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				taxPrepaid: 10,
				totalOtherChargesDueAgentPrepaid: 20,
				totalOtherChargesDueCarrierPrepaid: 30,
			});
		});

		it('should handle collect charges correctly', () => {
			const mockCharges: EmitCharges[] = [
				{
					chargePaymentType: DropDownType.COLLECT,
					taxCharges: 15,
					agentCharges: 25,
					carrierCharges: 35,
				},
			];

			component.onOtherChargesChange(mockCharges);

			// The method makes multiple calls: first clears prepaid fields (since no prepaid charges), then sets collect fields
			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				taxPrepaid: null,
				totalOtherChargesDueAgentPrepaid: null,
				totalOtherChargesDueCarrierPrepaid: null,
			});
			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				taxCollect: 15,
				totalOtherChargesDueAgentCollect: 25,
				totalOtherChargesDueCarrierCollect: 35,
			});
		});

		it('should clear prepaid values when no prepaid charges', () => {
			const mockCharges = [{ chargePaymentType: 'COLLECT', taxCharges: 15, agentCharges: 25, carrierCharges: 35 }];

			component.onOtherChargesChange(mockCharges as any);

			expect(component.prepaidCollectComponent.prepaidForm.patchValue).toHaveBeenCalledWith({
				taxPrepaid: null,
				totalOtherChargesDueAgentPrepaid: null,
				totalOtherChargesDueCarrierPrepaid: null,
			});
		});
	});

	describe('alsoNotifies management', () => {
		beforeEach(() => {
			component.alsoNotifies = [{ companyName: 'Company 1' } as any, { companyName: 'Company 2' } as any];
		});

		it('should delete alsoNotify item at specified index', () => {
			const mockEvent = { preventDefault: jasmine.createSpy(), stopPropagation: jasmine.createSpy() } as any;

			component.delAlsoNotify(0, mockEvent);

			expect(mockEvent.preventDefault).toHaveBeenCalled();
			expect(mockEvent.stopPropagation).toHaveBeenCalled();
			expect(component.alsoNotifies.length).toBe(1);
			expect(component.alsoNotifies[0].companyName).toBe('Company 2');
		});

		it('should add new alsoNotify item', () => {
			const initialLength = component.alsoNotifies.length;

			component.addAlsoNotify();

			expect(component.alsoNotifies.length).toBe(initialLength + 1);
			expect(component.alsoNotifies[component.alsoNotifies.length - 1]).toEqual(
				jasmine.objectContaining({
					companyName: '',
					contactName: '',
					countryCode: '',
					regionCode: '',
					cityCode: '',
					locationName: '',
					textualPostCode: '',
					phoneNumber: '',
					emailAddress: '',
				})
			);
		});
	});

	describe('onCancel', () => {
		it('should open confirm dialog and navigate on confirmation', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(true));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			component.onCancel();

			expect(component['dialog'].open).toHaveBeenCalled();
			expect(routerSpy.navigate).toHaveBeenCalledWith(['/hawb/create']);
			expect(component.isConfirmed).toBeTruthy();
		});

		it('should not navigate when dialog is cancelled', () => {
			const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
			dialogRefSpy.afterClosed.and.returnValue(of(false));
			spyOn(component['dialog'], 'open').and.returnValue(dialogRefSpy);

			component.onCancel();

			expect(component['dialog'].open).toHaveBeenCalled();
			expect(routerSpy.navigate).not.toHaveBeenCalled();
			expect(component.isConfirmed).toBeFalsy();
		});
	});

	describe('markFormGroupTouched', () => {
		it('should mark all form controls as touched', () => {
			const formGroup = new FormGroup({
				control1: new FormControl(''),
				control2: new FormControl(''),
				nestedGroup: new FormGroup({
					nestedControl: new FormControl(''),
				}),
			});

			spyOn(formGroup.get('control1')!, 'markAsTouched');
			spyOn(formGroup.get('control2')!, 'markAsTouched');
			spyOn(formGroup.get('nestedGroup')!, 'markAsTouched');

			(component as any).markFormGroupTouched(formGroup);

			expect(formGroup.get('control1')!.markAsTouched).toHaveBeenCalled();
			expect(formGroup.get('control2')!.markAsTouched).toHaveBeenCalled();
			expect(formGroup.get('nestedGroup')!.markAsTouched).toHaveBeenCalled();
		});
	});

	describe('onSave', () => {
		beforeEach(() => {
			// Setup valid form data
			component.hawbForm.patchValue({
				hawbPrefix: 'ABC',
				hawbNumber: '123456',
				grossWeight: 100,
				chargeableWeight: 100,
				rateCharge: { currencyUnit: 'USD', numericalValue: 50 },
				natureAndQuantityOfGoods: 'Test goods',
				date: new Date(),
				atPlace: 'Test place',
				signatureOfShipperOrHisAgent: 'Shipper signature',
				signatureOfCarrierOrItsAgent: 'Carrier signature',
			});

			// Mock child component forms as valid
			component.airPortInfoComponent.airportInfoForm.setErrors(null);
			component.carrierAgentComponent.carrierAgentForm.setErrors(null);
			component.otherChargesComponent.otherChargesForm.setErrors(null);
			component.prepaidCollectComponent.prepaidForm.setErrors(null);

			// Mock child component form values
			component.airPortInfoComponent.airportInfoForm.patchValue({
				departureAndRequestedRouting: 'JFK',
				airportOfDestination: 'LAX',
				declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: '1000' },
				declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: '1000' },
				wtOrVal: 'PREPAID',
			});

			component.carrierAgentComponent.carrierAgentForm.patchValue({
				company: 'Test Carrier',
				agentIataCode: 'TC123',
				country: 'US',
				province: 'NY',
				cityName: 'New York',
				address: '123 Test St',
				textualPostCode: '10001',
			});
		});

		it('should show error when hawbForm is invalid', () => {
			component.hawbForm.patchValue({ hawbPrefix: '' }); // Make form invalid
			spyOn(component['dialog'], 'open');

			component.onSave();

			expect(component['dialog'].open).toHaveBeenCalled();
		});

		it('should show error when airportInfoForm is invalid', () => {
			component.airPortInfoComponent.airportInfoForm.setErrors({ invalid: true });
			spyOn(component['dialog'], 'open');

			component.onSave();

			expect(component['dialog'].open).toHaveBeenCalled();
		});

		it('should show error when carrierAgentForm is invalid', () => {
			component.carrierAgentComponent.carrierAgentForm.setErrors({ invalid: true });
			spyOn(component['dialog'], 'open');

			component.onSave();

			expect(component['dialog'].open).toHaveBeenCalled();
		});

		it('should create HAWB when sliNumber is provided and hawbId is not', () => {
			component.sliNumber = 'SLI-123';
			component.hawbId = undefined;
			hawbSearchRequestServiceSpy.createHawb.and.returnValue(of({}));

			component.onSave();

			expect(hawbSearchRequestServiceSpy.createHawb).toHaveBeenCalledWith('SLI-123', jasmine.any(Object));
			expect(notificationServiceSpy.showSuccess).toHaveBeenCalled();
			expect(routerSpy.navigate).toHaveBeenCalledWith(['/hawb']);
		});

		it('should update HAWB when hawbId, sliId, and orgId are provided', () => {
			component.hawbId = 'HAWB-456';
			component['sliId'] = 'SLI-789';
			component['orgId'] = 'ORG-101';
			hawbSearchRequestServiceSpy.updateHawb.and.returnValue(of({}));

			component.onSave();

			expect(hawbSearchRequestServiceSpy.updateHawb).toHaveBeenCalledWith('HAWB-456', 'SLI-789', 'ORG-101', jasmine.any(Object));
			expect(notificationServiceSpy.showSuccess).toHaveBeenCalled();
			expect(routerSpy.navigate).toHaveBeenCalledWith(['/hawb']);
		});
	});

	describe('ngAfterViewInit', () => {
		it('should setup form value change subscriptions', () => {
			spyOn(component.hawbForm.get('total')!.valueChanges, 'pipe').and.returnValue(of(100));
			spyOn(
				component.airPortInfoComponent.airportInfoForm.get('declaredValueForCarriage')!.get('numericalValue')!.valueChanges,
				'pipe'
			).and.returnValue(of('50'));

			component.ngAfterViewInit();

			expect(component.hawbForm.get('total')!.valueChanges.pipe).toHaveBeenCalled();
			expect(
				component.airPortInfoComponent.airportInfoForm.get('declaredValueForCarriage')!.get('numericalValue')!.valueChanges.pipe
			).toHaveBeenCalled();
		});
	});

	describe('Error handling', () => {
		it('should handle service errors gracefully', () => {
			sliCreateRequestServiceSpy.getCurrencies.and.returnValue(of([]));
			component.sliNumber = 'SLI-123';
			sliCreateRequestServiceSpy.getSliDetail.and.returnValue(
				of({
					shipmentParty: [],
					rateCharge: {
						currencyUnit: 'USD',
						numericalValue: 0,
					},
					waybillPrefix: '',
					waybillNumber: '',
					accountingInformation: '',
					rateClassCode: '',
					totalVolumetricWeight: 0,
					goodsDescriptionForRate: '',
					carrierDeclarationDate: new Date().toISOString(),
					carrierDeclarationPlace: '',
					consignorDeclarationSignature: '',
					carrierDeclarationSignature: '',
				} as any)
			);

			expect(() => component.ngOnInit()).not.toThrow();
		});

		it('should handle empty service responses', () => {
			sliCreateRequestServiceSpy.getCurrencies.and.returnValue(of([]));

			component.ngOnInit();

			expect(component.currencies).toEqual(['']);
		});
	});
});
