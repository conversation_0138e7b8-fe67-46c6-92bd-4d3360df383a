@use 'utils';

.iata-main-footer {
	margin-top: 50px;
	padding: 50px;
	color: var(--iata-white);
	background-color: var(--iata-grey-800);
	min-width: utils.$device-breakpoint-large;

	&__content-container {
		text-align: center;
	}

	&__title {
		font-size: 22px;
		font-weight: 400;
		line-height: 32px;
		margin: 0;
	}

	&__logo {
		width: 100px;
	}

	&__links-container {
		margin-top: 30px;
	}

	&__link {
		color: var(--iata-white);
		font-size: 14px;
		line-height: 20px;
		text-decoration: none;
		padding: 0 10px;
		border-right: 2px solid rgba(#fff, 0.1);

		&:last-of-type {
			border-right: none;
		}

		&:hover {
			text-decoration: underline;
		}
	}

	&__social-links-container {
		margin-top: 30px;
	}

	&__social-link {
		padding: 0 10px;
	}

	&__lang-container {
		margin-top: 30px;
	}

	&__lang-link {
		color: var(--iata-white);
		font-size: 14px;
		font-weight: 500;
		line-height: 20px;
		text-decoration: none;
		text-transform: uppercase;
		padding: 0 10px;
		border-right: 2px solid rgba(#fff, 0.1);
		user-select: none;
		cursor: pointer;

		&:last-of-type {
			border-right: none;
		}

		&:hover,
		&.active-lang {
			text-decoration: underline;
		}

		&.active-lang {
			text-decoration: underline;
			pointer-events: none;
		}
	}
}

@include utils.respondAfter(utils.$device-breakpoint-large) {
	.iata-main-footer {
		&__content-container {
			width: 800px;
			margin: 0 auto;
		}
	}
}
