<mat-form-field subscriptSizing="dynamic" appearance="outline" floatLabel="always" class="width-100">
	<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
	<input type="text" matInput
		#inputElement
		[ngModel]="enumCode"
		(ngModelChange)="onInputChange($event)"
		[disabled]="disabled"
		[matAutocomplete]="autoEnumSelect"
		(focusout)="onTouched()">
	<mat-autocomplete #autoEnumSelect="matAutocomplete"
		[displayWith]="displayEnumName">
		@for (enum of filteredEnumCodes; track $index) {
			<mat-option [value]="enum.code">
				{{ enum.name }}
			</mat-option>
		}
	</mat-autocomplete>
</mat-form-field>
