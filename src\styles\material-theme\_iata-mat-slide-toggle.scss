@mixin iata-slide-toggle-theme($theme) {
	html .mat-mdc-slide-toggle {
		--iata-toggle-unselected-fill-color: #E6E0E9;
		--iata-toggle-unselected-stroke-color: #79747E;
		--iata-toggle-handle-size: 12px;

		--mat-checkbox-label-text-color: var(--iata-grey-primary);
		--mdc-switch-handle-height: var(--iata-toggle-handle-size);
		--mdc-switch-handle-width: var(--iata-toggle-handle-size);
		--mat-switch-with-icon-handle-size: var(--iata-toggle-handle-size);
		--mat-switch-pressed-handle-size: var(--iata-toggle-handle-size);
		--mdc-switch-selected-icon-size: 0;
		--mdc-switch-track-height: 22px;
		--mdc-switch-track-shape: 100px;
		--mdc-switch-track-width: 40px;
		--mdc-switch-unselected-icon-size: 0;

		--mdc-switch-selected-handle-color: var(--iata-white);
		--mdc-switch-selected-focus-handle-color: var(--iata-white);
		--mdc-switch-selected-hover-handle-color: var(--iata-white);
		--mdc-switch-selected-pressed-handle-color: var(--iata-white);
		--mdc-switch-selected-focus-track-color: var(--iata-blue-primary);
		--mdc-switch-selected-hover-track-color: var(--iata-blue-primary);
		--mdc-switch-selected-pressed-track-color: var(--iata-blue-primary);
		--mdc-switch-selected-track-color: var(--iata-blue-primary);

		--mdc-switch-unselected-focus-handle-color: var(--iata-toggle-unselected-stroke-color);
		--mdc-switch-unselected-focus-track-color: var(--iata-toggle-unselected-fill-color);
		--mdc-switch-unselected-handle-color: var(--iata-toggle-unselected-stroke-color);
		--mdc-switch-unselected-hover-handle-color: var(--iata-toggle-unselected-stroke-color);
		--mdc-switch-unselected-hover-track-color: var(--iata-toggle-unselected-fill-color);
		--mdc-switch-unselected-pressed-handle-color: var(--iata-toggle-unselected-stroke-color);
		--mdc-switch-unselected-pressed-track-color: var(--iata-toggle-unselected-fill-color);
		--mdc-switch-unselected-track-color: var(--iata-toggle-unselected-fill-color);

		.mdc-switch__ripple {
			display: none;
		}

		.mdc-switch__track {
			border: 2px solid var(--iata-toggle-unselected-stroke-color);
		}

		.mdc-switch__handle-track {
			transform: translateX(18%);
		}

		.mdc-switch--selected {
			.mdc-switch__track {
				border: 2px solid var(--iata-blue-primary);
			}

			.mdc-switch__handle-track {
				transform: translateX(84%);
			}
		}

		&[readonly="true"] {
			pointer-events: none;
			cursor: not-allowed;

			.mdc-switch__handle {
				pointer-events: none;
				cursor: not-allowed;
			}
		}

		&.iata-slide-toggle--small {
			--iata-toggle-handle-size: 8px;
			--mdc-switch-track-height: 12px;
			--mdc-switch-track-width: 25px;
		}
	}
}
