import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { ChangeDetectorRef } from '@angular/core';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { noop, Observable, of } from 'rxjs';
import { AutocompleteComponent } from './autocomplete.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { AbstractAutocompleteService } from '@shared/models/autocomplete.model';

class MockAutocompleteService {
	// eslint-disable-next-line
	getOptions(query: string | object): Observable<any[]> {
		return of([
			{ code: 1, name: 'Option 1' },
			{ code: 2, name: 'Option 2' },
		]);
	}
}

describe('AutocompleteComponent', () => {
	let component: AutocompleteComponent<any>;
	let fixture: ComponentFixture<AutocompleteComponent<any>>;
	let mockService: MockAutocompleteService;

	beforeEach(() => {
		mockService = new MockAutocompleteService();

		TestBed.configureTestingModule({
			imports: [ReactiveFormsModule, MatAutocompleteModule, AutocompleteComponent, NoopAnimationsModule],
			providers: [ChangeDetectorRef],
		}).compileComponents();

		fixture = TestBed.createComponent(AutocompleteComponent);
		component = fixture.componentInstance;
		component.api = mockService as AbstractAutocompleteService<any>;
		fixture.detectChanges();
	});

	it('should create the component', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize with default values', () => {
		expect(component.minLength).toBe(3);
		expect(component.label).toBe('');
		expect(component.isReadonly).toBe(false);
		expect(component.selection).toBeDefined();
	});

	it('#writeValue should call writeValue and set value', () => {
		component.writeValue('test');
		expect(component.selection.value).toBe('test');
	});

	it('#onChange should register onChange function', () => {
		const fn = jasmine.createSpy('onChange');
		component.registerOnChange(fn);
		component.onChange('test');
		expect(fn).toHaveBeenCalledWith('test');
	});

	it('#onTouched should register onTouched function', () => {
		const fn = jasmine.createSpy('onTouched');
		component.registerOnTouched(fn);
		component.onTouched();
		expect(fn).toHaveBeenCalled();
	});

	it('#setDisabledState should set disabled state', () => {
		component.setDisabledState(true);
		expect(component.disableControl).toBeTrue();
		component.setDisabledState(false);
		expect(component.disableControl).toBeFalse();
	});

	it('#setSelectionValue should filter and debounce value changes', fakeAsync(() => {
		const spy = spyOn(mockService, 'getOptions').and.callThrough();
		component.setDisabledState(true);
		component.ngOnInit();
		component.setDisabledState(false);

		component.selection.setValue('test');
		tick(500);
		fixture.detectChanges();
		expect(spy).toHaveBeenCalledWith('test');
	}));

	it('#markAsTouched should mark as touched', () => {
		const spy = spyOn(component, 'onTouched').and.callThrough();
		component['markAsTouched']();
		expect(component.touched).toBeTrue();
		expect(spy).toHaveBeenCalled();
	});

	it('#eraseValue should erase string value', () => {
		const spySelected = spyOn(component.selected, 'emit').and.callThrough();
		component.eraseValue({ preventDefault: noop, stopPropagation: noop } as any);
		expect(spySelected).toHaveBeenCalledWith(null);
	});

	it('#eraseValue should erase array data list when support multiple selection is supported', () => {
		const spySelected = spyOn(component.selected, 'emit').and.callThrough();
		component.multiple = true;
		component.selectedItems = [{ code: 'test', name: 'Test' }];
		component.eraseValue({ preventDefault: noop, stopPropagation: noop } as any);

		expect(component.selectedItems).toEqual([]);
		expect(spySelected).toHaveBeenCalledWith([]);
	});

	it('#onSelect should emit selected value', () => {
		const spySelected = spyOn(component.selected, 'emit').and.callThrough();
		component.onSelect({ option: { value: '1001' } } as any);
		expect(spySelected).toHaveBeenCalledWith('1001');
	});

	it('#onSelect should emit selected value when support multiple selection is supported', () => {
		const spySelected = spyOn(component.selected, 'emit').and.callThrough();
		component.multiple = true;
		component.selectedItems = [];
		component.onSelect({ option: { value: { code: 'item-01' } } } as any);
		expect(spySelected).toHaveBeenCalledWith([{ code: 'item-01' }]);
	});

	it('#onSelect should emit appended selected values when support multiple selection is supported', () => {
		const spySelected = spyOn(component.selected, 'emit').and.callThrough();
		component.multiple = true;
		component.selectedItems = [{ code: 'item-01' }];
		component.onSelect({ option: { value: { code: 'item-02' } } } as any);
		expect(spySelected).toHaveBeenCalledWith([{ code: 'item-01' }, { code: 'item-02' }]);
	});

	it('#onSelect should not emit new selected values when some of the items already have been selected', () => {
		const spySelected = spyOn(component.selected, 'emit').and.callThrough();
		component.multiple = true;
		component.selectedItems = [{ code: 'item-01' }];
		component.onSelect({ option: { value: { code: 'item-01' } } } as any);
		expect(spySelected).toHaveBeenCalledWith([{ code: 'item-01' }]);
	});

	it('#remove should remove item from selected items', () => {
		const spyOnChange = spyOn(component, 'onChange').and.callThrough();
		component.selectedItems = [{ code: 'item-01' }, { code: 'item-02' }];
		component.remove({ code: 'item-01' });
		expect(spyOnChange).toHaveBeenCalledWith([{ code: 'item-02' }]);
	});
});
