import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UserListObject } from '../models/user-list-object.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { ApiService } from '@shared/services/api.service';
import { HttpClient } from '@angular/common/http';
import { PaginationRequest } from '@shared/models/pagination-request.model';

@Injectable({ providedIn: 'root' })
export class UserMgmtRequestService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	getUserList(pageParams: PaginationRequest, keywords: string): Observable<PaginationResponse<UserListObject>> {
		return super.getData<PaginationResponse<UserListObject>>('user/user/list', {
			...pageParams,
			keywords,
		});
	}

	getUserInfo(id: string): Observable<UserListObject> {
		return super.getData<UserListObject>('user/user', { id });
	}

	createUser(userPayload: UserListObject): Observable<string> {
		return super.postData<string>('user/user', userPayload);
	}

	updateUser(userPayload: UserListObject): Observable<string> {
		return super.updateData<string>('user/user', userPayload);
	}

	deleteUser(id: string): Observable<string> {
		return super.deleteData<string>('user/user', { ids: [id] });
	}
}
