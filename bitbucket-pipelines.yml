# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: atlassian/default-image:2
# image: hashicorp/terraform:1.0.0

definitions:
    services:
        docker:
            memory: 2048
        docker-for-sonar:
            memory: 3072
            type: docker
    caches:
        sonar: ~/.sonar/cache # Caching SonarCloud artifacts will speed up your build

    steps:
        - step: &TagRelease
              name: 'Tag Release'
              script:
                  - PACKAGE_VERSION=$(node -p "require('./package.json').version")
                  - export VERSION=$PACKAGE_VERSION-"${BITBUCKET_COMMIT::7}"
                  - echo 'Tag release to' $VERSION
                  - git tag -fa "$VERSION" -m "Tag Release version $VERSION"
                  - git push --tags origin

        - step: &Sonar
              name: 'Perform sonar scan'
              image: node:20-alpine
              services:
                  - docker-for-sonar
              caches:
                  - sonar
                  - node
              size: 2x
              script:
                  - apk add chromium
                  - export CHROME_BIN='/usr/bin/chromium-browser'
                  - npm install -g @angular/cli
                  - npm install
                  - npm run build-test
                  - npm run test:ci
                  - pipe: sonarsource/sonarcloud-scan:2.0.0
                    variables:
                        SONAR_TOKEN: ${SONAR_TOKEN}
                        DEBUG: 'false'

        - step: &SecurityCheckSnyk
              name: Security check on Snyk
              image: snyk/snyk:node-20
              script:
                  - snyk auth $SNYK_TOKEN
                  - snyk test --severity-threshold=medium --json-file-output=.snyk-test-report.json
              artifacts:
                  download: true
                  paths:
                      - .snyk-test-report.json

        - step: &increment-version
              name: increment version
              services:
                  - docker
              caches:
                  - node
                  - docker
              image: public.ecr.aws/sam/build-nodejs20.x:latest
              script:
                  - echo "step - Increment Version "
                  - PACKAGE_VERSION=$(node -p "require('./package.json').version")
                  - npm version patch -m "Version set to $NEW_VERSION [skip ci]"
                  - NEW_VERSION=$(node -p "require('./package.json').version")
                  - echo "echo New version to commit $NEW_VERSION"
                  - git push --all origin
                  - echo -n "Develop version set to '$NEW_VERSION'."

        - step: &DeployTest
              name: 'Build Test and Push ECR'
              deployment: Test
              services:
                  - docker
              oidc: true
              caches:
                  - node
                  - docker
              image: public.ecr.aws/sam/build-nodejs20.x:latest
              script:
                  - PACKAGE_VERSION=$(node -p "require('./package.json').version")
                  # set vars
                  - chmod +x pipeline-scripts/oidc_auth.sh
                  - echo $IAM_ROLE_ARN && ./pipeline-scripts/oidc_auth.sh $IAM_ROLE_ARN
                  - export $(cat credentials | xargs -d '\n')
                  # build the image
                  - npm install -g @angular/cli
                  - npm install
                  - npm run build-test
                  #- cd dist/$BITBUCKET_REPO_SLUG/browser
                  #- cd iata-orll-be
                  - IMAGE_NAME="$PROJECT_NAME/$SERVICE_NAME"
                  - IMAGE_VERSION=$PACKAGE_VERSION
                  - echo $IMAGE_NAME
                  - echo $IMAGE_VERSION
                  - docker build -t $IMAGE_NAME:$IMAGE_VERSION .
                  - DOCKER_IMAGE="$AWS_ACCOUNT.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com.cn/$IMAGE_NAME:$IMAGE_VERSION"
                  - echo $DOCKER_IMAGE
                  - docker tag $IMAGE_NAME:$IMAGE_VERSION $DOCKER_IMAGE
                  # use the pipe to push the image to AWS ECR
                  - pipe: atlassian/aws-ecr-push-image:2.5.0
                    variables:
                        AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                        AWS_OIDC_ROLE_ARN: $IAM_ROLE_ARN
                        IMAGE_NAME: '$IMAGE_NAME'
                        TAGS: '$IMAGE_VERSION'

        - step: &DeployStaging
              name: 'Build Staging and Push ECR'
              deployment: Staging
              services:
                  - docker
              oidc: true
              caches:
                  - node
                  - docker
              image: public.ecr.aws/sam/build-nodejs20.x:latest
              script:
                  - PACKAGE_VERSION=$(node -p "require('./package.json').version")
                  # set vars
                  - chmod +x pipeline-scripts/oidc_auth.sh
                  - echo $IAM_ROLE_ARN && ./pipeline-scripts/oidc_auth.sh $IAM_ROLE_ARN
                  - export $(cat credentials | xargs -d '\n')
                  # build the image
                  - npm install -g @angular/cli
                  - npm install
                  - npm run build-staging
                  #- cd dist/$BITBUCKET_REPO_SLUG/browser
                  #- cd iata-orll-be
                  - IMAGE_NAME="$PROJECT_NAME/$SERVICE_NAME"
                  - IMAGE_VERSION=$PACKAGE_VERSION
                  - echo $IMAGE_NAME
                  - echo $IMAGE_VERSION
                  - docker build -t $IMAGE_NAME:$IMAGE_VERSION .
                  - DOCKER_IMAGE="$AWS_ACCOUNT.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com.cn/$IMAGE_NAME:$IMAGE_VERSION"
                  - echo $DOCKER_IMAGE
                  - docker tag $IMAGE_NAME:$IMAGE_VERSION $DOCKER_IMAGE
                  # use the pipe to push the image to AWS ECR
                  - pipe: atlassian/aws-ecr-push-image:2.5.0
                    variables:
                        AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                        AWS_OIDC_ROLE_ARN: $IAM_ROLE_ARN
                        IMAGE_NAME: '$IMAGE_NAME'
                        TAGS: '$IMAGE_VERSION'

        - step: &DeployDemo
              name: 'Build Demo and Push ECR'
              deployment: Demo
              services:
                  - docker
              oidc: true
              caches:
                  - node
                  - docker
              image: public.ecr.aws/sam/build-nodejs20.x:latest
              script:
                  - PACKAGE_VERSION=$(node -p "require('./package.json').version")
                  # set vars
                  - chmod +x pipeline-scripts/oidc_auth.sh
                  - echo $IAM_ROLE_ARN && ./pipeline-scripts/oidc_auth.sh $IAM_ROLE_ARN
                  - export $(cat credentials | xargs -d '\n')
                  # build the image
                  - npm install -g @angular/cli
                  - npm install
                  - npm run build-demo
                  #- cd dist/$BITBUCKET_REPO_SLUG/browser
                  #- cd iata-orll-be
                  - IMAGE_NAME="$PROJECT_NAME/$SERVICE_NAME"
                  - IMAGE_VERSION=$PACKAGE_VERSION
                  - echo $IMAGE_NAME
                  - echo $IMAGE_VERSION
                  - docker build -t $IMAGE_NAME:$IMAGE_VERSION .
                  - DOCKER_IMAGE="$AWS_ACCOUNT.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com.cn/$IMAGE_NAME:$IMAGE_VERSION"
                  - echo $DOCKER_IMAGE
                  - docker tag $IMAGE_NAME:$IMAGE_VERSION $DOCKER_IMAGE
                  # use the pipe to push the image to AWS ECR
                  - pipe: atlassian/aws-ecr-push-image:2.5.0
                    variables:
                        AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                        AWS_OIDC_ROLE_ARN: $IAM_ROLE_ARN
                        IMAGE_NAME: '$IMAGE_NAME'
                        TAGS: '$IMAGE_VERSION'
          services:
              - docker
pipelines:
    pull-requests:
        '**':
            - parallel:
                  - step: *Sonar
                  - step: *SecurityCheckSnyk

    custom:
        tag-release:
            - step: *TagRelease
        deploy-to-test:
            - step: *DeployTest
        deploy-to-staging:
            - step: *DeployStaging
        increment-version:
            - step: *increment-version
        deploy-to-demo:
            - step: *DeployDemo
        security:
            - step: *SecurityCheckSnyk
        sonar:
            - step: *Sonar
    branches:
        develop:
            - parallel:
                  - step: *SecurityCheckSnyk
                  - step: *Sonar
            - step: *DeployTest
            - step: *increment-version
        release/staging:
            - parallel:
                  - step: *Sonar
                  - step: *SecurityCheckSnyk
            -   step: *DeployStaging
        main:
            - parallel:
                  - step: *Sonar
                  - step: *SecurityCheckSnyk
            - step: *TagRelease
            - step: *DeployDemo
