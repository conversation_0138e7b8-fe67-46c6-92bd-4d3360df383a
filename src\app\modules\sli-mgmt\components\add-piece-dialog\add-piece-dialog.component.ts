import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';

@Component({
	selector: 'orll-add-piece-dialog',
	templateUrl: './add-piece-dialog.component.html',
	styleUrl: './add-piece-dialog.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatButtonModule,
		MatDialogModule,
		MatIconModule,
		TranslateModule,
		ReactiveFormsModule,
		FormsModule,
		MatRadioModule,
		CommonModule,
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AddPieceDialogComponent extends DestroyRefComponent {
	submitted = false;

	pieceTypeForm: FormGroup = new FormGroup({
		pieceType: new FormControl<string>('', [Validators.required]),
	});

	constructor(
		public dialogRef: MatDialogRef<AddPieceDialogComponent>,
		@Inject(MAT_DIALOG_DATA) public data: any
	) {
		super();
	}

	onNext(): void {
		if (this.pieceTypeForm.invalid) {
			this.submitted = true;
			return;
		}
		this.submitted = false;
		const pieceType = this.pieceTypeForm.value.pieceType;
		this.dialogRef.close(pieceType);
	}

	onCancel(): void {
		this.dialogRef.close();
	}
}
