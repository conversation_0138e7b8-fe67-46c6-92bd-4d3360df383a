import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BreadcrumbComponent } from './breadcrumb.component';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { CUSTOM_ELEMENTS_SCHEMA, Directive, Input } from '@angular/core';
import { Subject } from 'rxjs';

// Mock RouterLink directive
@Directive({
	selector: '[routerLink]',
	standalone: true,
})
export class MockRouterLinkDirective {
	@Input() routerLink: any;
}

describe('BreadcrumbComponent', () => {
	let component: BreadcrumbComponent;
	let fixture: ComponentFixture<BreadcrumbComponent>;
	let activatedRoute: ActivatedRoute;
	let eventsSubject: Subject<any>;

	// Mock router events
	const createMockRouter = () => {
		eventsSubject = new Subject<any>();
		return {
			events: eventsSubject.asObservable(),
			navigate: jasmine.createSpy('navigate'),
			createUrlTree: jasmine.createSpy('createUrlTree').and.returnValue({}),
			serializeUrl: jasmine.createSpy('serializeUrl').and.returnValue(''),
		};
	};

	// Mock activated route with configurable snapshot data
	const createMockActivatedRoute = (breadcrumbData?: any) => {
		const mockSnapshot = {
			url: [{ path: 'create' }],
			data: breadcrumbData ? { breadcrumb: breadcrumbData } : {},
		};

		return {
			outlet: 'primary',
			snapshot: mockSnapshot,
			children: [] as any,
			routeConfig: { path: 'create' },
		};
	};

	beforeEach(async () => {
		const mockRouter = createMockRouter();
		const mockActivatedRoute = createMockActivatedRoute();

		await TestBed.configureTestingModule({
			imports: [BreadcrumbComponent, CommonModule, MockRouterLinkDirective, TranslateModule.forRoot(), MatIconModule],
			providers: [
				{ provide: Router, useValue: mockRouter },
				{
					provide: ActivatedRoute,
					useValue: {
						root: mockActivatedRoute,
						children: [],
					},
				},
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA],
		}).compileComponents();

		fixture = TestBed.createComponent(BreadcrumbComponent);
		component = fixture.componentInstance;
		activatedRoute = TestBed.inject(ActivatedRoute);
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should always display the home breadcrumb', () => {
		fixture.detectChanges();
		const breadcrumbElement = fixture.nativeElement.querySelector('.iata-breadcrumb');
		expect(breadcrumbElement).toBeTruthy();

		const homeLink = fixture.nativeElement.querySelector('.iata-breadcrumb__link');
		expect(homeLink).toBeTruthy();
		expect(homeLink.getAttribute('ng-reflect-router-link')).toBe('/');
	});

	it('should initialize breadcrumbs on init', () => {
		spyOn<any>(component, 'createBreadcrumbs').and.returnValue([]);
		component.ngOnInit();
		expect(component['createBreadcrumbs']).toHaveBeenCalledWith(activatedRoute.root);
	});

	it('should update breadcrumbs on NavigationEnd events', () => {
		spyOn<any>(component, 'createBreadcrumbs').and.returnValue([]);
		component.ngOnInit();

		// Simulate NavigationEnd event
		eventsSubject.next(new NavigationEnd(1, 'sli', 'sli'));

		expect(component['createBreadcrumbs']).toHaveBeenCalledTimes(2);
	});

	it('should update breadcrumbs with correct data on navigation', () => {
		// Setup mock to return specific breadcrumbs
		const mockBreadcrumbs = [{ label: 'Route1', url: 'route1' }];
		spyOn<any>(component, 'createBreadcrumbs').and.returnValue(mockBreadcrumbs);

		// Initialize component
		component.ngOnInit();

		// Verify breadcrumbs are set correctly
		expect(component.breadcrumbs).toEqual(mockBreadcrumbs);

		// Simulate navigation
		const newMockBreadcrumbs = [
			{ label: "Shipper's Letter of Instructions (SLI) List", url: 'sli' },
			{ label: 'Create SLI', url: 'sli/create' },
		];
		(component['createBreadcrumbs'] as jasmine.Spy).and.returnValue(newMockBreadcrumbs);
		eventsSubject.next(new NavigationEnd(2, 'sli', 'sli/create'));

		// Verify breadcrumbs are updated
		expect(component.breadcrumbs).toEqual(newMockBreadcrumbs);
	});

	it('should not update breadcrumbs on non-NavigationEnd events', () => {
		spyOn<any>(component, 'createBreadcrumbs').and.returnValue([]);
		component.ngOnInit();

		// Reset call count
		(component['createBreadcrumbs'] as jasmine.Spy).calls.reset();

		// Simulate a different event
		eventsSubject.next({ type: 'other-event' });

		expect(component['createBreadcrumbs']).not.toHaveBeenCalled();
	});

	describe('createBreadcrumbs', () => {
		it('should return empty array if no children', () => {
			const mockRoute = { children: [] as any } as ActivatedRoute;
			const result = component['createBreadcrumbs'](mockRoute);
			expect(result).toEqual([]);
		});

		it('should render home breadcrumb in the DOM when breadcrumbs array is populated', () => {
			// Set up test breadcrumbs
			component.breadcrumbs = [
				{ label: "Shipper's Letter of Instructions (SLI) List", url: 'sli' },
				{ label: 'Create SLI', url: 'sli/create' },
			];

			fixture.detectChanges();

			// verify the home breadcrumb exists
			const homeBreadcrumb = fixture.nativeElement.querySelector('.iata-breadcrumb__item');
			expect(homeBreadcrumb).toBeTruthy();
		});

		it('should skip non-primary outlets', () => {
			const mockChild = createMockActivatedRoute();
			mockChild.outlet = 'not-primary';

			const mockRoute = {
				children: [mockChild] as any,
			} as ActivatedRoute;

			const result = component['createBreadcrumbs'](mockRoute);
			expect(result).toEqual([]);
		});

		it('should create breadcrumb with label from route data', () => {
			const mockChild = createMockActivatedRoute({ label: 'Create SLI' });

			const mockRoute = {
				children: [mockChild] as any,
			} as ActivatedRoute;

			// Mock child to have no children to avoid recursion
			mockChild.children = [];

			const result = component['createBreadcrumbs'](mockRoute);

			expect(result.length).toBe(1);
			expect(result[0].label).toBe('Create SLI');
			expect(result[0].url).toBe('create');
		});

		it('should use route URL as label if breadcrumb data has no label', () => {
			// Create a mock route with breadcrumb data but no label
			const mockChild = createMockActivatedRoute({});

			const mockRoute = {
				children: [mockChild] as any,
			} as ActivatedRoute;

			// Mock child to have no children to avoid recursion
			mockChild.children = [];

			const result = component['createBreadcrumbs'](mockRoute);

			expect(result.length).toBe(1);
			expect(result[0].label).toBe('create'); // Should use the route path as label
			expect(result[0].url).toBe('create');
		});

		it('should create breadcrumb with icon if provided', () => {
			const mockChild = createMockActivatedRoute({
				label: 'Create SLI',
				icon: 'add',
			});

			const mockRoute = {
				children: [mockChild] as any,
			} as ActivatedRoute;

			// Mock child to have no children to avoid recursion
			mockChild.children = [];

			const result = component['createBreadcrumbs'](mockRoute);

			expect(result.length).toBe(1);
			expect(result[0].icon).toBe('add');
		});

		it('should build correct URL for nested routes', () => {
			// Create parent route
			const mockParent = createMockActivatedRoute({ label: "Shipper's Letter of Instructions (SLI) List" });
			mockParent.snapshot.url = [{ path: 'sli' }];

			// Create child route
			const mockChild = createMockActivatedRoute({ label: 'Create SLI' });
			mockChild.snapshot.url = [{ path: 'create' }];

			// Set up hierarchy
			mockParent.children = [mockChild];
			mockChild.children = [];

			const mockRoute = {
				children: [mockParent] as any,
			} as ActivatedRoute;

			const result = component['createBreadcrumbs'](mockRoute);

			expect(result.length).toBe(2);
			expect(result[0].url).toBe('sli');
			expect(result[1].url).toBe('sli/create');
		});
	});

	it('should handle breadcrumbs without URLs', () => {
		// Set up test breadcrumbs without URLs
		component.breadcrumbs = [
			{
				label: 'No URL Breadcrumb',
				url: '',
			},
		];
		fixture.detectChanges();

		// Check if non-link breadcrumb is rendered correctly
		const nonLinkBreadcrumb = fixture.nativeElement.querySelector('.iata-breadcrumb__item:not(.iata-breadcrumb__item--home)');
		expect(nonLinkBreadcrumb).toBeTruthy();
		expect(nonLinkBreadcrumb.textContent).toContain('common.breadcrumb.home');
	});
});
