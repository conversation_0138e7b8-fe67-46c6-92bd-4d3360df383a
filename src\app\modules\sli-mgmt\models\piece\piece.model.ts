import { Product } from './product.model';
import { PackagingType } from './packaging-type.model';
import { Dimensions } from './dimensions.model';
import { PieceItem } from './piece-item.model';

export interface Piece {
	sliNumber?: string;
	pieceId?: string;
	type?: string;
	product: Product;
	packagingType: PackagingType;
	packagedIdentifier?: string;
	nvdForCustoms?: boolean;
	nvdForCarriage?: boolean;
	grossWeight: number;
	dimensions: Dimensions;
	pieceQuantity: number;
	slac?: number;
	upid?: string;
	shippingMarks?: string;
	textualHandlingInstructions?: string;
	containedPieces?: Piece[];
	containedItems: PieceItem[];
	allPackedInOneIndicator?: boolean;
	qvalueNumeric?: number;
	productDg?: PieceProductDg;
	dgDeclaration?: PieceDgDeclaration;
	//live animals
	speciesCommonName?: string;
	speciesScientificName?: string;
	specimenDescription?: string;
	quantityAnimals?: number;
}

export interface PieceProductDg {
	specialProvisionId: string;
	explosiveCompatibilityGroupCode: string;
	packagingDangerLevelCode: string;
	technicalName: string;
	unNumber: string;
	description: string;
	hsCommodityDescription: string;
	properShippingName: string;
	hazardClassificationId: string;
	additionalHazardClassificationId: string;
	packingInstructionNumber: string;
	authorizationInformation: string;
}

export interface PieceDgDeclaration {
	shipperDeclarationText: string;
	handlingInformation: string;
	complianceDeclarationText: string;
	exclusiveUseIndicator: string;
	aircraftLimitationInformation: string;
}
