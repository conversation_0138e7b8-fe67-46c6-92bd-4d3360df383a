@mixin iata-tabs-theme($theme) {
	.mat-mdc-tab-group {
		--mdc-tab-indicator-active-indicator-color: var(--iata-red-primary);
		--mdc-tab-indicator-active-indicator-height: 4px;
		--mat-tab-header-inactive-label-text-color: var(--iata-grey-600);
		--mat-tab-header-active-label-text-color: var(--iata-red-primary);
		--mat-tab-header-inactive-focus-label-text-color: var(--iata-grey-600);
		--mat-tab-header-inactive-hover-label-text-color: var(--iata-grey-600);
		--mat-tab-header-active-focus-label-text-color: var(--iata-red-primary);
		--mat-tab-header-active-hover-label-text-color: var(--iata-red-primary);
		--mat-tab-header-active-focus-indicator-color: var(--iata-red-primary);
		--mat-tab-header-active-hover-indicator-color: var(--iata-red-primary);

		.mat-mdc-tab-header {
			--mdc-secondary-navigation-tab-container-height: 40px;
			--mat-tab-header-label-text-size: 16px;
			--mat-tab-header-label-text-line-height: 20px;
			--mat-tab-header-label-text-weight: 400;
		}

		.mdc-tab {
			padding-left: 10px;
			padding-right: 10px;
			font-size: 11px;
		}

		.mat-mdc-tab-body-content {
			overflow-x: hidden;
		}

		&.iata-mat-tabs--light {
			.mat-mdc-tab {
				background-color: var(--iata-white);
			}

			.mat-mdc-tab-labels {
				border-bottom: 1px solid var(--iata-grey-200);
			}
		}
	}
}
