if [ -z $BITBUCKET_STEP_OIDC_TOKEN ]; then
  echo "You must enable OIDC on this Bitbucket pipeline step! Add \"oidc: true\" to this step."
  exit 1
fi

STS=($(aws sts assume-role-with-web-identity \
      --role-arn $1 \
      --role-session-name "BitBucket-runner-${BITBUCKET_COMMIT}-${BITBUCKET_BUILD_NUMBER}" \
      --web-identity-token $BITBUCKET_STEP_OIDC_TOKEN \
      --duration-seconds 3600 \
      --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]' \
      --output text))

echo AWS_ACCESS_KEY_ID="${STS[0]}" >> credentials
echo AWS_SECRET_ACCESS_KEY="${STS[1]}" >> credentials
echo AWS_SESSION_TOKEN="${STS[2]}" >> credentials