import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslatePipe } from '@ngx-translate/core';
import { FormsModule, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatAutocomplete, MatAutocompleteSelectedEvent, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { CodeName } from '@shared/models/code-name.model';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { Piece } from '../../models/piece/piece.model';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { startWith } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

const REGEX_ONE_NUMBER = /^\d+(\.\d)?$/;

@Component({
	selector: 'orll-live-animal-piece-add',
	imports: [
		CommonModule,
		ReactiveFormsModule,
		TranslatePipe,
		MatFormFieldModule,
		MatIconModule,
		MatInputModule,
		MatExpansionModule,
		MatButtonModule,
		MatSelectModule,
		MatAutocompleteTrigger,
		FormsModule,
		MatAutocomplete,
	],
	templateUrl: './live-animal-piece-add.component.html',
	styleUrl: './live-animal-piece-add.component.scss',
})
export class LiveAnimalPieceAddComponent extends DestroyRefComponent implements OnInit, OnChanges {
	@Input() piece: Piece | null = null;

	sliLiveAnimalPieceForm = this.fb.group({
		productDescription: [''],
		typeOfPackage: this.fb.group({
			typeCode: ['', Validators.required],
			description: [''],
		}),
		packagedIdentifier: [''],
		speciesCommonName: [''],
		speciesScientificName: [''],
		specimenDescription: [''],
		animalQuantity: [null, [Validators.required, Validators.pattern(/^\d+$/)]],
		shippingMarks: [''],
		upid: [''],
		grossWeight: ['', [Validators.pattern(REGEX_ONE_NUMBER)]],
		dimensions: this.fb.group({
			length: this.fb.control<string | null>(null, [Validators.required, Validators.pattern(REGEX_ONE_NUMBER)]),
			width: this.fb.control<string | null>(null, [Validators.required, Validators.pattern(REGEX_ONE_NUMBER)]),
			height: this.fb.control<string | null>(null, [Validators.required, Validators.pattern(REGEX_ONE_NUMBER)]),
		}),
		whetherHaveDeclaredValueForCustoms: [true],
		whetherHaveDeclaredValueForCarriage: [true],
		textualHandlingInstructions: [''],
	});

	typeOfPackage = '';

	packagingTypes: CodeName[] = [];
	filteredPackagingTypes: CodeName[] = [];

	constructor(
		private readonly fb: NonNullableFormBuilder,
		public readonly sliCreateRequestService: SliCreateRequestService
	) {
		super();
	}

	ngOnInit() {
		this.sliCreateRequestService.getPackingTypes().subscribe((packagingTypes: CodeName[]) => {
			this.packagingTypes = packagingTypes;
			this.filteredPackagingTypes = [...packagingTypes];
		});

		this.sliLiveAnimalPieceForm
			.get(['typeOfPackage', 'typeCode'])
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredPackagingTypes = this.packagingTypes.filter((packagingTypes) =>
					packagingTypes.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
				);
			});
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes['piece']) {
			if (this.piece) {
				this.patchLiveAnimalPieceForm(this.piece);
			} else {
				this.sliLiveAnimalPieceForm.reset();
			}
		}
	}

	displayPackagingTypeName = (code: string): string => {
		const type = this.packagingTypes.find((item) => item.code === code);
		const name = type?.name ?? '';
		return name;
	};

	onPackagingTypeSelected(event: MatAutocompleteSelectedEvent) {
		const type = this.packagingTypes.find((item) => item.code === event.option.value);
		const name = type?.name ?? '';
		this.sliLiveAnimalPieceForm.get('typeOfPackage')?.patchValue({
			typeCode: event.option.value,
			description: name,
		});
	}

	private patchLiveAnimalPieceForm(piece: Piece) {
		this.sliLiveAnimalPieceForm.patchValue({
			productDescription: piece.product?.description,
			typeOfPackage: {
				typeCode: piece.packagingType?.typeCode,
				description: piece.packagingType?.description,
			},
			packagedIdentifier: piece.packagedIdentifier,
			speciesCommonName: piece.speciesCommonName,
			speciesScientificName: piece.speciesScientificName,
			specimenDescription: piece.specimenDescription,
			animalQuantity: piece.quantityAnimals as any,
			shippingMarks: piece.shippingMarks as any,
			upid: piece.upid as any,
			grossWeight: piece.grossWeight as any,
			dimensions: {
				length: piece.dimensions?.length as any,
				width: piece.dimensions?.width as any,
				height: piece.dimensions?.height as any,
			},
			whetherHaveDeclaredValueForCustoms: piece.nvdForCustoms as any,
			whetherHaveDeclaredValueForCarriage: piece.nvdForCarriage as any,
			textualHandlingInstructions: piece.textualHandlingInstructions,
		});
	}

	onTypeOfPackageChange(name: string) {
		const typeOfPackage = this.packagingTypes.find((packagingType: CodeName) => packagingType.name === name);

		this.filteredPackagingTypes = this.packagingTypes.filter((packagingTypes) =>
			packagingTypes.name.toLowerCase().includes(name?.toLowerCase().trim() ?? '')
		);

		if (typeOfPackage) {
			this.sliLiveAnimalPieceForm.get('typeOfPackage')?.patchValue({
				typeCode: typeOfPackage.code,
				description: typeOfPackage.name,
			});
		}
	}

	hasValidTouchedFormData() {
		return this.sliLiveAnimalPieceForm.touched && this.sliLiveAnimalPieceForm.valid;
	}
}
