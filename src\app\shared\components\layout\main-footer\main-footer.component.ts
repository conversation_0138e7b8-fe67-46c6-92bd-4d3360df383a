import { ChangeDetectionStrategy, Component } from '@angular/core';
import { environment } from '@environments/environment';
import { TranslateModule } from '@ngx-translate/core';

@Component({
	selector: 'iata-main-footer',
	templateUrl: './main-footer.component.html',
	styleUrls: ['./main-footer.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [TranslateModule],
})
export class MainFooterComponent {
	readonly supportUrl = environment.supportUrl;
}
