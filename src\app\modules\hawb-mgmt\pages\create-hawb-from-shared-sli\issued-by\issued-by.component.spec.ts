import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IssuedByComponent } from './issued-by.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';

describe('IssuedByComponent', () => {
	let component: IssuedByComponent;
	let fixture: ComponentFixture<IssuedByComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [IssuedByComponent],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting(), provideTranslateService()],
		}).compileComponents();

		fixture = TestBed.createComponent(IssuedByComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
