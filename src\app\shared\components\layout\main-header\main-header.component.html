<!-- Desktop Header -->
<header class="iata-main-header">
    <div class="iata-page-content-container iata-main-header__content">
        <div class="iata-main-header__logo-container">
            <a routerLink="/" class="iata-main-header__logo-link">
				<img src="assets/images/iata-logo.svg" class="iata-main-header__logo" alt="IATA logo"/>
				<div>
					<span class="iata-main-header__title">{{'common.mainHeader.app.title' | translate}}</span>
					<span class="iata-main-header__subtitle">{{'common.mainHeader.app.subtitle' | translate}}</span>
				</div>
            </a>
        </div>

        <div class="iata-main-header__nav-container">
			<nav class="iata-main-header__primary-nav" aria-label="Primary navigation">
				<ul class="iata-main-header__primary-nav__list">

					@if (hasSomeRole(menuSliRoles) | async) {
						<li class="iata-main-header__primary-nav__list__item">
							<a class="iata-main-header__primary-nav__list__item__link" routerLink="sli" routerLinkActive="iata-active-nav-item">
								<span class="iata-main-header__primary-nav__list__item__link__label">{{'common.mainHeader.mainNav.sli' | translate}}</span>
							</a>
						</li>
					}

					@if (hasSomeRole(menuHawbRoles) | async) {
						<li class="iata-main-header__primary-nav__list__item">
							<a class="iata-main-header__primary-nav__list__item__link" routerLink="hawb" routerLinkActive="iata-active-nav-item">
								<span class="iata-main-header__primary-nav__list__item__link__label">{{'common.mainHeader.mainNav.hawb' | translate}}</span>
							</a>
						</li>
					}

					@if (hasSomeRole(menuMawbRoles) | async) {
						<li class="iata-main-header__primary-nav__list__item">
							<a class="iata-main-header__primary-nav__list__item__link" routerLink="mawb" routerLinkActive="iata-active-nav-item">
								<span class="iata-main-header__primary-nav__list__item__link__label">{{'common.mainHeader.mainNav.mawb' | translate}}</span>
							</a>
						</li>
					}

					@if (isSuperUser() | async) {
						<li class="iata-main-header__primary-nav__list__item">
							<a class="iata-main-header__primary-nav__list__item__link" routerLink="users-mgmt" routerLinkActive="iata-active-nav-item">
								<span class="iata-main-header__primary-nav__list__item__link__label">{{'common.mainHeader.mainNav.users' | translate}}</span>
							</a>
						</li>
					}

				</ul>
			</nav>

			<ng-container *ngIf="getCurrentUser() | async as user">
				<nav class="iata-main-header__secondary-nav" aria-label="Secondary navigation">
					<ul class="iata-main-header__secondary-nav__list">
						<li class="iata-main-header__secondary-nav__list__item">
							<a class="iata-main-header__secondary-nav__list__item__link">
								<mat-icon color="primary" class="user-toggle" aria-hidden="false" aria-label="{{'common.mainHeader.account' | translate}}"
									matTooltip="{{'common.mainHeader.account' | translate}}"
									(click)="$event.stopPropagation(); isOpen = !isOpen"
									(keydown.enter)="$event.stopPropagation(); isOpen = !isOpen"
									cdkOverlayOrigin
									#trigger="cdkOverlayOrigin"
								>account_circle</mat-icon>
							</a>
							<div class="username">{{user?.firstName }} {{user?.lastName}}</div>
						</li>
					</ul>
				</nav>

				<ng-template
					cdkConnectedOverlay
					[cdkConnectedOverlayOrigin]="trigger"
					[cdkConnectedOverlayOpen]="isOpen"
					(backdropClick)="isOpen = false"
					[cdkConnectedOverlayHasBackdrop]="true"
				>
					<ul class="iata-main-header__secondary-nav__dropdown-list">
						<div class="role-title">{{'common.mainHeader.currentRole' | translate}}</div>
						<mat-divider></mat-divider>
						<li class="iata-main-header__secondary-nav__dropdown-list__item">
							<div class="current-role">
								<mat-icon color="" aria-hidden="false" aria-label="{{'common.mainHeader.userid' | translate}}">person</mat-icon>
								<span>{{user?.primaryOrgName}}</span>
							</div>
						</li>
						@for (role of user?.orgList; track role.id) {
							<li class="iata-main-header__secondary-nav__dropdown-list__item">
								<a class="iata-main-header__secondary-nav__dropdown-list__item__link"
									(click)="$event.stopPropagation(); onSwitch(role)"
									(keydown.enter)="$event.stopPropagation(); onSwitch(role)">
									<mat-icon color="primary" aria-hidden="false" aria-label="{{'common.mainHeader.userid' | translate}}">person</mat-icon>
									<span>{{role.name}}</span>
								</a>
							</li>
						}
					</ul>
				</ng-template>
			</ng-container>
        </div>
    </div>
</header>
