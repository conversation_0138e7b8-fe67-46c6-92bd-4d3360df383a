import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { CodeName } from '@shared/models/code-name.model';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { SliPieceTableComponent } from '../sli-piece-table/sli-piece-table.component';
import { CommonModule } from '@angular/common';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { startWith } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { PieceList } from '../../models/piece/piece-list.model';

const REGX_NUMBER_1_DECIMAL = '^\\d+(\\.\\d{1})?$';
const REGX_NUMBER_2_DECIMAL = /^\d+(\.\d{1,2})?$/;

@Component({
	selector: 'orll-sli-piece-list',
	templateUrl: './sli-piece-list.component.html',
	styleUrl: './sli-piece-list.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatInputModule,
		MatSelectModule,
		MatIconModule,
		TranslateModule,
		ReactiveFormsModule,
		FormsModule,
		MatFormFieldModule,
		CommonModule,
		MatAutocompleteModule,
		SliPieceTableComponent,
	],
})
export class SliPieceListComponent extends DestroyRefComponent implements OnInit {
	currencies: string[] = [];
	weightValuationIndicators: string[] = [DropDownType.PREPAID, DropDownType.COLLECT];
	incoterms: CodeName[] = [];
	pieceList: PieceList[] = [];
	pageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};
	totalRecords = 0;
	dataLoading = false;

	filteredDeclaredCustomsCurrency: string[] = [];
	filteredDeclaredCarriageCurrency: string[] = [];
	filteredInsuredAmountCurrency: string[] = [];

	sliPieceListForm: FormGroup = new FormGroup({
		goodsDescription: new FormControl<string>('', [Validators.required]),
		totalGrossWeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimLength: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimWidth: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimHeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		declaredValueForCustoms: new FormControl<string>('NCV', [Validators.pattern('^NCV$|^\\d+(\\.\\d{1,2})?$')]),
		declaredValueForCarriage: new FormControl<string>('NVD', [Validators.pattern('^NVD$|^\\d+(\\.\\d{1,2})?$')]),
		insuredAmount: new FormControl<string>('NIL', [Validators.pattern('^NIL$|^\\d+(\\.\\d{1,2})?$')]),
		declaredValueForCustomsCurrency: new FormControl<string>(''),
		declaredValueForCarriageCurrency: new FormControl<string>(''),
		insuredAmountCurrency: new FormControl<string>(''),
		textualHandlingInstructions: new FormControl<string>(''),
		weightValuationIndicator: new FormControl<string>(''),
		incoterms: new FormControl<string>(''),
	});

	@Input() sliNumber = '';
	@Output() saveRequest = new EventEmitter<string>();

	constructor(
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly cdr: ChangeDetectorRef
	) {
		super();
	}

	ngOnInit(): void {
		this.initRefData();
		this.setupAutocomplete();

		this.refreshData();
	}

	refreshData() {
		if (this.sliNumber) {
			// for NE-ONE loop timer
			setTimeout(() => {
				this.getPieceListPage(this.pageParams);
			}, 2000);
		}
	}

	private initRefData(): void {
		this.sliCreateRequestService.getCurrencies().subscribe((currencies: string[]) => {
			this.currencies = ['', ...currencies];
			this.filteredInsuredAmountCurrency = ['', ...currencies];
		});

		this.sliCreateRequestService.getIncoterms().subscribe((incoterms: CodeName[]) => {
			this.incoterms = incoterms;
		});
	}

	private setupAutocomplete(): void {
		this.sliPieceListForm
			.get('declaredValueForCustomsCurrency')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredDeclaredCustomsCurrency = this.filterCurrency(search);
			});

		this.sliPieceListForm
			.get('declaredValueForCarriageCurrency')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredDeclaredCarriageCurrency = this.filterCurrency(search);
			});

		this.sliPieceListForm
			.get('insuredAmountCurrency')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredInsuredAmountCurrency = this.filterCurrency(search);
			});
	}

	filterCurrency(search: string): string[] {
		return this.currencies.filter((currency) => currency.toLowerCase().includes(search?.toLowerCase().trim() ?? ''));
	}

	// eslint-disable-next-line
	getFormData(ignore?: boolean): {} | null {
		if (!ignore && this.sliPieceListForm.invalid) {
			return null;
		}
		return {
			goodsDescription: this.sliPieceListForm.value.goodsDescription!,
			totalGrossWeight: this.sliPieceListForm.value.totalGrossWeight ? Number(this.sliPieceListForm.value.totalGrossWeight) : '',
			totalDimensions: {
				length: this.sliPieceListForm.value.dimLength ? Number(this.sliPieceListForm.value.dimLength) : '',
				width: this.sliPieceListForm.value.dimWidth ? Number(this.sliPieceListForm.value.dimWidth) : '',
				height: this.sliPieceListForm.value.dimHeight ? Number(this.sliPieceListForm.value.dimHeight) : '',
			},
			insuredAmount: {
				numericalValue: REGX_NUMBER_2_DECIMAL.test(this.sliPieceListForm.value.insuredAmount)
					? Number(this.sliPieceListForm.value.insuredAmount)
					: null,
				currencyUnit: this.sliPieceListForm.value.insuredAmountCurrency,
			},
			declaredValueForCarriage: {
				numericalValue: REGX_NUMBER_2_DECIMAL.test(this.sliPieceListForm.value.declaredValueForCarriage)
					? Number(this.sliPieceListForm.value.declaredValueForCarriage)
					: null,
				currencyUnit: this.sliPieceListForm.value.declaredValueForCarriageCurrency,
			},
			declaredValueForCustoms: {
				numericalValue: REGX_NUMBER_2_DECIMAL.test(this.sliPieceListForm.value.declaredValueForCustoms)
					? Number(this.sliPieceListForm.value.declaredValueForCustoms)
					: null,
				currencyUnit: this.sliPieceListForm.value.declaredValueForCustomsCurrency,
			},
			weightValuationIndicator: this.sliPieceListForm.value.weightValuationIndicator,
			textualHandlingInstructions: this.sliPieceListForm.value.textualHandlingInstructions,
			incoterms: this.sliPieceListForm.value.incoterms,
			pieces: [], // update SLI only without piece list data
		};
	}

	onSortChange(sort: Sort): void {
		if (sort.direction === '') {
			this.pageParams.orderByColumn = '';
			this.pageParams.isAsc = '';
			return;
		}
		this.pageParams.orderByColumn = sort.active;
		this.pageParams.isAsc = sort.direction;
	}

	onPageChange(event: PageEvent & { sortField?: string; sortDirection?: string }): void {
		this.pageParams.pageNum = event.pageIndex + 1;
		this.pageParams.pageSize = event.pageSize;
		if (event.sortDirection) {
			this.pageParams.orderByColumn = event.sortField;
			this.pageParams.isAsc = event.sortDirection;
		}

		this.getPieceListPage(this.pageParams);
	}

	private getPieceListPage(pageParams: PaginationRequest): void {
		this.dataLoading = true;
		this.pieceList = [];

		this.sliCreateRequestService
			.getPieceList(pageParams, this.sliNumber)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.pieceList = res.rows;
					this.totalRecords = res.total;
					this.dataLoading = false;
					this.cdr.markForCheck();
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}
}
