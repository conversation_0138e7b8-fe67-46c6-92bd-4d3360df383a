<div class="orll-mawb-search-form">
	<form [formGroup]="mawbSearchForm">
		<h2 class="mat-display-2 orll-mawb-search-form__title">{{'mawb.mgmt.title' | translate}}</h2>
		<div class="row">
			<div class="col-4">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-icon matPrefix>search</mat-icon>
					<mat-label>{{'sli.mgmt.goodsDescription' | translate}}</mat-label>
					<input matInput formControlName="goodsDescription">
				</mat-form-field>
			</div>

			<div class="col-3">
				<iata-autocomplete
					#mawbNumberAutocomplete
					[id]="'mawbNumber'"
					[api]="mawbSearchRequestService"
					[label]="'mawb.mgmt.mawbNumber' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'mawbNumber')">
				</iata-autocomplete>
			</div>

			<div class="col-3">
				<iata-autocomplete
					#airlineCodeAutocomplete
					[id]="'airlineCode'"
					[api]="mawbSearchRequestService"
					[label]="'mawb.mgmt.airlineCode' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'airlineCode')">
				</iata-autocomplete>
			</div>
		</div>

		<div class="row">
			<div class="col-2">
				<iata-autocomplete
					#departureAutocomplete
					[id]="'origin'"
					[api]="mawbSearchRequestService"
					[label]="'sli.mgmt.departureLocation' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'origin')">
				</iata-autocomplete>
			</div>

			<div class="col-2">
				<iata-autocomplete
					#arrivalAutocomplete
					[id]="'destination'"
					[api]="mawbSearchRequestService"
					[label]="'sli.mgmt.arrivalLocation' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'destination')">
				</iata-autocomplete>
			</div>

			<div class="col-3">
				<iata-autocomplete
					#latestStatusAutocomplete
					[id]="'latestStatus'"
					[api]="mawbSearchRequestService"
					[label]="'mawb.mgmt.latestStatus' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'latestStatus')">
				</iata-autocomplete>
			</div>

			<div class="col-3">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-icon matPrefix>search</mat-icon>
					<mat-label>{{'sli.mgmt.createDate' | translate}}</mat-label>
					<mat-date-range-input [rangePicker]="createDateRangePicker">
						<input matStartDate formControlName="startDate" placeholder="Start date">
						<input matEndDate formControlName="endDate" placeholder="End date">
					</mat-date-range-input>
					<mat-datepicker-toggle matIconSuffix [for]="createDateRangePicker"></mat-datepicker-toggle>
					<mat-date-range-picker #createDateRangePicker></mat-date-range-picker>
				</mat-form-field>
			</div>

			<div class="orll-mawb-search-form__footer col-2">
				<button mat-stroked-button color="primary" (click)="onReset($event)" class="orll-mawb-search-form__reset-button">
					<mat-icon>refresh</mat-icon>
					{{'sli.mgmt.reset' | translate}}
				</button>
				<button mat-flat-button color="primary" [disabled]="mawbSearchForm.invalid" (click)="onSearch()">
					<mat-icon>search</mat-icon>
					{{'sli.mgmt.search' | translate}}
				</button>
			</div>
		</div>
	</form>
</div>
