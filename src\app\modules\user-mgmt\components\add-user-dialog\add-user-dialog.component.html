<div class="orll-add-user-dialog">
	<h2 mat-dialog-title class="orll-add-user-dialog__title">{{'users.mgmt.create.title' | translate}}</h2>
	<mat-dialog-content>
		<form [formGroup]="addUserForm" class="orll-add-user-dialog__content">
			<div class="row">
				<mat-form-field appearance="outline" class="col-6">
					<mat-label>{{'users.mgmt.create.firstName' | translate}}</mat-label>
					<input matInput formControlName="firstName" required>
					@if (addUserForm.get('firstName')?.hasError('required')) {
						<mat-error>{{'users.mgmt.create.firstName.required' | translate}}</mat-error>
					}
				</mat-form-field>

				<mat-form-field appearance="outline" class="col-6">
					<mat-label>{{'users.mgmt.create.lastName' | translate}}</mat-label>
					<input matInput formControlName="lastName" required>
					@if (addUserForm.get('lastName')?.hasError('required')) {
						<mat-error>{{'users.mgmt.create.lastName.required' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>

			<div class="row">
				<mat-form-field appearance="outline" class="col-12">
					<mat-label>{{'users.mgmt.create.email' | translate}}</mat-label>
					<input matInput formControlName="email" required>
					@if (addUserForm.get('email')?.hasError('required')) {
						<mat-error>{{'users.mgmt.create.email.required' | translate}}</mat-error>
					}
					@if (addUserForm.get('email')?.hasError('email')) {
						<mat-error>{{'validators.email' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>

			<div class="row">
				<mat-form-field appearance="outline" class="col-4">
					<mat-label>{{'users.mgmt.create.orgName' | translate}}</mat-label>
					<input matInput formControlName="orgName" required>
					@if (addUserForm.get('orgName')?.hasError('required')) {
						<mat-error>{{'users.mgmt.create.orgName.required' | translate}}</mat-error>
					}
				</mat-form-field>

				<mat-form-field appearance="outline" class="col-4">
					<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
					<mat-label>{{'users.mgmt.create.primaryOrgName' | translate}}</mat-label>
					<input type="text" matInput
						formControlName="primaryOrgId"
						[matAutocomplete]="autoPrimaryOrg">
					<mat-autocomplete #autoPrimaryOrg="matAutocomplete" [displayWith]="displayOrgName">
						@for (org of filteredPrimaryOrgList; track org) {
							<mat-option [value]="org.id">{{org.name}}</mat-option>
						}
					</mat-autocomplete>
					@if (addUserForm.get('primaryOrgId')?.hasError('required')) {
						<mat-error>{{'users.mgmt.create.primaryOrgName.required' | translate}}</mat-error>
					}
				</mat-form-field>

				<mat-form-field appearance="outline" class="col-4">
					<mat-label>{{'users.mgmt.create.userType' | translate}}</mat-label>
					<mat-select formControlName="userType">
						@for (userType of userTypes; track userType) {
							<mat-option [value]="userType.code">{{userType.name}}</mat-option>
						}
					</mat-select>
					@if (addUserForm.get('userType')?.hasError('required')) {
						<mat-error>{{'users.mgmt.create.userType.required' | translate}}</mat-error>
					}
				</mat-form-field>
			</div>
		</form>

		<div class="orll-add-user-dialog__secondary">
			<mat-checkbox
				(click)="$event.stopPropagation(); isSecondary = !isSecondary"
				(keydown.enter)="$event.stopPropagation(); isSecondary = !isSecondary"
				[(ngModel)]="isSecondary"
				(change)="isSecondary = !isSecondary">
			</mat-checkbox>
			<div class="orll-add-user-dialog__checkbox">{{'users.mgmt.create.secondary' | translate}}</div>
		</div>

		@if (isSecondary) {
			<orll-secondary-user-panel [secondaryUserList]="secondaryUserList"></orll-secondary-user-panel>
		}
	</mat-dialog-content>
	<mat-dialog-actions align="end">
		<button mat-stroked-button (click)="onCancel()" class="orll-add-user-dialog__cancel-button">
			{{'common.dialog.cancel' | translate}}
		</button>
		<button mat-flat-button color="primary" class="orll-add-user-dialog__ok-button" (click)="onOk()">
			<mat-icon>check</mat-icon>
			{{'common.dialog.ok' | translate}}
		</button>
	</mat-dialog-actions>
</div>
