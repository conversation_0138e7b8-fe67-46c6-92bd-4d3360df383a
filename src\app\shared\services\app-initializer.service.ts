import { Injectable } from '@angular/core';
import { AuthService } from '@shared/auth/auth.service';

@Injectable({
	providedIn: 'root',
})
export class AppInitializerService {
	constructor(private readonly authService: AuthService) {}

	async initializeApp(): Promise<boolean> {
		return new Promise((resolve) => {
			if (!this.authService.isLoggedIn()) {
				this.authService
					.login({
						userId: 'shipper1',
						orgId: 'ddbab271-1ddd-4cc4-b546-af9a0c0d4943',
					})
					.subscribe({
						next: () => resolve(true),
						error: () => resolve(false),
					});
			} else {
				resolve(true);
			}
		});
	}
}
