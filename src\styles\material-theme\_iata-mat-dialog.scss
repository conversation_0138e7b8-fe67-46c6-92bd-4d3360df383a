@mixin iata-dialog-theme($theme) {
	.mdc-dialog__container {
		--mdc-dialog-container-shape: 1px;

		.mdc-dialog {
			&__title {
				--mdc-dialog-subhead-color: var(--iata-white);
				--mdc-dialog-subhead-line-height: 25px;
				--mdc-dialog-subhead-size: 20px;
				--mdc-dialog-subhead-weight: 500;

				position: relative;
				margin: 0;
				padding: 0;
				height: 64px;
				background-color: var(--iata-blue-primary);
				text-align: center;

				// custom styles for the Close icon
				.iata-dialog-close-icon {
					position: absolute;
					right: 18px;
					top: 50%;
					transform: translate(0, -50%);
					cursor: pointer;
				}
			}

			&__content {
				--mdc-dialog-supporting-text-color: var(--iata-grey-primary);
				padding: 0 !important;
			}

			&__actions {
				padding: 0 !important;
			}
		}

		.mat-mdc-dialog-content {
			max-height: 80vh;
		}
	}
}
