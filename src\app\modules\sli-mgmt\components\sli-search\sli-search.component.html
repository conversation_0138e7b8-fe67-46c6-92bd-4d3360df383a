<div class="orll-sli-search-form">
	<form [formGroup]="sliSearchForm">
		<h2 class="mat-display-2 orll-sli-search-form__title">{{'sli.mgmt.title' | translate}}</h2>
		<div class="row">
			<div class="col-2">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-icon matPrefix>search</mat-icon>
					<mat-label>{{'sli.mgmt.goodsDescription' | translate}}</mat-label>
					<input matInput formControlName="goodsDescription">
				</mat-form-field>
			</div>

			<div class="col-2">
				<iata-autocomplete
					#sliNumberAutocomplete
					[id]="'sliCode'"
					[api]="sliSearchRequestService"
					[label]="'sli.mgmt.sliCode' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'sliCode')">
				</iata-autocomplete>
			</div>

			<div class="col-3">
				<iata-autocomplete
					#shipperAutocomplete
					[id]="'shipper'"
					[api]="sliSearchRequestService"
					[label]="'sli.mgmt.shipper' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'shipper')">
				</iata-autocomplete>
			</div>

			<div class="col-3">
				<iata-autocomplete
					#consigneeAutocomplete
					[id]="'consignee'"
					[api]="sliSearchRequestService"
					[label]="'sli.mgmt.consignee' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'consignee')">
				</iata-autocomplete>
			</div>
		</div>

		<div class="row">
			<div class="col-2">
				<iata-autocomplete
					#departureAutocomplete
					[id]="'airport'"
					[api]="sliSearchRequestService"
					[label]="'sli.mgmt.departureLocation' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'departureLocation')">
				</iata-autocomplete>
			</div>

			<div class="col-2">
				<iata-autocomplete
					#arrivalAutocomplete
					[id]="'airport'"
					[api]="sliSearchRequestService"
					[label]="'sli.mgmt.arrivalLocation' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'arrivalLocation')">
				</iata-autocomplete>
			</div>

			<div class="col-3">
				<iata-autocomplete
					#hawbNumberAutocomplete
					[id]="'hawbNumber'"
					[api]="sliSearchRequestService"
					[label]="'sli.mgmt.hawbNumber' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'hawbNumber')">
				</iata-autocomplete>
			</div>

			<div class="col-3">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-icon matPrefix>search</mat-icon>
					<mat-label>{{'sli.mgmt.createDate' | translate}}</mat-label>
					<mat-date-range-input [rangePicker]="createDateRangePicker">
						<input matStartDate formControlName="startDate" placeholder="Start date">
						<input matEndDate formControlName="endDate" placeholder="End date">
					</mat-date-range-input>
					<mat-datepicker-toggle matIconSuffix [for]="createDateRangePicker"></mat-datepicker-toggle>
					<mat-date-range-picker #createDateRangePicker></mat-date-range-picker>
				</mat-form-field>
			</div>

			<div class="orll-sli-search-form__footer col-2">
				<button mat-stroked-button color="primary" (click)="onReset($event)" class="orll-sli-search-form__reset-button">
					<mat-icon>refresh</mat-icon>
					{{'sli.mgmt.reset' | translate}}
				</button>
				<button mat-flat-button color="primary" [disabled]="sliSearchForm.invalid" (click)="onSearch()">
					<mat-icon>search</mat-icon>
					{{'sli.mgmt.search' | translate}}
				</button>
			</div>
		</div>
	</form>
</div>
