.iata-breadcrumb {
	padding: 8px 16px;
	background-color: var(--iata-grey-50);
	border-radius: 4px;
	margin-bottom: 16px;

	&__list {
		display: flex;
		flex-wrap: wrap;
		padding: 0;
		margin: 0;
		list-style: none;
	}

	&__item {
		display: flex;
		align-items: center;

		&:last-child {
			.iata-breadcrumb__link {
				color: var(--iata-grey-300);
				pointer-events: none;
			}
		}
	}

	&__link {
		display: flex;
		align-items: center;
		color: var(--iata-grey-600);
		text-decoration: none;
		font-size: 14px;

		&:hover {
			text-decoration: underline;
		}
	}

	&__separator {
		margin: 0 8px;
		color: var(--iata-grey-600);
	}

	&__icon {
		font-size: 18px;
		width: 18px;
		height: 18px;
		margin-right: 4px;
	}

	&__text {
		line-height: 24px;
	}
}
