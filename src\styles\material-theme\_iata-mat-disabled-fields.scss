@mixin iata-disabled-fields-theme($theme) {
	:root {
		--iata-disabled-field-text-color: #999;
		--iata-disabled-button-background-color: #F7F8FA;
		--iata-disabled-button-text-color: #999;
		--iata-disabled-outlined-button-text-color: #999;
	}

	.mat-mdc-form-field.mat-form-field-disabled {
		.mat-mdc-text-field-wrapper {
			--mdc-outlined-text-field-disabled-label-text-color: var(--iata-disabled-field-text-color) !important;
			--mdc-outlined-text-field-disabled-input-text-color: var(--iata-disabled-field-text-color) !important;

			background-color: var(--iata-disabled-button-background-color) !important;
		}

		.mat-mdc-select-arrow {
			color: var(--iata-disabled-field-text-color) !important;
		}
	}

	.mat-mdc-icon-button.mat-mdc-button-disabled {
		color: var(--iata-disabled-field-text-color) !important;
	}

	.mdc-button.mat-mdc-button-disabled, .mat-mdc-outlined-button.mat-mdc-button-disabled {
		background-color: var(--iata-disabled-button-background-color) !important;
		color: var(--iata-disabled-button-text-color) !important;
	}
}
