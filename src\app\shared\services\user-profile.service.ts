import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { UserProfile } from '@shared/models/user-profile.model';
import { UserRole } from '@shared/models/user-role.model';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { ApiService } from './api.service';

@Injectable({ providedIn: 'root' })
export class UserProfileService extends ApiService {
	private profile: UserProfile | null = null;
	private readonly profile$ = new BehaviorSubject<UserProfile | null>(null);

	constructor(http: HttpClient) {
		super(http);
	}

	getProfile(forceReload = false): Observable<UserProfile> {
		if (this.profile && !forceReload) {
			return of(this.profile);
		}

		return super.getData<UserProfile>('user/user/current').pipe(
			tap((profile: UserProfile) => {
				this.profile = profile;
				this.profile$.next(profile);
			})
		);
	}

	changeRole(orgId: string): Observable<boolean> {
		return super.postData<boolean>('user/user/change-org', { orgId });
	}

	get currentUser(): UserProfile | null {
		return this.profile;
	}

	get currentUser$(): Observable<UserProfile | null> {
		return this.profile$.asObservable();
	}

	isSuperUser(): boolean {
		const user = this.currentUser;
		if (!user) {
			return false;
		}

		return user.userType.toString() === UserRole.SUPER_USER;
	}

	hasSomeRole(neededRoles: string[]): Observable<boolean> {
		return this.getProfile().pipe(
			map((profile: UserProfile) => {
				return neededRoles.includes(profile.orgType);
			})
		);
	}

	hasPermission(neededPermission: string, module: string): Observable<boolean> {
		return this.getProfile().pipe(
			map((profile: UserProfile) => {
				return (
					profile.permissionList?.some((permission) => {
						if (permission.module === module) {
							return permission.code.some((code) => neededPermission.includes(code));
						} else {
							return false;
						}
					}) ?? false
				);
			})
		);
	}
}
