.orll-dg-piece-in-panel {
	box-shadow: none !important;
	border: 1px solid var(--iata-grey-300);

	&__title {
		color: var(--iata-grey-600);
		font-size: 20px;
		font-weight: normal;
		margin: 20px 0;
	}

	.col-fixed-width-button {
		width: 100px;
		flex: 0 0 100px;
	}

	&__add-button {
		color: var(--iata-blue-primary) !important;
		border: 1px solid var(--iata-blue-primary);
		background-color: white;
		width: 100%;
	}
}

.dg-piece-form-textarea {
	height: 100px;
}

.orll-sli-shipper-form__title {
	color: var(--iata-blue-primary);
}

.negative-mr-1 {
	margin-right: -1px;
}

.unit {
	margin-right: 5px;
	font-size: 14px;
	color: var(--iata-black);
}

.autocomplete-arrow {
	padding: 0 5px;
}

.row {
	margin-bottom: 10px;
}

.row-mb-16 {
	margin-bottom: 16px;
}

.q-value {
	margin-top: -3px;
}
