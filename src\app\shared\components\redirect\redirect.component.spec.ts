import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, Observable } from 'rxjs';
// eslint-disable-next-line @typescript-eslint/naming-convention
import RedirectComponent from './redirect.component';
import { RouteGuardService } from '@shared/services/route-guard.service';
import { UserRole } from '@shared/models/user-role.model';

describe('RedirectComponent', () => {
	let component: RedirectComponent;
	let fixture: ComponentFixture<RedirectComponent>;
	let mockRouter: jasmine.SpyObj<Router>;
	let mockRouteGuardService: jasmine.SpyObj<RouteGuardService>;

	beforeEach(async () => {
		// Create spies for Router and RouteGuardService
		mockRouter = jasmine.createSpyObj('Router', ['navigate']);
		mockRouteGuardService = jasmine.createSpyObj('RouteGuardService', ['isSuperUser', 'hasSomeRole']);

		await TestBed.configureTestingModule({
			imports: [RedirectComponent],
			providers: [
				{ provide: Router, useValue: mockRouter },
				{ provide: RouteGuardService, useValue: mockRouteGuardService },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(RedirectComponent);
		component = fixture.componentInstance;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should call determineDefaultRoute and navigate to the returned route', () => {
			// Arrange
			const expectedRoute = 'sli';
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.returnValue(of(true));

			// Act
			component.ngOnInit();

			// Assert
			expect(mockRouteGuardService.isSuperUser).toHaveBeenCalled();
			expect(mockRouteGuardService.hasSomeRole).toHaveBeenCalledTimes(3);
			expect(mockRouter.navigate).toHaveBeenCalledWith([expectedRoute]);
		});
	});

	describe('determineDefaultRoute', () => {
		it('should return "users-mgmt" when user is super user', (done: DoneFn) => {
			// Arrange
			mockRouteGuardService.isSuperUser.and.returnValue(of(true));
			mockRouteGuardService.hasSomeRole.and.returnValue(of(false));

			// Act
			component['determineDefaultRoute']().subscribe((route) => {
				// Assert
				expect(route).toBe('users-mgmt');
				expect(mockRouteGuardService.isSuperUser).toHaveBeenCalled();
				done();
			});
		});

		it('should return "sli" when user has SLI roles but is not super user', (done: DoneFn) => {
			// Arrange
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.callFake((roles: string[]) => {
				// Return true for SLI roles, false for others
				if (roles.includes(UserRole.SHIPPER) || roles.includes(UserRole.FORWARDER)) {
					return of(true);
				}
				return of(false);
			});

			// Act
			component['determineDefaultRoute']().subscribe((route) => {
				// Assert
				expect(route).toBe('sli');
				expect(mockRouteGuardService.isSuperUser).toHaveBeenCalled();
				expect(mockRouteGuardService.hasSomeRole).toHaveBeenCalledWith([UserRole.SHIPPER, UserRole.FORWARDER]);
				done();
			});
		});

		it('should return "hawb" when user has HAWB roles but not SLI or super user roles', (done: DoneFn) => {
			// Arrange
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.callFake((roles: string[]) => {
				// Return true only for HAWB roles
				if (roles.includes(UserRole.FORWARDER) && roles.includes(UserRole.CARRIER)) {
					return of(true);
				}
				if (roles.includes(UserRole.SHIPPER) && roles.includes(UserRole.FORWARDER)) {
					return of(false); // SLI roles
				}
				return of(false);
			});

			// Act
			component['determineDefaultRoute']().subscribe((route) => {
				// Assert
				expect(route).toBe('hawb');
				expect(mockRouteGuardService.isSuperUser).toHaveBeenCalled();
				expect(mockRouteGuardService.hasSomeRole).toHaveBeenCalledWith([UserRole.SHIPPER, UserRole.FORWARDER]);
				expect(mockRouteGuardService.hasSomeRole).toHaveBeenCalledWith([UserRole.FORWARDER, UserRole.CARRIER]);
				done();
			});
		});

		it('should return "mawb" when user has MAWB roles but not SLI, HAWB, or super user roles', (done: DoneFn) => {
			// Arrange
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.callFake((roles: string[]) => {
				// Return true only for the third call (MAWB roles)
				const callCount = mockRouteGuardService.hasSomeRole.calls.count();
				if (callCount === 3 && roles.includes(UserRole.FORWARDER) && roles.includes(UserRole.CARRIER)) {
					return of(true);
				}
				return of(false);
			});

			// Act
			component['determineDefaultRoute']().subscribe((route) => {
				// Assert
				expect(route).toBe('mawb');
				expect(mockRouteGuardService.isSuperUser).toHaveBeenCalled();
				expect(mockRouteGuardService.hasSomeRole).toHaveBeenCalledTimes(3);
				done();
			});
		});

		it('should return "sli" as default when user has no specific roles', (done: DoneFn) => {
			// Arrange
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.returnValue(of(false));

			// Act
			component['determineDefaultRoute']().subscribe((route) => {
				// Assert
				expect(route).toBe('sli');
				expect(mockRouteGuardService.isSuperUser).toHaveBeenCalled();
				expect(mockRouteGuardService.hasSomeRole).toHaveBeenCalledTimes(3);
				done();
			});
		});
	});

	describe('Role Priority Tests', () => {
		it('should prioritize super user over all other roles', (done: DoneFn) => {
			// Arrange - User is super user AND has other roles
			mockRouteGuardService.isSuperUser.and.returnValue(of(true));
			mockRouteGuardService.hasSomeRole.and.returnValue(of(true)); // Has all roles

			// Act
			component['determineDefaultRoute']().subscribe((route) => {
				// Assert
				expect(route).toBe('users-mgmt');
				done();
			});
		});

		it('should prioritize SLI over HAWB and MAWB roles', (done: DoneFn) => {
			// Arrange - User has SLI, HAWB, and MAWB roles but is not super user
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.callFake(() => {
				// Return true for all role checks
				return of(true);
			});

			// Act
			component['determineDefaultRoute']().subscribe((route) => {
				// Assert
				expect(route).toBe('sli');
				done();
			});
		});

		it('should prioritize HAWB over MAWB roles when SLI is not available', (done: DoneFn) => {
			// Arrange - User has HAWB and MAWB roles but not SLI or super user
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.callFake((roles: string[]) => {
				// Return false for SLI roles, true for HAWB and MAWB
				if (roles.includes(UserRole.SHIPPER) && roles.includes(UserRole.FORWARDER)) {
					return of(false); // SLI roles
				}
				return of(true); // HAWB and MAWB roles
			});

			// Act
			component['determineDefaultRoute']().subscribe((route) => {
				// Assert
				expect(route).toBe('hawb');
				done();
			});
		});
	});

	describe('Edge Cases and Error Handling', () => {
		it('should handle errors from RouteGuardService.isSuperUser', (done: DoneFn) => {
			// Arrange
			const errorMessage = 'Super user check failed';
			mockRouteGuardService.isSuperUser.and.returnValue(
				new Observable((subscriber) => {
					subscriber.error(new Error(errorMessage));
				})
			);
			mockRouteGuardService.hasSomeRole.and.returnValue(of(false));

			// Act
			component['determineDefaultRoute']().subscribe({
				next: () => {
					fail('Should have thrown an error');
				},
				error: (error) => {
					// Assert
					expect(error.message).toBe(errorMessage);
					done();
				},
			});
		});

		it('should handle errors from RouteGuardService.hasSomeRole', (done: DoneFn) => {
			// Arrange
			const errorMessage = 'Role check failed';
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.returnValue(
				new Observable((subscriber) => {
					subscriber.error(new Error(errorMessage));
				})
			);

			// Act
			component['determineDefaultRoute']().subscribe({
				next: () => {
					fail('Should have thrown an error');
				},
				error: (error) => {
					// Assert
					expect(error.message).toBe(errorMessage);
					done();
				},
			});
		});

		it('should handle mixed success and error responses', (done: DoneFn) => {
			// Arrange
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.callFake(() => {
				// First call succeeds, second call fails
				const callCount = mockRouteGuardService.hasSomeRole.calls.count();
				if (callCount === 1) {
					return of(false);
				} else {
					return new Observable((subscriber) => {
						subscriber.error(new Error('Role check failed'));
					});
				}
			});

			// Act
			component['determineDefaultRoute']().subscribe({
				next: () => {
					fail('Should have thrown an error');
				},
				error: (error) => {
					// Assert
					expect(error.message).toBe('Role check failed');
					done();
				},
			});
		});
	});

	describe('Integration Tests', () => {
		it('should complete the full flow from ngOnInit to navigation for super user', () => {
			// Arrange
			mockRouteGuardService.isSuperUser.and.returnValue(of(true));
			mockRouteGuardService.hasSomeRole.and.returnValue(of(false));

			// Act
			component.ngOnInit();

			// Assert
			expect(mockRouteGuardService.isSuperUser).toHaveBeenCalled();
			expect(mockRouter.navigate).toHaveBeenCalledWith(['users-mgmt']);
		});

		it('should complete the full flow from ngOnInit to navigation for regular user with SLI roles', () => {
			// Arrange
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.callFake((roles: string[]) => {
				// Return true only for SLI roles
				if (roles.includes(UserRole.SHIPPER) || roles.includes(UserRole.FORWARDER)) {
					return of(true);
				}
				return of(false);
			});

			// Act
			component.ngOnInit();

			// Assert
			expect(mockRouteGuardService.isSuperUser).toHaveBeenCalled();
			expect(mockRouteGuardService.hasSomeRole).toHaveBeenCalledWith([UserRole.SHIPPER, UserRole.FORWARDER]);
			expect(mockRouter.navigate).toHaveBeenCalledWith(['sli']);
		});

		it('should complete the full flow from ngOnInit to navigation for user with no roles (default)', () => {
			// Arrange
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.returnValue(of(false));

			// Act
			component.ngOnInit();

			// Assert
			expect(mockRouteGuardService.isSuperUser).toHaveBeenCalled();
			expect(mockRouteGuardService.hasSomeRole).toHaveBeenCalledTimes(3);
			expect(mockRouter.navigate).toHaveBeenCalledWith(['sli']);
		});
	});

	describe('Role Constants Validation', () => {
		it('should use correct role constants for SLI roles', (done: DoneFn) => {
			// Arrange
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.callFake((roles: string[]) => {
				// Verify the exact roles being passed
				if (roles.length === 2 && roles.includes('SHP') && roles.includes('FFW')) {
					return of(true);
				}
				return of(false);
			});

			// Act
			component['determineDefaultRoute']().subscribe((route) => {
				// Assert
				expect(route).toBe('sli');
				expect(mockRouteGuardService.hasSomeRole).toHaveBeenCalledWith(['SHP', 'FFW']);
				done();
			});
		});

		it('should use correct role constants for HAWB roles', (done: DoneFn) => {
			// Arrange
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.callFake((roles: string[]) => {
				// Return false for SLI, true for HAWB
				if (roles.includes('SHP') && roles.includes('FFW')) {
					return of(false); // SLI roles
				}
				if (roles.length === 2 && roles.includes('FFW') && roles.includes('AIR')) {
					return of(true); // HAWB roles
				}
				return of(false);
			});

			// Act
			component['determineDefaultRoute']().subscribe((route) => {
				// Assert
				expect(route).toBe('hawb');
				expect(mockRouteGuardService.hasSomeRole).toHaveBeenCalledWith(['FFW', 'AIR']);
				done();
			});
		});

		it('should use correct role constants for MAWB roles', (done: DoneFn) => {
			// Arrange
			mockRouteGuardService.isSuperUser.and.returnValue(of(false));
			mockRouteGuardService.hasSomeRole.and.callFake((roles: string[]) => {
				// Return false for SLI and HAWB, true for MAWB
				if (roles.includes('SHP') && roles.includes('FFW')) {
					return of(false); // SLI roles
				}
				const callCount = mockRouteGuardService.hasSomeRole.calls.count();
				if (callCount === 2 && roles.includes('FFW') && roles.includes('AIR')) {
					return of(false); // HAWB roles
				}
				if (callCount === 3 && roles.includes('FFW') && roles.includes('AIR')) {
					return of(true); // MAWB roles
				}
				return of(false);
			});

			// Act
			component['determineDefaultRoute']().subscribe((route) => {
				// Assert
				expect(route).toBe('mawb');
				expect(mockRouteGuardService.hasSomeRole).toHaveBeenCalledWith(['FFW', 'AIR']);
				done();
			});
		});
	});
});
