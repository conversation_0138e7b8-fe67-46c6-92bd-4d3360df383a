import { ChangeDetectionStrategy, Component, Output, EventEmitter, ViewChildren, QueryList } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { provideNativeDateAdapter } from '@angular/material/core';
import { FormGroup, FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DatePipe, CommonModule } from '@angular/common';
import { AutocompleteComponent } from '@shared/components/autocomplete/autocomplete.component';
import { SliSearchRequestService } from '../../services/sli-search-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { SliSearchPayload } from '../../models/sli-search-payload.model';

const DATE_FORMAT = 'yyyy-MM-dd';

@Component({
	selector: 'orll-sli-search',
	templateUrl: './sli-search.component.html',
	styleUrls: ['./sli-search.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	providers: [provideNativeDateAdapter(), DatePipe],
	imports: [
		MatInputModule,
		MatIconModule,
		MatChipsModule,
		MatButtonModule,
		MatDatepickerModule,
		TranslateModule,
		ReactiveFormsModule,
		MatAutocompleteModule,
		CommonModule,
		AutocompleteComponent,
	],
})
export class SliSearchComponent extends DestroyRefComponent {
	@Output() searchSli = new EventEmitter<SliSearchPayload>();
	@ViewChildren(
		'sliNumberAutocomplete,shipperAutocomplete,consigneeAutocomplete,departureAutocomplete,arrivalAutocomplete,hawbNumberAutocomplete'
	)
	autocompleteList!: QueryList<AutocompleteComponent<any>>;

	sliSearchForm: FormGroup = new FormGroup({
		goodsDescription: new FormControl<string>(''),
		startDate: new FormControl<Date | null>(null),
		endDate: new FormControl<Date | null>(null),
	});

	selectedShippers: CodeName[] = [];
	selectedConsignees: CodeName[] = [];
	selectedDepartureLocations: CodeName[] = [];
	selectedArrivalLocations: CodeName[] = [];
	selectedSliCodes: CodeName[] = [];
	selectedHawbNumbers: CodeName[] = [];

	constructor(
		public readonly sliSearchRequestService: SliSearchRequestService,
		private readonly datePipe: DatePipe
	) {
		super();
	}

	// eslint-disable-next-line
	selectedItems(item: CodeName[], keyword: string): void {
		switch (keyword) {
			case 'shipper': {
				this.selectedShippers = item;
				break;
			}

			case 'consignee': {
				this.selectedConsignees = item;
				break;
			}

			case 'departureLocation': {
				this.selectedDepartureLocations = item;
				break;
			}

			case 'arrivalLocation': {
				this.selectedArrivalLocations = item;
				break;
			}

			case 'sliCode': {
				this.selectedSliCodes = item;
				break;
			}

			case 'hawbNumber': {
				this.selectedHawbNumbers = item;
				break;
			}

			default:
				break;
		}
	}

	onSearch(): void {
		const payload: SliSearchPayload = {
			goodsDescription: this.sliSearchForm.value.goodsDescription ?? '',
			createDateStart: this.datePipe.transform(this.sliSearchForm.value.startDate, DATE_FORMAT) ?? null,
			createDateEnd: this.datePipe.transform(this.sliSearchForm.value.endDate, DATE_FORMAT) ?? null,
			shipperNameList: this.selectedShippers.map((shipper) => shipper.name),
			consigneeNameList: this.selectedConsignees.map((consignee) => consignee.name),
			departureLocationList: this.selectedDepartureLocations.map((location) => location.code),
			arrivalLocationList: this.selectedArrivalLocations.map((location) => location.code),
			sliCodeList: this.selectedSliCodes.map((sliCode) => sliCode.code),
			hawbNumberList: this.selectedHawbNumbers.map((hawbNumber) => hawbNumber.code),
		};
		this.searchSli.emit(payload);
	}

	onReset(event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		this.sliSearchForm.reset();
		this.selectedShippers = [];
		this.selectedConsignees = [];
		this.selectedDepartureLocations = [];
		this.selectedArrivalLocations = [];
		this.selectedSliCodes = [];
		this.selectedHawbNumbers = [];
		this.autocompleteList.forEach((autocomplete) => autocomplete.eraseValue(event));
	}
}
