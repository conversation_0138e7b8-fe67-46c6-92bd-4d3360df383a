import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { MawbTableComponent } from '../../components/mawb-table/mawb-table.component';
import { MawbSearchComponent } from '../../components/mawb-search/mawb-search.component';
import { MawbSearchPayload } from '../../models/mawb-search-payload.model';
import { MawbSearchRequestService } from '../../services/mawb-search-request.service';
import { MawbListObject } from '../../models/mawb-list-object.model';

@Component({
	selector: 'orll-mawb-list',
	imports: [MawbTableComponent, SpinnerComponent, MawbSearchComponent],
	templateUrl: './mawb-list.component.html',
	styleUrl: './mawb-list.component.scss',
})
export default class MawbListComponent extends DestroyRefComponent implements OnInit {
	mawbSearchPayload!: MawbSearchPayload;
	mawbList: MawbListObject[] = [];
	mawbListTotalRecords = 0;
	dataLoading = false;
	pageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};

	constructor(
		private readonly mawbSearchRequestService: MawbSearchRequestService,
		private readonly cdr: ChangeDetectorRef
	) {
		super();
	}

	ngOnInit(): void {
		this.getMawbListPage(this.pageParams);
	}

	onSearch(mawbSearchPayload: MawbSearchPayload): void {
		this.mawbSearchPayload = mawbSearchPayload;
		this.getMawbListPage(this.pageParams);
	}

	onMawbListSortChange(sort: Sort): void {
		if (sort.direction === '') {
			this.pageParams.orderByColumn = '';
			this.pageParams.isAsc = '';
			return;
		}
		this.pageParams.orderByColumn = sort.active;
		this.pageParams.isAsc = sort.direction;
		this.getMawbListPage(this.pageParams);
	}

	onMawbListPageChange(event: PageEvent & { sortField?: string; sortDirection?: string }): void {
		this.pageParams.pageNum = event.pageIndex + 1;
		this.pageParams.pageSize = event.pageSize;
		if (event.sortDirection) {
			this.pageParams.orderByColumn = event.sortField;
			this.pageParams.isAsc = event.sortDirection;
		}

		this.getMawbListPage(this.pageParams);
	}

	private getMawbListPage(pageParams: PaginationRequest): void {
		this.dataLoading = true;
		this.mawbList = [];

		this.mawbSearchRequestService
			.getMawbList(pageParams, this.mawbSearchPayload)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.mawbList = res.rows;
					this.mawbListTotalRecords = res.total;
					this.dataLoading = false;
					this.cdr.markForCheck();
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}
}
