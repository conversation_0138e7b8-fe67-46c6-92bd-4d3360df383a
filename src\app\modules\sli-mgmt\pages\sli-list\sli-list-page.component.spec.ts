import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SliSearchRequestService } from '../../services/sli-search-request.service';
import { of, throwError } from 'rxjs';
import { PageEvent } from '@angular/material/paginator';
import { PaginationResponse } from '@shared/models/pagination-response.model';
// eslint-disable-next-line @typescript-eslint/naming-convention
import SliListPageComponent from './sli-list-page.component';
import { TranslateModule } from '@ngx-translate/core';
import { ActivatedRoute } from '@angular/router';
import { SliSearchPayload } from '../../models/sli-search-payload.model';
import { Sort } from '@angular/material/sort';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { UserProfileService } from '@shared/services/user-profile.service';

describe('SliListPageComponent', () => {
	let component: SliListPageComponent;
	let fixture: ComponentFixture<SliListPageComponent>;
	let sliSearchRequestServiceSpy: jasmine.SpyObj<SliSearchRequestService>;
	let cdrSpy: jasmine.SpyObj<ChangeDetectorRef>;
	let mockSliRecords: any[];
	let mockPaginationResponse: PaginationResponse<any>;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;

	beforeEach(() => {
		// Create spies for all required services
		sliSearchRequestServiceSpy = jasmine.createSpyObj('SliSearchRequestService', ['getSliList', 'getSharedSliList']);
		cdrSpy = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

		// Setup mock data
		mockSliRecords = [{ id: 1 }, { id: 2 }, { id: 3 }];
		mockPaginationResponse = {
			rows: mockSliRecords,
			total: mockSliRecords.length,
			pageNum: 1,
		} as PaginationResponse<any>;

		// Configure default spy behavior
		sliSearchRequestServiceSpy.getSliList.and.returnValue(of(mockPaginationResponse));
		sliSearchRequestServiceSpy.getSharedSliList.and.returnValue(of(mockPaginationResponse));
	});

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [SliListPageComponent, TranslateModule.forRoot()],
			providers: [
				{
					provide: ActivatedRoute,
					useClass: class ActivatedRouteMock {},
				},
				{
					provide: SliSearchRequestService,
					useValue: sliSearchRequestServiceSpy,
				},
				{
					provide: ChangeDetectorRef,
					useValue: cdrSpy,
				},
				{
					provide: UserProfileService,
					useValue: userProfileServiceSpy,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA],
		}).compileComponents();

		fixture = TestBed.createComponent(SliListPageComponent);
		component = fixture.componentInstance;
		component['sliSearchPayload'] = {} as SliSearchPayload;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should retrieve the sli list page with default pagination parameters', () => {
			spyOn<SliListPageComponent, any>(component, 'getSliListPage');

			component.ngOnInit();

			expect(component['getSliListPage']).toHaveBeenCalledWith({ pageNum: 1, pageSize: 10 });
		});
	});

	describe('onSliListPageChange', () => {
		it('should update pagination parameters and retrieve the requested page', () => {
			spyOn<SliListPageComponent, any>(component, 'getSliListPage');
			const pageEvent: PageEvent = { pageIndex: 1, previousPageIndex: 0, pageSize: 10, length: 30 };

			component.onSliListPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(2);
			expect(component.pageParams.pageSize).toBe(10);
			expect(component['getSliListPage']).toHaveBeenCalledWith({ pageNum: 2, pageSize: 10 });
		});

		it('should update pagination and sort parameters when sort direction is provided', () => {
			spyOn<SliListPageComponent, any>(component, 'getSliListPage');
			const pageEvent: PageEvent & { sortField?: string; sortDirection?: string } = {
				pageIndex: 0,
				previousPageIndex: 0,
				pageSize: 20,
				length: 30,
				sortField: 'updatedAt',
				sortDirection: 'desc',
			};

			component.onSliListPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(1);
			expect(component.pageParams.pageSize).toBe(20);
			expect(component.pageParams.orderByColumn).toBe('updatedAt');
			expect(component.pageParams.isAsc).toBe('desc');
			expect(component['getSliListPage']).toHaveBeenCalledWith({
				pageNum: 1,
				pageSize: 20,
				orderByColumn: 'updatedAt',
				isAsc: 'desc',
			});
		});
	});

	describe('onSearch', () => {
		it('should update search payload and retrieve sli list with current pagination', () => {
			spyOn<SliListPageComponent, any>(component, 'getSliListPage');
			const mockSearchPayload: SliSearchPayload = { searchTerm: 'test' } as SliSearchPayload;

			component.onSearch(mockSearchPayload);

			expect(component.sliSearchPayload).toBe(mockSearchPayload);
			expect(component['getSliListPage']).toHaveBeenCalledWith(component.pageParams);
		});
	});

	describe('onSliListSortChange', () => {
		it('should update sort parameters when sort direction is provided', () => {
			const sortEvent: Sort = { active: 'createdAt', direction: 'asc' };

			component.onSliListSortChange(sortEvent);

			expect(component.pageParams.orderByColumn).toBe('createdAt');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should clear sort parameters when sort direction is empty', () => {
			// First set some values
			component.pageParams.orderByColumn = 'createdAt';
			component.pageParams.isAsc = 'asc';

			const sortEvent: Sort = { active: 'createdAt', direction: '' };

			component.onSliListSortChange(sortEvent);

			expect(component.pageParams.orderByColumn).toBe('');
			expect(component.pageParams.isAsc).toBe('');
		});

		it('should update sort parameters when sort direction is desc', () => {
			spyOn<SliListPageComponent, any>(component, 'getSliListPage');
			const sortEvent: Sort = { active: 'updatedAt', direction: 'desc' };

			component.onSliListSortChange(sortEvent);

			expect(component.pageParams.orderByColumn).toBe('updatedAt');
			expect(component.pageParams.isAsc).toBe('desc');
			// Verify getSliListPage is not called directly by onSliListSortChange
			expect(component['getSliListPage']).not.toHaveBeenCalled();
		});

		it('should not call getSliListPage after updating sort parameters', () => {
			// Setup
			spyOn<SliListPageComponent, any>(component, 'getSliListPage');
			const sortEvent: Sort = { active: 'createdAt', direction: 'asc' };

			// Execute
			component.onSliListSortChange(sortEvent);

			// Verify
			expect(component['getSliListPage']).not.toHaveBeenCalled();
			// Note: This test documents the current behavior. If the component is updated to call
			// getSliListPage after updating sort parameters, this test should be updated.
		});
	});

	describe('sliSearchRequest', () => {
		it('should call getSliList when fromSli is empty', () => {
			// Setup
			component.fromCreateHawb = false;
			const pageParams = { pageNum: 1, pageSize: 10 };
			const searchPayload = { searchTerm: 'test' } as SliSearchPayload;

			// Execute
			component['sliSearchRequest'](pageParams, searchPayload);

			// Verify
			expect(sliSearchRequestServiceSpy.getSliList).toHaveBeenCalledWith(pageParams, searchPayload);
			expect(sliSearchRequestServiceSpy.getSharedSliList).not.toHaveBeenCalled();
		});

		it('should call getSharedSliList when fromSli has a value', () => {
			// Setup
			component.fromCreateHawb = true;
			const pageParams = { pageNum: 1, pageSize: 10 };
			const searchPayload = { searchTerm: 'test' } as SliSearchPayload;

			// Execute
			component['sliSearchRequest'](pageParams, searchPayload);

			// Verify
			expect(sliSearchRequestServiceSpy.getSharedSliList).toHaveBeenCalledWith(pageParams, searchPayload);
			expect(sliSearchRequestServiceSpy.getSliList).not.toHaveBeenCalled();
		});
	});

	describe('getSliListPage', () => {
		it('should set dataLoading to true and clear sliList before making the request', () => {
			// Setup
			component.dataLoading = false;
			component.sliList = [{} as any];

			// We need to modify the spy to capture the state before the observable completes
			sliSearchRequestServiceSpy.getSliList.and.callFake(() => {
				// Check state right after the method call but before observable completes
				expect(component.dataLoading).toBeTrue();
				expect(component.sliList.length).toBe(0);
				return of(mockPaginationResponse);
			});

			// Execute
			component['getSliListPage']({ pageNum: 1, pageSize: 10 });

			// Verify
			expect(sliSearchRequestServiceSpy.getSliList).toHaveBeenCalled();
		});

		it('should retrieve a page of SLI list and update component properties on success', () => {
			// Execute
			component['getSliListPage']({ pageNum: 1, pageSize: 10 });

			// Verify
			expect(sliSearchRequestServiceSpy.getSliList).toHaveBeenCalledWith({ pageNum: 1, pageSize: 10 }, {});
			expect(component.sliList).toEqual(mockSliRecords);
			expect(component.sliListTotalRecords).toEqual(mockSliRecords.length);
			expect(component.dataLoading).toBeFalse();
		});

		it('should call getSharedSliList when fromSli is set', () => {
			// Setup
			component.fromCreateHawb = true;
			sliSearchRequestServiceSpy.getSharedSliList = jasmine.createSpy().and.returnValue(of(mockPaginationResponse));

			// Execute
			component['getSliListPage']({ pageNum: 1, pageSize: 10 });

			// Verify
			expect(sliSearchRequestServiceSpy.getSharedSliList).toHaveBeenCalledWith({ pageNum: 1, pageSize: 10 }, {});
			expect(sliSearchRequestServiceSpy.getSliList).not.toHaveBeenCalled();
			expect(component.sliList).toEqual(mockSliRecords);
			expect(component.sliListTotalRecords).toEqual(mockSliRecords.length);
			expect(component.dataLoading).toBeFalse();
		});

		it('should handle errors and display a snackbar notification', () => {
			const errorResponse = { message: 'something went wrong' };
			sliSearchRequestServiceSpy.getSliList.and.returnValue(throwError(() => errorResponse));

			component['getSliListPage']({ pageNum: 1, pageSize: 10 });

			expect(component.dataLoading).toBeFalse();
		});

		it('should handle errors from getSharedSliList and display a notification', () => {
			// Setup
			component.fromCreateHawb = true;
			const errorResponse = { message: 'shared list error' };
			sliSearchRequestServiceSpy.getSharedSliList = jasmine.createSpy().and.returnValue(throwError(() => errorResponse));

			// Execute
			component['getSliListPage']({ pageNum: 1, pageSize: 10 });

			// Verify
			expect(component.dataLoading).toBeFalse();
		});
	});
});
