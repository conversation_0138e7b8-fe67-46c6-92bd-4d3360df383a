import { ChangeDetectionStrategy, Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { FormControl, FormsModule, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslatePipe } from '@ngx-translate/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { PieceItemPanelComponent } from '../../components/piece-item-panel/piece-item-panel.component';
import { PieceItem } from '../../models/piece/piece-item.model';
import { CodeName } from '@shared/models/code-name.model';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { Piece } from '../../models/piece/piece.model';
import { startWith, tap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';

const REGEX_ONE_NUMBER = /^\d+(\.\d)?$/;

@Component({
	selector: 'orll-dg-piece-add',
	imports: [
		CommonModule,
		ReactiveFormsModule,
		TranslatePipe,
		MatFormFieldModule,
		MatIconModule,
		MatInputModule,
		MatExpansionModule,
		MatButtonModule,
		MatSelectModule,
		PieceItemPanelComponent,
		MatAutocompleteModule,
		FormsModule,
	],
	templateUrl: './dg-piece-add.component.html',
	changeDetection: ChangeDetectionStrategy.OnPush,
	styleUrl: './dg-piece-add.component.scss',
})
export class DgPieceAddComponent extends DestroyRefComponent implements OnInit, OnChanges {
	@Input() piece: Piece | null = null;

	typeOfPackage = '';

	sliDgPieceForm = this.fb.group({
		productDescription: ['', Validators.required],
		typeOfPackage: this.fb.group({
			typeCode: ['', Validators.required],
			description: [''],
		}),
		packagedIdentifier: [], //optional
		whetherHaveDeclaredValueForCustoms: [true],
		whetherHaveDeclaredValueForCarriage: [true],
		specialProvisionId: [],
		explosiveCompatibilityGroupCode: [],
		packagingDangerLevelCode: [],
		technicalName: [],
		unNumber: [],
		shippersDeclaration: [],
		handlingInformation: [],
		allPackedInOne: [true], // optional
		qValueNumeric: new FormControl<number | null>(null, [Validators.required]),
		upid: [], //optional
		shippingMarks: [], //optional
		grossWeight: this.fb.control<string | null>(null, [Validators.pattern(REGEX_ONE_NUMBER)]),
		dimensions: this.fb.group({
			length: this.fb.control<string | null>(null, [Validators.required, Validators.pattern(REGEX_ONE_NUMBER)]),
			width: this.fb.control<string | null>(null, [Validators.required, Validators.pattern(REGEX_ONE_NUMBER)]),
			height: this.fb.control<string | null>(null, [Validators.required, Validators.pattern(REGEX_ONE_NUMBER)]),
		}),
		hsCommodityDescription: [],
		properShippingName: [],
		textualHandlingInstructions: [],
		hazardClassificationId: [],
		additionalHazardClassificationId: [],
		packingInstructionNumber: [],
		complianceDeclaration: [],
		exclusiveUseIndicator: [],
		authorizationInformation: [],
		aircraftLimitationInformation: [],
	});

	dgPieceItems: PieceItem[] = [];

	packagingTypes: CodeName[] = [];
	filteredPackagingTypes: CodeName[] = [];

	constructor(
		private readonly fb: NonNullableFormBuilder,
		public readonly sliCreateRequestService: SliCreateRequestService
	) {
		super();
	}

	ngOnInit() {
		this.sliCreateRequestService.getPackingTypes().subscribe((packagingTypes: CodeName[]) => {
			this.packagingTypes = packagingTypes;
			this.filteredPackagingTypes = packagingTypes;
		});

		this.sliDgPieceForm
			.get(['typeOfPackage', 'typeCode'])
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredPackagingTypes = this.packagingTypes.filter((packagingTypes) =>
					packagingTypes.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
				);
			});

		this.sliDgPieceForm
			.get('allPackedInOne')
			?.valueChanges.pipe(
				takeUntilDestroyed(this.destroyRef),
				tap((result: boolean) => {
					const qValueNumericItem = this.sliDgPieceForm.get('qValueNumeric');
					if (!result) {
						this.sliDgPieceForm.setControl('qValueNumeric', new FormControl<number | null>(qValueNumericItem?.value ?? null));
					} else {
						this.sliDgPieceForm.setControl(
							'qValueNumeric',
							new FormControl<number | null>(qValueNumericItem?.value ?? null, [Validators.required])
						);
					}
				})
			)
			.subscribe();
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes['piece']) {
			if (this.piece) {
				this.patchDgPieceForm(this.piece);
			} else {
				this.sliDgPieceForm.reset();
			}
		}
	}

	displayPackagingTypeName = (code: string): string => {
		const type = this.packagingTypes.find((item) => item.code === code);
		const name = type?.name ?? '';
		return name;
	};

	onPackagingTypeSelected(event: MatAutocompleteSelectedEvent) {
		const type = this.packagingTypes.find((item) => item.code === event.option.value);
		const name = type?.name ?? '';
		this.sliDgPieceForm.get('typeOfPackage')?.patchValue({
			typeCode: event.option.value,
			description: name,
		});
	}

	private patchDgPieceForm(piece: Piece) {
		this.sliDgPieceForm.patchValue(
			{
				productDescription: piece.productDg?.description,
				typeOfPackage: {
					typeCode: piece.packagingType.typeCode,
					description: piece.packagingType.description,
				},
				packagedIdentifier: piece.packagedIdentifier as any,
				whetherHaveDeclaredValueForCustoms: piece.nvdForCustoms,
				whetherHaveDeclaredValueForCarriage: piece.nvdForCarriage,
				specialProvisionId: piece.productDg?.specialProvisionId as any,
				explosiveCompatibilityGroupCode: piece.productDg?.explosiveCompatibilityGroupCode as any,
				packagingDangerLevelCode: piece.productDg?.packagingDangerLevelCode as any,
				technicalName: piece.productDg?.technicalName as any,
				unNumber: piece.productDg?.unNumber as any,
				shippersDeclaration: piece.dgDeclaration?.shipperDeclarationText as any,
				handlingInformation: piece.dgDeclaration?.handlingInformation as any,
				complianceDeclaration: piece.dgDeclaration?.complianceDeclarationText as any,
				exclusiveUseIndicator: piece.dgDeclaration?.exclusiveUseIndicator as any,
				aircraftLimitationInformation: piece.dgDeclaration?.aircraftLimitationInformation as any,
				allPackedInOne: piece.allPackedInOneIndicator as boolean,
				qValueNumeric: piece.qvalueNumeric as number,
				upid: piece.upid as any,
				shippingMarks: piece.shippingMarks as any,
				grossWeight: piece.grossWeight as any,
				dimensions: {
					length: piece.dimensions?.length as any,
					width: piece.dimensions?.width as any,
					height: piece.dimensions?.height as any,
				},
				hsCommodityDescription: piece.productDg?.hsCommodityDescription as any,
				properShippingName: piece.productDg?.properShippingName as any,
				textualHandlingInstructions: piece.textualHandlingInstructions as any,
				hazardClassificationId: piece.productDg?.hazardClassificationId as any,
				additionalHazardClassificationId: piece.productDg?.additionalHazardClassificationId as any,
				packingInstructionNumber: piece.productDg?.packingInstructionNumber as any,
				authorizationInformation: piece.productDg?.authorizationInformation as any,
			},
			{
				emitEvent: false,
			}
		);

		const { containedItems } = piece;
		if (Array.isArray(containedItems)) {
			this.dgPieceItems = containedItems;
		}
	}

	hasValidTouchedFormData() {
		return this.sliDgPieceForm.touched && this.sliDgPieceForm.valid;
	}
}
