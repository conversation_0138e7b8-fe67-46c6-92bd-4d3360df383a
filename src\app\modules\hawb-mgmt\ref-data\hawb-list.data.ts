import { HawbListObject } from '../models/hawb-list-object.model';

export const HAWB_LIST: HawbListObject[] = [
	{
		hawbNumber: 'H000920',
		shipper: 'Shipper A',
		consignee: 'Consignee A',
		goodsDescription: 'Goods A',
		origin: 'DEP A',
		destination: 'ARR A',
		mawbNumber: 'M1',
		createDate: '2023-04-13',
		orgId: '123',
		sliId: 'S000001',
		hawbId: 'H000001',
		mawbId: 'M000001',
	},
	{
		hawbNumber: 'S000921',
		shipper: 'Shipper B',
		consignee: 'Consignee B',
		goodsDescription: 'Goods B',
		origin: 'DEP B',
		destination: 'ARR B',
		mawbNumber: 'M2',
		createDate: '2023-04-14',
		orgId: '123',
		sliId: 'S000002',
		hawbId: 'H000002',
		mawbId: 'M000002',
	},
];
