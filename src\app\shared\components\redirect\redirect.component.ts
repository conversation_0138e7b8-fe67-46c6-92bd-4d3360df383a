import { Component, inject, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { RouteGuardService } from '@shared/services/route-guard.service';
import { UserRole } from '@shared/models/user-role.model';
import { forkJoin } from 'rxjs';
import { map } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DestroyRefComponent } from '../destroy-observable/destroy-ref.component';

const MENU_SLI_ROLES: string[] = [UserRole.SHIPPER, UserRole.FORWARDER];
const MENU_HAWB_ROLES: string[] = [UserRole.FORWARDER, UserRole.CARRIER];
const MENU_MAWB_ROLES: string[] = [UserRole.FORWARDER, UserRole.CARRIER];

@Component({
	selector: 'orll-app-redirect',
	template: '<div class="flex justify-center items-center h-screen"><div>Redirecting...</div></div>',
	standalone: true,
})
export default class RedirectComponent extends DestroyRefComponent implements OnInit {
	private readonly router = inject(Router);
	private readonly routeGuardService = inject(RouteGuardService);

	ngOnInit(): void {
		this.determineDefaultRoute()
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((defaultRoute) => {
				this.router.navigate([defaultRoute]);
			});
	}

	private determineDefaultRoute() {
		// Check roles in priority order using observables
		return forkJoin({
			isSuperUser: this.routeGuardService.isSuperUser(),
			hasSliRoles: this.routeGuardService.hasSomeRole(MENU_SLI_ROLES),
			hasHawbRoles: this.routeGuardService.hasSomeRole(MENU_HAWB_ROLES),
			hasMawbRoles: this.routeGuardService.hasSomeRole(MENU_MAWB_ROLES),
		}).pipe(
			map(({ isSuperUser, hasMawbRoles, hasHawbRoles, hasSliRoles }) => {
				// Priority order: SuperUser > SLI > HAWB > MAWB > Default
				if (isSuperUser) {
					return 'users-mgmt';
				} else if (hasSliRoles) {
					return 'sli';
				} else if (hasHawbRoles) {
					return 'hawb';
				} else if (hasMawbRoles) {
					return 'mawb';
				}

				return 'sli';
			})
		);
	}
}
