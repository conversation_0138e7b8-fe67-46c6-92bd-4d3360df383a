@mixin iata-expansion-panel-theme($theme) {
	.mat-expansion-panel {
		--mat-expansion-container-background-color: var(--iata-grey-50);
		--mat-expansion-header-indicator-color: var(--iata-blue-primary);

		box-shadow: none !important;

		&.iata-expansion-panel--warn {
			background-color: rgb(from var(--iata-yellow-primary) r g b / 0.15);
		}
		&.iata-expansion-panel--dark {
			--mat-expansion-header-collapsed-state-height: 30px;
			--mat-expansion-header-expanded-state-height: 30px;
			--mat-expansion-container-background-color: #cccccc;
			--mat-expansion-header-indicator-color: var(--iata-grey-500);
			--mat-expansion-header-text-color: var(--iata-grey-500);
			--mat-expansion-header-text-size: 12px;

			margin-bottom: 2px;

			.mat-expansion-panel-content {
				background-color: var(--iata-grey-50);

				.mat-expansion-panel-body {
					padding: 0;
				}
			}
		}
	}
}
