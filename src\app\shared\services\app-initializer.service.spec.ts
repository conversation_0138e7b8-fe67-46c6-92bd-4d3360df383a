import { TestBed } from '@angular/core/testing';
import { AppInitializerService } from './app-initializer.service';
import { AuthService } from '@shared/auth/auth.service';
import { of, throwError } from 'rxjs';

describe('AppInitializerService', () => {
	let service: AppInitializerService;
	let authServiceSpy: jasmine.SpyObj<AuthService>;

	beforeEach(() => {
		const spy = jasmine.createSpyObj('AuthService', ['login', 'isLoggedIn']);

		TestBed.configureTestingModule({
			providers: [AppInitializerService, { provide: AuthService, useValue: spy }],
		});

		service = TestBed.inject(AppInitializerService);
		authServiceSpy = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	describe('initializeApp', () => {
		it('should return true when already logged in', async () => {
			// Arrange
			authServiceSpy.isLoggedIn.and.returnValue(true);

			// Act
			const result = await service.initializeApp();

			// Assert
			expect(result).toBe(true);
			expect(authServiceSpy.isLoggedIn).toHaveBeenCalled();
			expect(authServiceSpy.login).not.toHaveBeenCalled();
		});

		it('should call login and return true when login succeeds', async () => {
			// Arrange
			authServiceSpy.isLoggedIn.and.returnValue(false);
			authServiceSpy.login.and.returnValue(of({ data: 'token123' }));

			// Act
			const result = await service.initializeApp();

			// Assert
			expect(result).toBe(true);
			expect(authServiceSpy.isLoggedIn).toHaveBeenCalled();
			expect(authServiceSpy.login).toHaveBeenCalled();
		});

		it('should call login and return false when login fails', async () => {
			// Arrange
			authServiceSpy.isLoggedIn.and.returnValue(false);
			authServiceSpy.login.and.returnValue(throwError(() => new Error('Login failed')));

			// Act
			const result = await service.initializeApp();

			// Assert
			expect(result).toBe(false);
			expect(authServiceSpy.isLoggedIn).toHaveBeenCalled();
			expect(authServiceSpy.login).toHaveBeenCalled();
		});

		it('should pass userId and orgId from environment to login method', async () => {
			// Arrange
			authServiceSpy.isLoggedIn.and.returnValue(false);
			authServiceSpy.login.and.returnValue(of({ data: 'token123' }));

			// Act
			await service.initializeApp();

			// Assert
			expect(authServiceSpy.login).toHaveBeenCalledWith({
				userId: jasmine.any(String),
				orgId: jasmine.any(String),
			});
		});
	});
});
