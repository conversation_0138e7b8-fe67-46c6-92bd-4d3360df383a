import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { CurrencyInputComponent } from '@shared/components/currency-input/currency-input.component';
import { MatSelectModule } from '@angular/material/select';
import { EnumCodeTypeModel } from '@shared/components/enum-code-form-item/enum-code-type.model';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { CodeName } from '@shared/models/code-name.model';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { startWith } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { EmitCharges, OtherCharges } from '../../../models/other-charges.model';

const CHARGE_CODE = 'GT,ST,TA,TB,TC,TD,TE,TI,TV,TX';

@Component({
	selector: 'orll-other-charges',
	imports: [
		TranslateModule,
		ReactiveFormsModule,
		MatFormFieldModule,
		MatInputModule,
		MatButtonModule,
		MatIconModule,
		MatDividerModule,
		CurrencyInputComponent,
		MatSelectModule,
		MatAutocompleteModule,
	],
	templateUrl: './other-charges.component.html',
	styleUrl: './other-charges.component.scss',
})
export class OtherChargesComponent extends DestroyRefComponent implements OnInit {
	protected readonly enumCodeTypeModel = EnumCodeTypeModel;

	@Input()
	currencies: string[] = [];
	isDisabled = false;

	@Output()
	otherChargesChange = new EventEmitter<EmitCharges[]>();

	otherChargesForm = this.fb.group({
		chargePaymentType: [null, Validators.required],
		entitlement: ['', Validators.required],
		otherChargeCode: ['', Validators.required],
		otherChargeAmount: new FormGroup(
			{
				currencyUnit: new FormControl<string | null>(null),
				numericalValue: new FormControl<number | null>(null, [Validators.required, Validators.pattern('^\\d+(\\.\\d{1,2})?$')]),
			},
			[Validators.required]
		),
	});

	otherChargesList: OtherCharges[] = [];

	allEntitlements: CodeName[] = [];
	allOtherCharges: CodeName[] = [];
	filteredOtherCharges: CodeName[] = [];

	constructor(
		private readonly fb: NonNullableFormBuilder,
		private readonly dialog: MatDialog,
		private readonly translateService: TranslateService,
		private readonly mgmtRequestService: OrgMgmtRequestService
	) {
		super();
	}

	ngOnInit(): void {
		this.initRefData();
		this.setupAutocomplete();
	}

	enumRequest(enumType: EnumCodeTypeModel): void {
		this.mgmtRequestService.getEnumCode(enumType).subscribe((res: CodeName[]) => {
			if (enumType === this.enumCodeTypeModel.ENTITLEMENT_CODE) {
				this.allEntitlements = res;
			} else {
				this.allOtherCharges = res;
				this.filteredOtherCharges = res;
			}
		});
	}

	private initRefData(): void {
		this.enumRequest(this.enumCodeTypeModel.ENTITLEMENT_CODE);
		this.enumRequest(this.enumCodeTypeModel.OTHER_CHARGE_CODE);
	}

	private setupAutocomplete(): void {
		this.otherChargesForm
			.get('otherChargeCode')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredOtherCharges = this.allOtherCharges.filter((item) =>
					item.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
				);
			});
	}

	displayOtherChargesName = (code: string): string => {
		const type = this.allOtherCharges.find((item) => item.code === code);
		return type?.name ?? '';
	};

	onOtherChargeSelect(event: MatAutocompleteSelectedEvent) {
		const selectedCharge = event.option.value;
		if (CHARGE_CODE.includes(selectedCharge)) {
			this.otherChargesForm.get('entitlement')?.patchValue('');
			this.otherChargesForm.get('entitlement')?.disable();
		} else {
			this.otherChargesForm.get('entitlement')?.enable();
		}
	}

	addOtherCharges() {
		this.otherChargesForm.markAllAsTouched();
		if (this.otherChargesForm.invalid) {
			this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translateService.instant('common.dialog.form.validate'),
				},
			});

			return;
		}
		this.otherChargesList.push({
			chargePaymentType: this.otherChargesForm.get('chargePaymentType')?.value ?? '',
			entitlement: this.otherChargesForm.get('entitlement')?.value ?? '',
			otherChargeCode: this.otherChargesForm.get('otherChargeCode')?.value ?? '',
			otherChargeAmount: {
				currencyUnit: this.otherChargesForm.get('otherChargeAmount')?.get('currencyUnit')?.value ?? '',
				numericalValue: Number(this.otherChargesForm.get('otherChargeAmount')?.get('numericalValue')?.value),
			},
		});

		this.otherChargesForm.reset();
		this.emitOtherCharges();
	}

	deleteOtherCharge(index: number) {
		this.otherChargesList.splice(index, 1);
		this.emitOtherCharges();
	}

	public initOtherCharges(otherChargesList: OtherCharges[]): void {
		this.otherChargesList = otherChargesList;
		this.emitOtherCharges();
	}

	private emitOtherCharges(): void {
		let taxChargesPrepaid = 0;
		let agentChargesPrepaid = 0;
		let carrierChargesPrepaid = 0;
		let taxChargesCollect = 0;
		let agentChargesCollect = 0;
		let carrierChargesCollect = 0;
		const emitCharges: EmitCharges[] = [];

		this.otherChargesList.forEach((item) => {
			if (item.chargePaymentType === DropDownType.PREPAID) {
				if (CHARGE_CODE.includes(item.otherChargeCode)) {
					taxChargesPrepaid += item.otherChargeAmount.numericalValue ?? 0;
				} else if (item.entitlement === DropDownType.ENTITLEMENT_A) {
					agentChargesPrepaid += item.otherChargeAmount.numericalValue ?? 0;
				} else {
					carrierChargesPrepaid += item.otherChargeAmount.numericalValue ?? 0;
				}
				emitCharges.push({
					chargePaymentType: item.chargePaymentType,
					taxCharges: taxChargesPrepaid,
					agentCharges: agentChargesPrepaid,
					carrierCharges: carrierChargesPrepaid,
				});
			} else {
				if (CHARGE_CODE.includes(item.otherChargeCode)) {
					taxChargesCollect += item.otherChargeAmount.numericalValue ?? 0;
				} else if (item.entitlement === DropDownType.ENTITLEMENT_A) {
					agentChargesCollect += item.otherChargeAmount.numericalValue ?? 0;
				} else {
					carrierChargesCollect += item.otherChargeAmount.numericalValue ?? 0;
				}
				emitCharges.push({
					chargePaymentType: item.chargePaymentType,
					taxCharges: taxChargesCollect,
					agentCharges: agentChargesCollect,
					carrierCharges: carrierChargesCollect,
				});
			}
		});

		this.otherChargesChange.emit(emitCharges);
	}
}
