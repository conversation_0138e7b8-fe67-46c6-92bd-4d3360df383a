import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CarrierAgentComponent } from './carrier-agent.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';

describe('CarrierAgentComponent', () => {
	let component: CarrierAgentComponent;
	let fixture: ComponentFixture<CarrierAgentComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [CarrierAgentComponent],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting(), provideTranslateService()],
		}).compileComponents();

		fixture = TestBed.createComponent(CarrierAgentComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
