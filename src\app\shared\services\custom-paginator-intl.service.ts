import { Injectable } from '@angular/core';
import { MatPaginatorIntl } from '@angular/material/paginator';
import { TranslateService } from '@ngx-translate/core';

@Injectable()
export class CustomPaginatorIntl extends MatPaginatorIntl {
	constructor(private readonly translate: TranslateService) {
		super();

		// Initialize with translations
		this.translateLabels();

		// Update labels when language changes
		this.translate.onLangChange.subscribe(() => {
			this.translateLabels();
		});
	}

	translateLabels(): void {
		this.itemsPerPageLabel = this.translate.instant('pagination.itemsPerPage');
		this.nextPageLabel = this.translate.instant('pagination.nextPage');
		this.previousPageLabel = this.translate.instant('pagination.previousPage');
		this.firstPageLabel = this.translate.instant('pagination.firstPage');
		this.lastPageLabel = this.translate.instant('pagination.lastPage');

		this.changes.next();
	}
}
