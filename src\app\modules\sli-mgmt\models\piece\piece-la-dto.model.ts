export interface PieceLaDto {
	id?: string
	type: string
	packagingType: PackagingType
	nvdForCustoms: boolean
	nvdForCarriage: boolean
	grossWeight: number
	upid: string
	dimensions: Dimensions
	packagedIdentifier: string
	textualHandlingInstructions: string
	shippingMarks: string
	pieceQuantity: number
	sliNumber: string
	slac: number
	product: Product
	quantityAnimals: string
	speciesCommonName: string
	speciesScientificName: string
	specimenDescription: string
}

export interface PackagingType {
	typeCode: string
	description: string
}

export interface Dimensions {
	length: number
	width: number
	height: number
}

export interface Product {
	description: string
	hsCommodityDescription: string
}
