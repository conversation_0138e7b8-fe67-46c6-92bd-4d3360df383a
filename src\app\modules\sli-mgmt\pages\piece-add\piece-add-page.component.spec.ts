import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import PieceAddPageComponent from './piece-add-page.component';
import { Router, RouterModule } from '@angular/router';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { By } from '@angular/platform-browser';
import { SliPieceFormComponent } from '../../components/sli-piece-form/sli-piece-form.component';
import { SliPieceItemComponent } from '../../components/sli-piece-item/sli-piece-item.component';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { ChangeDetectorRef } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { NotificationService } from '@shared/services/notification.service';
import { Observable, of, throwError } from 'rxjs';
import { Piece } from '../../models/piece/piece.model';

describe('PieceAddPageComponent', () => {
	let component: PieceAddPageComponent;
	let fixture: ComponentFixture<PieceAddPageComponent>;

	// Mocks
	let mockRouter: jasmine.SpyObj<Router>;
	let mockSliCreateRequestService: jasmine.SpyObj<SliCreateRequestService>;
	let mockNotificationService: jasmine.SpyObj<NotificationService>;
	let mockDialog: jasmine.SpyObj<MatDialog>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;

	beforeEach(async () => {
		// Create spies
		mockRouter = jasmine.createSpyObj('Router', ['navigate']);
		mockSliCreateRequestService = jasmine.createSpyObj('SliCreateRequestService', [
			'createPiece',
			'updatePiece',
			'getPieceDetail',
			'getPackingTypes',
			'createDgPiece',
			'updateDgPiece',
			'createLaPiece',
			'updateLaPiece',
		]);
		mockNotificationService = jasmine.createSpyObj('NotificationService', ['showError', 'showSuccess']);
		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
		mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

		// Configure mock responses
		mockSliCreateRequestService.getPackingTypes.and.returnValue(of([]));
		mockDialog.open.and.returnValue({
			afterClosed: () => of(true),
		} as any);

		await TestBed.configureTestingModule({
			imports: [
				PieceAddPageComponent,
				TranslateModule.forRoot(),
				ReactiveFormsModule,
				NoopAnimationsModule,
				MatFormFieldModule,
				MatInputModule,
				MatButtonModule,
				MatIconModule,
				SliPieceFormComponent,
				SliPieceItemComponent,
				RouterModule.forRoot([], {
					useHash: true,
					bindToComponentInputs: true,
				}),
			],
			providers: [
				{ provide: Router, useValue: mockRouter },
				{ provide: SliCreateRequestService, useValue: mockSliCreateRequestService },
				{ provide: NotificationService, useValue: mockNotificationService },
				{ provide: MatDialog, useValue: mockDialog },
				{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(PieceAddPageComponent);
		component = fixture.componentInstance;

		// Set required inputs
		component.pieceType = 'general';
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize with default values', async () => {
		component.pieceType = 'general';
		fixture.detectChanges();

		// Check default values
		expect(component.pieceType).toBe('general');
		expect(component.pieceQuantity.value).toBe(1);
	});

	it('should validate pieceQuantity with positive number pattern', () => {
		// Invalid values
		component.pieceQuantity.setValue(Number('abc'));
		expect(component.pieceQuantity.valid).toBeFalsy();
		expect(component.pieceQuantity.hasError('pattern')).toBeTruthy();

		component.pieceQuantity.setValue(0); // Not positive
		expect(component.pieceQuantity.valid).toBeFalsy();
		expect(component.pieceQuantity.hasError('pattern')).toBeTruthy();

		component.pieceQuantity.setValue(-1); // Negative
		expect(component.pieceQuantity.valid).toBeFalsy();
		expect(component.pieceQuantity.hasError('pattern')).toBeTruthy();

		component.pieceQuantity.setValue(1.5); // Decimal
		expect(component.pieceQuantity.valid).toBeFalsy();
		expect(component.pieceQuantity.hasError('pattern')).toBeTruthy();

		// Valid values
		component.pieceQuantity.setValue(1);
		expect(component.pieceQuantity.valid).toBeTruthy();

		component.pieceQuantity.setValue(10);
		expect(component.pieceQuantity.valid).toBeTruthy();
	});

	it('should show error message when pieceQuantity is invalid', () => {
		component.pieceType = 'general';

		fixture.detectChanges();

		// Set invalid value
		component.pieceQuantity.setValue(null);
		component.pieceQuantity.markAsTouched();
		fixture.detectChanges();

		// Check for required error
		const requiredError = fixture.debugElement.query(By.css('mat-error'));
		expect(requiredError).toBeTruthy();

		// Set invalid pattern
		component.pieceQuantity.setValue(0);
		fixture.detectChanges();

		// Check for pattern error
		const patternError = fixture.debugElement.query(By.css('mat-error'));
		expect(patternError).toBeTruthy();
	});

	it('should have cancel and done buttons', () => {
		component.pieceType = 'general';

		fixture.detectChanges();

		// Find buttons specifically in the footer section (not including child component buttons)
		const footerButtons = fixture.debugElement.queryAll(By.css('.orll-piece-add-page__buttons button'));
		expect(footerButtons.length).toBe(2);

		// Check cancel button
		const cancelButton = fixture.debugElement.query(By.css('.orll-piece-add-page__cancel-button'));
		expect(cancelButton).toBeTruthy();

		// Check done button
		const doneButton = fixture.debugElement.query(By.css('.orll-piece-add-page__buttons button[color="primary"]'));
		expect(doneButton).toBeTruthy();
	});

	it('should call onCancel when cancel button is clicked', () => {
		component.pieceType = 'general';

		fixture.detectChanges();

		// Spy on onCancel method
		spyOn(component, 'onCancel');

		// Find and click cancel button
		const cancelButton = fixture.debugElement.query(By.css('.orll-piece-add-page__cancel-button'));
		cancelButton.triggerEventHandler('click', null);

		// Check if onCancel was called
		expect(component.onCancel).toHaveBeenCalled();
	});

	describe('onDone', () => {
		it('should call onDone when done button is clicked', () => {
			component.pieceType = 'general';
			fixture.detectChanges();
			spyOn(component, 'onDone');
			const doneButton = fixture.debugElement.query(By.css('.orll-piece-add-page__done-button'));
			doneButton.triggerEventHandler('click', null);
			expect(component.onDone).toHaveBeenCalled();
		});

		it('should call addOrUpdatePiece when pieceType is general', () => {
			component.pieceType = 'general';
			spyOn<any>(component, 'addOrUpdatePiece');
			component.onDone();
			expect(component['addOrUpdatePiece']).toHaveBeenCalled();
		});

		it('should call addOrUpdateDgPiece when pieceType is dg', () => {
			component.pieceType = 'dg';
			spyOn<any>(component, 'addOrUpdateDgPiece');
			component.onDone();
			expect(component['addOrUpdateDgPiece']).toHaveBeenCalled();
		});

		it('should call addOrUpdateLiveAnimalPiece when pieceType is la', () => {
			component.pieceType = 'la';
			spyOn<any>(component, 'addOrUpdateLiveAnimalPiece');
			component.onDone();
			expect(component['addOrUpdateLiveAnimalPiece']).toHaveBeenCalled();
		});
	});

	describe('getPieceDetail', () => {
		it('should call sliCreateRequestService.getPieceDetail with the provided pieceId', () => {
			// Setup
			const pieceId = 'test-piece-id';
			const mockPiece: Piece = {
				pieceId,
				upid: 'TEST123',
				type: 'Piece',
				product: { description: 'Test Product' },
				packagingType: { typeCode: 'BOX', description: 'Box' },
				packagedIdentifier: 'ID123',
				nvdForCustoms: true,
				nvdForCarriage: true,
				grossWeight: 50,
				dimensions: { length: 20, width: 15, height: 10 },
				pieceQuantity: 1,
				slac: 0,
				shippingMarks: '',
				textualHandlingInstructions: '',
				containedPieces: [],
				containedItems: [],
			} as Piece;

			mockSliCreateRequestService.getPieceDetail.and.returnValue(of(mockPiece));

			// Execute
			component.getPieceDetail(pieceId);

			// Verify
			expect(mockSliCreateRequestService.getPieceDetail).toHaveBeenCalledWith(pieceId);
			expect(component.piece).toEqual(mockPiece);
			expect(component.pieceQuantity?.value).toEqual(mockPiece.pieceQuantity);
		});
	});

	describe('pieceDetailRequest', () => {
		it('should call createPiece when pieceId is not provided', () => {
			// Setup
			component.sliNumber = 'test-sli';
			const mockPiece: Piece = {
				upid: 'TEST123',
				type: 'Piece',
				product: { description: 'Test Product' },
				packagingType: { typeCode: 'BOX', description: 'Box' },
				packagedIdentifier: 'ID123',
				nvdForCustoms: true,
				nvdForCarriage: true,
				grossWeight: 50,
				dimensions: { length: 20, width: 15, height: 10 },
				pieceQuantity: 1,
				slac: 0,
				shippingMarks: '',
				textualHandlingInstructions: '',
				containedPieces: [],
				containedItems: [],
				sliNumber: component.sliNumber,
			} as Piece;

			mockSliCreateRequestService.createPiece.and.returnValue(of('piece-123'));

			// Execute
			let result: string | undefined;
			component['pieceDetailRequest'](mockPiece).subscribe((res) => {
				result = res;
			});

			// Verify
			expect(mockSliCreateRequestService.createPiece).toHaveBeenCalledWith(mockPiece);
			expect(mockSliCreateRequestService.updatePiece).not.toHaveBeenCalled();
			expect(result).toBe('piece-123');
		});

		it('should call createPiece when sliNumber equals pieceId', () => {
			// Setup
			component.sliNumber = 'test-sli';
			component.pieceId = 'test-sli';
			const mockPiece: Piece = {
				pieceId: component.pieceId,
				upid: 'TEST123',
				type: 'Piece',
				product: { description: 'Test Product' },
				packagingType: { typeCode: 'BOX', description: 'Box' },
				packagedIdentifier: 'ID123',
				nvdForCustoms: true,
				nvdForCarriage: true,
				grossWeight: 50,
				dimensions: { length: 20, width: 15, height: 10 },
				pieceQuantity: 1,
				slac: 0,
				shippingMarks: '',
				textualHandlingInstructions: '',
				containedPieces: [],
				containedItems: [],
				sliNumber: component.sliNumber,
			} as Piece;

			mockSliCreateRequestService.createPiece.and.returnValue(of('piece-123'));

			// Execute
			let result: string | undefined;
			component['pieceDetailRequest'](mockPiece).subscribe((res) => {
				result = res;
			});

			// Verify
			expect(mockSliCreateRequestService.createPiece).toHaveBeenCalledWith(mockPiece);
			expect(mockSliCreateRequestService.updatePiece).not.toHaveBeenCalled();
			expect(result).toBe('piece-123');
		});

		it('should call updatePiece when sliNumber does not equal pieceId', () => {
			// Setup
			component.sliNumber = 'test-sli';
			component.pieceId = 'test-piece';
			const mockPiece: Piece = {
				pieceId: component.pieceId,
				upid: 'TEST123',
				type: 'Piece',
				product: { description: 'Test Product' },
				packagingType: { typeCode: 'BOX', description: 'Box' },
				packagedIdentifier: 'ID123',
				nvdForCustoms: true,
				nvdForCarriage: true,
				grossWeight: 50,
				dimensions: { length: 20, width: 15, height: 10 },
				pieceQuantity: 1,
				slac: 0,
				shippingMarks: '',
				textualHandlingInstructions: '',
				containedPieces: [],
				containedItems: [],
				sliNumber: component.sliNumber,
			} as Piece;

			mockSliCreateRequestService.updatePiece.and.returnValue(of('piece-updated'));

			// Execute
			let result: string | undefined;
			component['pieceDetailRequest'](mockPiece).subscribe((res) => {
				result = res;
			});

			// Verify
			expect(mockSliCreateRequestService.updatePiece).toHaveBeenCalledWith(mockPiece, component.pieceId);
			expect(mockSliCreateRequestService.createPiece).not.toHaveBeenCalled();
			expect(result).toBe('piece-updated');
		});
	});

	describe('canDeactivate', () => {
		it('should return true when form is saved', () => {
			// Setup
			component.isSaved = true;

			// Execute & Verify
			expect(component.canDeactivate()).toBe(true);
			expect(mockDialog.open).not.toHaveBeenCalled();
		});

		it('should navigate and return true when dialog is confirmed', fakeAsync(() => {
			// Setup
			component.sliNumber = 'test-sli';
			component.isSaved = false;
			component.isConfirmed = false;
			mockDialog.open.and.returnValue({
				afterClosed: () => of(true),
			} as any);

			// Execute
			let result: boolean | null = null;
			(component.canDeactivate() as Observable<boolean>).subscribe((value) => {
				result = value;
			});
			tick();

			// Verify
			expect(component.isConfirmed).toBe(true);
			expect(mockRouter.navigate).toHaveBeenCalledWith(['sli/edit', 'test-sli']);
			expect(result).toBeTrue();
		}));

		it('should not navigate and return false when dialog is cancelled', fakeAsync(() => {
			// Setup
			component.isSaved = false;
			component.isConfirmed = false;
			mockDialog.open.and.returnValue({
				afterClosed: () => of(false),
			} as any);

			// Execute
			let result: boolean | null = null;
			(component.canDeactivate() as Observable<boolean>).subscribe((value) => {
				result = value;
			});
			tick();

			// Verify
			expect(component.isConfirmed).toBe(false);
			expect(mockRouter.navigate).not.toHaveBeenCalled();
			expect(result).toBeFalse();
		}));
	});

	describe('onCancel', () => {
		it('should open confirmation dialog when clicked', () => {
			// Setup
			mockDialog.open.and.returnValue({
				afterClosed: () =>
					new Observable(() => {
						// Never complete this observable to simulate dialog still being open
					}),
			} as any);

			// Execute
			component.onCancel();

			// Verify
			expect(mockDialog.open).toHaveBeenCalled();
			expect(mockRouter.navigate).not.toHaveBeenCalled();
		});

		it('should navigate when confirmation dialog returns true', () => {
			// Setup
			component.sliNumber = 'test-sli';
			mockDialog.open.and.returnValue({
				afterClosed: () => of(true),
			} as any);

			// Execute
			component.onCancel();

			// Verify
			expect(component.isConfirmed).toBe(true);
			expect(mockRouter.navigate).toHaveBeenCalledWith(['sli/edit', 'test-sli']);
		});

		it('should not navigate when confirmation dialog returns false', () => {
			// Setup
			mockDialog.open.and.returnValue({
				afterClosed: () => of(false),
			} as any);

			// Execute
			component.onCancel();

			// Verify
			expect(component.isConfirmed).toBe(false);
			expect(mockRouter.navigate).not.toHaveBeenCalled();
		});
	});

	describe('addOrUpdatePiece', () => {
		beforeEach(() => {
			// Setup mock components
			component.gelPiece = jasmine.createSpyObj('SliPieceFormComponent', ['getFormData'], {
				sliPieceForm: jasmine.createSpyObj('FormGroup', ['markAllAsTouched']),
			});
			component.pieceQuantity.markAsTouched();
			component.pieceItemWrapper = jasmine.createSpyObj('SliPieceItemComponent', ['getPieceItemList', 'getPieceInList']);

			component.sliNumber = 'test-sli';
			(component.gelPiece.getFormData as jasmine.Spy).and.returnValue({ upid: 'test-upid' });
			(component.pieceItemWrapper.getPieceItemList as jasmine.Spy).and.returnValue([]);
			(component.pieceItemWrapper.getPieceInList as jasmine.Spy).and.returnValue([]);
		});

		it('should mark forms as touched and validate inputs', () => {
			// Setup
			mockSliCreateRequestService.createPiece.and.returnValue(of('piece-123'));
			spyOn<any>(component, 'pieceDetailRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdatePiece']();

			// Verify
			expect(component.gelPiece.sliPieceForm?.markAllAsTouched).toHaveBeenCalled();
		});

		it('should show dialog if form validation fails', () => {
			// Setup
			component.sliNumber = '';

			// Execute
			component['addOrUpdatePiece']();

			// Verify
			expect(mockDialog.open).toHaveBeenCalled();
			expect(mockSliCreateRequestService.createPiece).not.toHaveBeenCalled();
		});

		it('should call pieceDetailRequest with correct payload', () => {
			// Setup
			const expectedPayload = {
				sliNumber: 'test-sli',
				upid: 'test-upid',
				slac: 0,
				pieceQuantity: 1,
				containedItems: [],
				containedPieces: [],
			};
			component.pieceQuantity.setValue(1);

			spyOn<any>(component, 'pieceDetailRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdatePiece']();

			// Verify
			expect(component['pieceDetailRequest']).toHaveBeenCalledWith(jasmine.objectContaining(expectedPayload));
		});

		it('should set dataLoading to true while processing', () => {
			// Setup
			spyOn<any>(component, 'pieceDetailRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdatePiece']();

			// Verify
			expect(component.dataLoading).toBe(false); // Should be reset after observable completes
		});

		it('should navigate after successful creation/update', () => {
			// Setup
			spyOn<any>(component, 'pieceDetailRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdatePiece']();

			// Verify
			expect(component.isSaved).toBe(true);
			expect(mockRouter.navigate).toHaveBeenCalledWith(['sli/edit', 'test-sli']);
		});

		it('should handle errors and reset dataLoading', () => {
			// Setup
			spyOn<any>(component, 'pieceDetailRequest').and.returnValue(throwError(() => new Error('API error')));

			// Execute
			component['addOrUpdatePiece']();

			// Verify
			expect(component.dataLoading).toBe(false);
			expect(component.isSaved).toBe(false);
		});
	});

	describe('addOrUpdateLiveAnimalPiece', () => {
		let mockLiveAnimalForm: jasmine.SpyObj<any>;
		let mockFormValue: any;

		beforeEach(() => {
			// Setup test data
			component.pieceType = 'la';
			component.sliNumber = 'test-sli-123';
			component.pieceQuantity.setValue(2);

			// Create comprehensive mock form value
			mockFormValue = {
				productDescription: 'Live Animal - Dogs',
				typeOfPackage: { typeCode: '4A', description: 'Box, steel' },
				packagedIdentifier: 'LA-ID-123',
				speciesCommonName: 'Dog',
				speciesScientificName: 'Canis lupus familiaris',
				specimenDescription: 'Friendly domestic dogs',
				animalQuantity: 2,
				shippingMarks: 'Handle with care - Live animals',
				upid: 'LA-UPID-456',
				grossWeight: '25.5',
				dimensions: { length: 100, width: 50, height: 60 },
				whetherHaveDeclaredValueForCustoms: true,
				whetherHaveDeclaredValueForCarriage: false,
				textualHandlingInstructions: 'Keep cool and provide water',
			};

			// Create mock form with Jasmine spies
			mockLiveAnimalForm = jasmine.createSpyObj('FormGroup', ['markAllAsTouched'], {
				invalid: false,
				valid: true,
				value: mockFormValue,
			});

			// Create mock component with form
			component.liveAnimalPieceComponent = jasmine.createSpyObj('LiveAnimalPieceAddComponent', [], {
				sliLiveAnimalPieceForm: mockLiveAnimalForm,
			});
		});

		describe('Form Validation', () => {
			it('should mark all form fields as touched before validation', () => {
				// Arrange
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				expect(mockLiveAnimalForm.markAllAsTouched).toHaveBeenCalled();
			});

			it('should validate piece quantity form control', () => {
				// Arrange
				component.pieceQuantity.setValue(null);
				component.pieceQuantity.markAsTouched();
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert - Should still process if LA form is valid
				expect(component.pieceQuantity.touched).toBe(true);
			});

			it('should show validation dialog when live animal form is invalid', () => {
				// Arrange
				Object.defineProperty(mockLiveAnimalForm, 'invalid', {
					get: jasmine.createSpy('invalid').and.returnValue(true),
					configurable: true,
				});

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				expect(mockDialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
					width: '300px',
					data: { content: 'common.dialog.form.validate' },
				});
				expect(mockSliCreateRequestService.createLaPiece).not.toHaveBeenCalled();
				expect(mockSliCreateRequestService.updateLaPiece).not.toHaveBeenCalled();
			});

			it('should proceed even when piece quantity is invalid (no validation in LA method)', () => {
				// Arrange
				component.pieceQuantity.setValue(-1); // Invalid value
				component.pieceQuantity.markAsTouched();
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert - LA method doesn't validate pieceQuantity, so it should proceed
				expect(component['pieceDetailLaRequest']).toHaveBeenCalled();
			});

			it('should proceed even when sliNumber is missing (no validation in LA method)', () => {
				// Arrange
				component.sliNumber = '';
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert - LA method doesn't validate sliNumber, so it should proceed
				expect(component['pieceDetailLaRequest']).toHaveBeenCalled();
			});
		});

		describe('Successful Processing', () => {
			beforeEach(() => {
				// Ensure all validations pass
				component.sliNumber = 'test-sli-123';
				component.pieceQuantity.setValue(2);
				Object.defineProperty(mockLiveAnimalForm, 'invalid', {
					get: jasmine.createSpy('invalid').and.returnValue(false),
					configurable: true,
				});
			});

			it('should call pieceDetailLaRequest with correctly formatted payload', () => {
				// Arrange
				const expectedPayload = jasmine.objectContaining({
					type: 'PieceLiveAnimals',
					sliNumber: 'test-sli-123',
					pieceQuantity: 2,
					slac: 0,
					packagingType: { typeCode: '4A', description: 'Box, steel' },
					packagedIdentifier: 'LA-ID-123',
					speciesCommonName: 'Dog',
					speciesScientificName: 'Canis lupus familiaris',
					specimenDescription: 'Friendly domestic dogs',
					quantityAnimals: 2, // Note: this is quantityAnimals, not animalQuantity
					shippingMarks: 'Handle with care - Live animals',
					upid: 'LA-UPID-456',
					grossWeight: 25.5, // Should be converted to number
					dimensions: { length: 100, width: 50, height: 60 },
					nvdForCustoms: true,
					nvdForCarriage: false,
					textualHandlingInstructions: 'Keep cool and provide water',
					product: { description: 'Live Animal - Dogs', hsCommodityDescription: '' },
				});

				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				expect(component['pieceDetailLaRequest']).toHaveBeenCalledWith(expectedPayload);
			});

			it('should set dataLoading to true during processing', () => {
				// Arrange
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));
				component.dataLoading = false;

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert - dataLoading should be reset to false after completion
				expect(component.dataLoading).toBe(false);
			});

			it('should set isSaved to true and navigate on successful save', () => {
				// Arrange
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));
				component.isSaved = false;

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				expect(component.isSaved).toBe(true);
				expect(mockRouter.navigate).toHaveBeenCalledWith(['sli/edit', 'test-sli-123']);
			});

			it('should handle successful response with piece ID', () => {
				// Arrange
				const mockPieceId = 'new-la-piece-456';
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of(mockPieceId));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				expect(component.isSaved).toBe(true);
				expect(mockRouter.navigate).toHaveBeenCalledWith(['sli/edit', component.sliNumber]);
			});
		});

		describe('Error Handling', () => {
			beforeEach(() => {
				// Ensure validations pass to reach error handling
				component.sliNumber = 'test-sli-123';
				component.pieceQuantity.setValue(2);
				Object.defineProperty(mockLiveAnimalForm, 'invalid', {
					get: jasmine.createSpy('invalid').and.returnValue(false),
					configurable: true,
				});
			});

			it('should handle API errors gracefully', () => {
				// Arrange
				const mockError = new Error('API Error: Unable to save live animal piece');
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(throwError(() => mockError));
				component.dataLoading = true;
				component.isSaved = false; // Start with false to test error handling

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				expect(component.dataLoading).toBe(false);
				expect(component.isSaved).toBe(false);
				expect(mockRouter.navigate).not.toHaveBeenCalled();
			});

			it('should handle network timeout errors', () => {
				// Arrange
				const timeoutError = new Error('Timeout: Request took too long');
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(throwError(() => timeoutError));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				expect(component.dataLoading).toBe(false);
				expect(component.isSaved).toBe(false);
			});

			it('should handle validation errors from server', () => {
				// Arrange
				const validationError = {
					error: { message: 'Invalid animal species data' },
					status: 400,
				};
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(throwError(() => validationError));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				expect(component.dataLoading).toBe(false);
				expect(component.isSaved).toBe(false);
			});
		});

		describe('Data Transformation', () => {
			it('should correctly transform grossWeight from string to number', () => {
				// Arrange
				mockFormValue.grossWeight = '45.75';
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				const callArgs = (component['pieceDetailLaRequest'] as jasmine.Spy).calls.mostRecent().args[0];
				expect(callArgs.grossWeight).toBe(45.75);
				expect(typeof callArgs.grossWeight).toBe('number');
			});

			it('should handle zero grossWeight correctly', () => {
				// Arrange
				mockFormValue.grossWeight = '0';
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				const callArgs = (component['pieceDetailLaRequest'] as jasmine.Spy).calls.mostRecent().args[0];
				expect(callArgs.grossWeight).toBe(0);
			});

			it('should map nvdForCustoms and nvdForCarriage correctly', () => {
				// Arrange
				mockFormValue.whetherHaveDeclaredValueForCustoms = false;
				mockFormValue.whetherHaveDeclaredValueForCarriage = true;
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				const callArgs = (component['pieceDetailLaRequest'] as jasmine.Spy).calls.mostRecent().args[0];
				expect(callArgs.nvdForCustoms).toBe(false);
				expect(callArgs.nvdForCarriage).toBe(true);
			});

			it('should preserve all live animal specific fields', () => {
				// Arrange
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				const callArgs = (component['pieceDetailLaRequest'] as jasmine.Spy).calls.mostRecent().args[0];
				expect(callArgs.speciesCommonName).toBe('Dog');
				expect(callArgs.speciesScientificName).toBe('Canis lupus familiaris');
				expect(callArgs.specimenDescription).toBe('Friendly domestic dogs');
				expect(callArgs.quantityAnimals).toBe(2); // Note: quantityAnimals, not animalQuantity
			});
		});

		describe('Integration with pieceDetailLaRequest', () => {
			it('should pass through the complete payload to pieceDetailLaRequest', () => {
				// Arrange
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				expect(component['pieceDetailLaRequest']).toHaveBeenCalledTimes(1);
				const payload = (component['pieceDetailLaRequest'] as jasmine.Spy).calls.mostRecent().args[0];

				// Verify essential fields are present
				expect(payload.sliNumber).toBeDefined();
				expect(payload.pieceQuantity).toBeDefined();
				expect(payload.speciesCommonName).toBeDefined();
				expect(payload.speciesScientificName).toBeDefined();
				expect(payload.quantityAnimals).toBeDefined(); // Note: quantityAnimals, not animalQuantity
			});

			it('should handle pieceDetailLaRequest returning empty response', () => {
				// Arrange
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of(''));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				expect(component.isSaved).toBe(true);
				expect(mockRouter.navigate).toHaveBeenCalled();
			});

			it('should handle pieceDetailLaRequest returning null', () => {
				// Arrange
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of(null));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				expect(component.isSaved).toBe(true);
				expect(mockRouter.navigate).toHaveBeenCalled();
			});
		});

		describe('Component State Management', () => {
			it('should reset dataLoading state after successful operation', () => {
				// Arrange
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));
				component.dataLoading = true;

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				expect(component.dataLoading).toBe(false);
			});

			it('should reset dataLoading state after failed operation', () => {
				// Arrange
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(throwError(() => new Error('Test error')));
				component.dataLoading = true;

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert
				expect(component.dataLoading).toBe(false);
			});

			it('should proceed with empty sliNumber (no validation in LA method)', () => {
				// Arrange
				component.sliNumber = ''; // Empty but not validated in LA method
				component.isSaved = false;
				spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('la-piece-123'));

				// Act
				component['addOrUpdateLiveAnimalPiece']();

				// Assert - LA method doesn't validate sliNumber, so it should proceed
				expect(component.isSaved).toBe(true);
				expect(component['pieceDetailLaRequest']).toHaveBeenCalled();
			});
		});
	});

	describe('pieceDetailLaRequest', () => {
		it('should call createLaPiece when pieceId is not provided', () => {
			component.sliNumber = 'test-sli';
			const mockLaPiece = { upid: 'la-123' } as Piece;
			mockSliCreateRequestService.createLaPiece.and.returnValue(of('la-piece-created'));

			let result: string | undefined;
			component['pieceDetailLaRequest'](mockLaPiece).subscribe((res) => {
				result = res;
			});

			expect(mockSliCreateRequestService.createLaPiece).toHaveBeenCalledWith(mockLaPiece);
			expect(mockSliCreateRequestService.updateLaPiece).not.toHaveBeenCalled();
			expect(result).toBe('la-piece-created');
		});

		it('should call updateLaPiece when pieceId is provided', () => {
			component.sliNumber = 'test-sli';
			component.pieceId = 'la-piece-id';
			const mockLaPiece = { upid: 'la-123' } as Piece;
			mockSliCreateRequestService.updateLaPiece.and.returnValue(of('la-piece-updated'));

			let result: string | undefined;
			component['pieceDetailLaRequest'](mockLaPiece).subscribe((res) => {
				result = res;
			});

			expect(mockSliCreateRequestService.updateLaPiece).toHaveBeenCalledWith(mockLaPiece, component.pieceId);
			expect(mockSliCreateRequestService.createLaPiece).not.toHaveBeenCalled();
			expect(result).toBe('la-piece-updated');
		});
	});

	describe('addOrUpdateDgPiece', () => {
		beforeEach(() => {
			// Setup mock DG component
			component.pieceType = 'dg';
			component.sliNumber = 'test-sli';
			component.dgPieceComponent = jasmine.createSpyObj('DgPieceAddComponent', [], {
				sliDgPieceForm: jasmine.createSpyObj('FormGroup', ['markAllAsTouched'], {
					invalid: false,
					value: {
						productDescription: 'test productDescription',
						typeOfPackage: {
							typeCode: '1A',
							description: 'Drum, steel',
						},
						packagedIdentifier: 'test packagedIdentifier',
						whetherHaveDeclaredValueForCustoms: true,
						whetherHaveDeclaredValueForCarriage: true,
						specialProvisionId: 'test specialProvisionId',
						explosiveCompatibilityGroupCode: 'test explosiveCompatibilityGroupCode',
						packagingDangerLevelCode: 'test packagingDangerLevelCode',
						technicalName: 'test technicalName',
						unNumber: 'test unNumber',
						shippersDeclaration: 'test shippersDeclaration',
						handlingInformation: 'test handlingInformation',
						allPackedInOne: true,
						qValueNumeric: 111,
						upid: 'test upid',
						shippingMarks: 'test shippingMarks',
						grossWeight: '111.22',
						dimensions: {
							length: 0,
							width: 0,
							height: 0,
						},
						hsCommodityDescription: 'test hsCommodityDescription',
						properShippingName: 'test properShippingName',
						textualHandlingInstructions: 'test textualHandlingInstructions',
						hazardClassificationId: 'test hazardClassificationId',
						additionalHazardClassificationId: 'test additionalHazardClassificationId',
						packingInstructionNumber: 'test packingInstructionNumber',
						complianceDeclaration: 'test complianceDeclaration',
						exclusiveUseIndicator: 'test exclusiveUseIndicator',
						authorizationInformation: 'test authorizationInformation',
						aircraftLimitationInformation: 'test aircraftLimitationInformation',
					},
				}),
				dgPieceItems: [],
			});
		});

		it('should validate the DG form before submission', () => {
			// Override the method to directly test notification service call
			spyOn<any>(component, 'addOrUpdateDgPiece').and.callThrough();

			// Setup form invalid
			Object.defineProperty(component.dgPieceComponent!.sliDgPieceForm, 'invalid', { get: () => true });

			// Reset notification service spy
			mockNotificationService.showError.calls.reset();

			// Call the method directly
			component['addOrUpdateDgPiece']();

			// Verify
			expect(component.dgPieceComponent!.sliDgPieceForm.markAllAsTouched).toHaveBeenCalled();
		});

		it('should call pieceDetailDgRequest with correct payload for DG piece', () => {
			// Setup
			const expectedPayload = jasmine.objectContaining({
				type: 'PieceDg',
				sliNumber: 'test-sli',
				upid: 'test upid',
				slac: 0,
				pieceQuantity: 1,
				packagingType: { typeCode: '1A', description: 'Drum, steel' },
				nvdForCustoms: true,
				nvdForCarriage: true,
				grossWeight: 111.22,
			});
			component.pieceQuantity.setValue(1);

			spyOn<any>(component, 'pieceDetailDgRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdateDgPiece']();

			// Verify
			expect(component['pieceDetailDgRequest']).toHaveBeenCalledWith(expectedPayload);
		});

		it('should set dataLoading to true while processing', () => {
			// Setup
			spyOn<any>(component, 'pieceDetailDgRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdateDgPiece']();

			// Verify
			expect(component.dataLoading).toBe(false); // Should be reset after observable completes
		});

		it('should navigate after successful creation/update', () => {
			// Setup
			spyOn<any>(component, 'pieceDetailDgRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdateDgPiece']();

			// Verify
			expect(component.isSaved).toBe(true);
			expect(mockRouter.navigate).toHaveBeenCalledWith(['sli/edit', 'test-sli']);
		});

		it('should handle errors and reset dataLoading', () => {
			// Setup
			spyOn<any>(component, 'pieceDetailDgRequest').and.returnValue(throwError(() => new Error('API error')));

			// Execute
			component['addOrUpdateDgPiece']();

			// Verify
			expect(component.dataLoading).toBe(false);
			expect(component.isSaved).toBe(false);
		});
	});

	describe('addLiveAnimalPiece', () => {
		beforeEach(() => {
			// Setup mock Live Animal component
			component.pieceType = 'la';
			component.sliNumber = 'test-sli';

			component.liveAnimalPieceComponent = jasmine.createSpyObj('LiveAnimalPieceAddComponent', [], {
				sliLiveAnimalPieceForm: jasmine.createSpyObj('FormGroup', ['markAllAsTouched'], {
					invalid: false,
					value: {
						productDescription: 'test productDescription',
						typeOfPackage: {
							typeCode: '1A',
							description: 'Drum, steel',
						},
						packagedIdentifier: 'test packagedIdentifier',
						speciesCommonName: 'test speciesCommonName',
						speciesScientificName: 'test speciesScientificName',
						specimenDescription: 'test specimenDescription',
						animalQuantity: 1,
						shippingMarks: 'test shippingMarks',
						upid: 'test upid',
						grossWeight: 0,
						dimensions: {
							length: 0,
							width: 0,
							height: 0,
						},
						whetherHaveDeclaredValueForCustoms: false,
						whetherHaveDeclaredValueForCarriage: false,
						textualHandlingInstructions: 'test textualHandlingInstructions',
					},
				}),
			});
		});

		it('should validate the Live Animal form before submission', () => {
			// Override the method to directly test notification service call
			spyOn<any>(component, 'addOrUpdateLiveAnimalPiece').and.callThrough();

			// Setup form invalid
			Object.defineProperty(component.liveAnimalPieceComponent!.sliLiveAnimalPieceForm, 'invalid', { get: () => true });

			// Reset notification service spy
			mockNotificationService.showError.calls.reset();

			// Call the method directly
			component['addOrUpdateLiveAnimalPiece']();

			// Verify
			expect(component.liveAnimalPieceComponent!.sliLiveAnimalPieceForm.markAllAsTouched).toHaveBeenCalled();
		});

		it('should call pieceDetailLaRequest with correct payload for LA piece', () => {
			// Setup
			const expectedPayload = jasmine.objectContaining({
				type: 'PieceLiveAnimals',
				sliNumber: 'test-sli',
				upid: 'test upid',
				slac: 0,
				pieceQuantity: 1,
				packagingType: { typeCode: '1A', description: 'Drum, steel' },
				nvdForCustoms: false,
				nvdForCarriage: false,
				grossWeight: 0,
				speciesCommonName: 'test speciesCommonName',
				speciesScientificName: 'test speciesScientificName',
				quantityAnimals: 1,
			});
			component.pieceQuantity.setValue(1);

			spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdateLiveAnimalPiece']();

			// Verify
			expect(component['pieceDetailLaRequest']).toHaveBeenCalledWith(expectedPayload);
		});

		it('should set dataLoading to true while processing', () => {
			// Setup
			spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdateLiveAnimalPiece']();

			// Verify
			expect(component.dataLoading).toBe(false); // Should be reset after observable completes
		});

		it('should navigate after successful creation/update', () => {
			// Setup
			spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdateLiveAnimalPiece']();

			// Verify
			expect(component.isSaved).toBe(true);
			expect(mockRouter.navigate).toHaveBeenCalledWith(['sli/edit', 'test-sli']);
		});

		it('should handle errors and reset dataLoading', () => {
			// Setup
			spyOn<any>(component, 'pieceDetailLaRequest').and.returnValue(throwError(() => new Error('API error')));

			// Execute
			component['addOrUpdateLiveAnimalPiece']();

			// Verify
			expect(component.dataLoading).toBe(false);
			expect(component.isSaved).toBe(false);
		});
	});
});
