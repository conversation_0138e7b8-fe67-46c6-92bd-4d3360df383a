import { TestBed } from '@angular/core/testing';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { OrgMgmtRequestService } from './org-mgmt-request.service';
import { Organization } from '../models/organization.model';
import { OrgInfo } from '../models/org-info.model';
import { Response } from '@shared/models/response.model';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { environment } from '@environments/environment';

const baseUrl = environment.baseApi;

describe('OrgMgmtRequestService', () => {
	let service: OrgMgmtRequestService;
	let httpMock: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			imports: [],
			providers: [
				OrgMgmtRequestService,
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		});

		service = TestBed.inject(OrgMgmtRequestService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify(); // Verify that no outstanding requests remain
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	describe('getOrgList', () => {
		it('should return organizations when no orgType is provided', () => {
			const mockOrgs: Organization[] = [
				{ id: '1', name: 'Org 1', orgType: 'TYPE_A' },
				{ id: '2', name: 'Org 2', orgType: 'TYPE_B' },
			];

			const mockResponse: Response<Organization> = {
				data: mockOrgs,
				code: 200,
				msg: 'Success',
			};

			service.getOrgList().subscribe((orgs) => {
				expect(orgs).toEqual(mockOrgs);
			});

			const req = httpMock.expectOne(`${baseUrl}/org/org/list?orgType=`);
			expect(req.request.method).toBe('GET');
			req.flush(mockResponse);
		});

		it('should return filtered organizations when orgType is provided', () => {
			const orgType = 'AIRLINE';
			const mockOrgs: Organization[] = [
				{ id: '1', name: 'Airline 1', orgType: 'AIRLINE' },
				{ id: '2', name: 'Airline 2', orgType: 'AIRLINE' },
			];

			const mockResponse: Response<Organization> = {
				data: mockOrgs,
				code: 200,
				msg: 'Success',
			};

			service.getOrgList(orgType).subscribe((orgs) => {
				expect(orgs).toEqual(mockOrgs);
			});

			const req = httpMock.expectOne(`${baseUrl}/org/org/list?orgType=${orgType}`);
			expect(req.request.method).toBe('GET');
			req.flush(mockResponse);
		});
	});

	describe('getOrgInfo', () => {
		it('should return organization info for a given orgId', () => {
			const orgId = '123';
			const mockOrgInfo: OrgInfo = {
				id: '123',
				companyName: 'Test Company',
				partyRole: 'SHIPPER',
				countryCode: 'US',
				locationName: 'New York',
				regionCode: 'NY',
				textualPostCode: '10001',
				cityCode: 'NYC',
				persons: [],
			};

			const mockResponse = {
				code: 200,
				data: mockOrgInfo,
			};

			service.getOrgInfo(orgId).subscribe((info) => {
				expect(info).toEqual(mockOrgInfo);
			});

			const req = httpMock.expectOne(`${baseUrl}/org/org?orgId=${orgId}`);
			expect(req.request.method).toBe('GET');
			req.flush(mockResponse);
		});
	});
});
